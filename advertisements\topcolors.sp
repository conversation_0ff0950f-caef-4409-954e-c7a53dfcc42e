StringMap g_hTopColors;

void AddTopColors()
{
    if (!g_hTopColors) {
        g_hTopColors = new StringMap();
    }

    AddTopColor("aliceblue", "F0F8FF");
    AddTopColor("allies", "4D7942");
    AddTopColor("ancient", "EB4B4B");
    AddTopColor("antiquewhite", "FAEBD7");
    AddTopColor("aqua", "00FFFF");
    AddTopColor("aquamarine", "7FFFD4");
    AddTopColor("arcana", "ADE55C");
    AddTopColor("axis", "FF4040");
    AddTopColor("azure", "007FFF");
    AddTopColor("beige", "F5F5DC");
    AddTopColor("bisque", "FFE4C4");
    AddTopColor("black", "000000");
    AddTopColor("blanchedalmond", "FFEBCD");
    AddTopColor("blue", "99CCFF");
    AddTopColor("blueviolet", "8A2BE2");
    AddTopColor("brown", "A52A2A");
    AddTopColor("burlywood", "DEB887");
    AddTopColor("cadetblue", "5F9EA0");
    AddTopColor("chartreuse", "7FFF00");
    AddTopColor("chocolate", "D2691E");
    AddTopColor("collectors", "AA0000");
    AddTopColor("common", "B0C3D9");
    AddTopColor("community", "70B04A");
    AddTopColor("coral", "FF7F50");
    AddTopColor("cornflowerblue", "6495ED");
    AddTopColor("cornsilk", "FFF8DC");
    AddTopColor("corrupted", "A32C2E");
    AddTopColor("crimson", "DC143C");
    AddTopColor("cyan", "00FFFF");
    AddTopColor("darkblue", "00008B");
    AddTopColor("darkcyan", "008B8B");
    AddTopColor("darkgoldenrod", "B8860B");
    AddTopColor("darkgray", "A9A9A9");
    AddTopColor("darkgrey", "A9A9A9");
    AddTopColor("darkgreen", "006400");
    AddTopColor("darkkhaki", "BDB76B");
    AddTopColor("darkmagenta", "8B008B");
    AddTopColor("darkolivegreen", "556B2F");
    AddTopColor("darkorange", "FF8C00");
    AddTopColor("darkorchid", "9932CC");
    AddTopColor("darkred", "8B0000");
    AddTopColor("darksalmon", "E9967A");
    AddTopColor("darkseagreen", "8FBC8F");
    AddTopColor("darkslateblue", "483D8B");
    AddTopColor("darkslategray", "2F4F4F");
    AddTopColor("darkslategrey", "2F4F4F");
    AddTopColor("darkturquoise", "00CED1");
    AddTopColor("darkviolet", "9400D3");
    AddTopColor("deeppink", "FF1493");
    AddTopColor("deepskyblue", "00BFFF");
    AddTopColor("dimgray", "696969");
    AddTopColor("dimgrey", "696969");
    AddTopColor("dodgerblue", "1E90FF");
    AddTopColor("exalted", "CCCCCD");
    AddTopColor("firebrick", "B22222");
    AddTopColor("floralwhite", "FFFAF0");
    AddTopColor("forestgreen", "228B22");
    AddTopColor("frozen", "4983B3");
    AddTopColor("fuchsia", "FF00FF");
    AddTopColor("fullblue", "0000FF");
    AddTopColor("fullred", "FF0000");
    AddTopColor("gainsboro", "DCDCDC");
    AddTopColor("genuine", "4D7455");
    AddTopColor("ghostwhite", "F8F8FF");
    AddTopColor("gold", "FFD700");
    AddTopColor("goldenrod", "DAA520");
    AddTopColor("gray", "CCCCCC");
    AddTopColor("grey", "CCCCCC");
    AddTopColor("green", "3EFF3E");
    AddTopColor("greenyellow", "ADFF2F");
    AddTopColor("haunted", "38F3AB");
    AddTopColor("honeydew", "F0FFF0");
    AddTopColor("hotpink", "FF69B4");
    AddTopColor("immortal", "E4AE33");
    AddTopColor("indianred", "CD5C5C");
    AddTopColor("indigo", "4B0082");
    AddTopColor("ivory", "FFFFF0");
    AddTopColor("khaki", "F0E68C");
    AddTopColor("lavender", "E6E6FA");
    AddTopColor("lavenderblush", "FFF0F5");
    AddTopColor("lawngreen", "7CFC00");
    AddTopColor("legendary", "D32CE6");
    AddTopColor("lemonchiffon", "FFFACD");
    AddTopColor("lightblue", "ADD8E6");
    AddTopColor("lightcoral", "F08080");
    AddTopColor("lightcyan", "E0FFFF");
    AddTopColor("lightgoldenrodyellow", "FAFAD2");
    AddTopColor("lightgray", "D3D3D3");
    AddTopColor("lightgrey", "D3D3D3");
    AddTopColor("lightgreen", "99FF99");
    AddTopColor("lightpink", "FFB6C1");
    AddTopColor("lightsalmon", "FFA07A");
    AddTopColor("lightseagreen", "20B2AA");
    AddTopColor("lightskyblue", "87CEFA");
    AddTopColor("lightslategray", "778899");
    AddTopColor("lightslategrey", "778899");
    AddTopColor("lightsteelblue", "B0C4DE");
    AddTopColor("lightyellow", "FFFFE0");
    AddTopColor("lime", "00FF00");
    AddTopColor("limegreen", "32CD32");
    AddTopColor("linen", "FAF0E6");
    AddTopColor("magenta", "FF00FF");
    AddTopColor("maroon", "800000");
    AddTopColor("mediumaquamarine", "66CDAA");
    AddTopColor("mediumblue", "0000CD");
    AddTopColor("mediumorchid", "BA55D3");
    AddTopColor("mediumpurple", "9370D8");
    AddTopColor("mediumseagreen", "3CB371");
    AddTopColor("mediumslateblue", "7B68EE");
    AddTopColor("mediumspringgreen", "00FA9A");
    AddTopColor("mediumturquoise", "48D1CC");
    AddTopColor("mediumvioletred", "C71585");
    AddTopColor("midnightblue", "191970");
    AddTopColor("mintcream", "F5FFFA");
    AddTopColor("mistyrose", "FFE4E1");
    AddTopColor("moccasin", "FFE4B5");
    AddTopColor("mythical", "8847FF");
    AddTopColor("navajowhite", "FFDEAD");
    AddTopColor("navy", "000080");
    AddTopColor("normal", "B2B2B2");
    AddTopColor("oldlace", "FDF5E6");
    AddTopColor("olive", "9EC34F");
    AddTopColor("olivedrab", "6B8E23");
    AddTopColor("orange", "FFA500");
    AddTopColor("orangered", "FF4500");
    AddTopColor("orchid", "DA70D6");
    AddTopColor("palegoldenrod", "EEE8AA");
    AddTopColor("palegreen", "98FB98");
    AddTopColor("paleturquoise", "AFEEEE");
    AddTopColor("palevioletred", "D87093");
    AddTopColor("papayawhip", "FFEFD5");
    AddTopColor("peachpuff", "FFDAB9");
    AddTopColor("peru", "CD853F");
    AddTopColor("pink", "FFC0CB");
    AddTopColor("plum", "DDA0DD");
    AddTopColor("powderblue", "B0E0E6");
    AddTopColor("purple", "800080");
    AddTopColor("rare", "4B69FF");
    AddTopColor("red", "FF4040");
    AddTopColor("rosybrown", "BC8F8F");
    AddTopColor("royalblue", "4169E1");
    AddTopColor("saddlebrown", "8B4513");
    AddTopColor("salmon", "FA8072");
    AddTopColor("sandybrown", "F4A460");
    AddTopColor("seagreen", "2E8B57");
    AddTopColor("seashell", "FFF5EE");
    AddTopColor("selfmade", "70B04A");
    AddTopColor("sienna", "A0522D");
    AddTopColor("silver", "C0C0C0");
    AddTopColor("skyblue", "87CEEB");
    AddTopColor("slateblue", "6A5ACD");
    AddTopColor("slategray", "708090");
    AddTopColor("slategrey", "708090");
    AddTopColor("snow", "FFFAFA");
    AddTopColor("springgreen", "00FF7F");
    AddTopColor("steelblue", "4682B4");
    AddTopColor("strange", "CF6A32");
    AddTopColor("tan", "D2B48C");
    AddTopColor("teal", "008080");
    AddTopColor("thistle", "D8BFD8");
    AddTopColor("tomato", "FF6347");
    AddTopColor("turquoise", "40E0D0");
    AddTopColor("uncommon", "B0C3D9");
    AddTopColor("unique", "FFD700");
    AddTopColor("unusual", "8650AC");
    AddTopColor("valve", "A50F79");
    AddTopColor("vintage", "476291");
    AddTopColor("violet", "EE82EE");
    AddTopColor("wheat", "F5DEB3");
    AddTopColor("white", "FFFFFF");
    AddTopColor("whitesmoke", "F5F5F5");
    AddTopColor("yellow", "FFFF00");
    AddTopColor("yellowgreen", "9ACD32");
}

void AddTopColor(const char[] sName, const char[] sColor)
{
    int aColor[4];
    ParseColor(sColor, aColor);

    g_hTopColors.SetArray(sName, aColor, sizeof(aColor));
}

void ParseColor(const char[] sColor, int aColor[4])
{
    int iColor = StringToInt(sColor, 16);
    aColor[0]  = iColor >> 16;
    aColor[1]  = iColor >> 8 & 255;
    aColor[2]  = iColor & 255;
    aColor[3]  = 255;
}

void ParseTopColor(const char[] sText, int &iStart, int aColor[4])
{
    int iEnd = StrContains(sText, "}");
    if (sText[0] != '{' || iEnd == -1) {
        return;
    }

    char sColor[32];
    strcopy(sColor, iEnd, sText[1]);
    if (sColor[0] == '#') {
        ParseColor(sColor[1], aColor);
    } else {
        g_hTopColors.GetArray(sColor, aColor, sizeof(aColor));
    }
    iStart = iEnd + 1;
}
