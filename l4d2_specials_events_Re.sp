#pragma semicolon 1
#pragma newdecls required

#include <sourcemod>
#include <sdktools>
#include <sdkhooks>
#include <left4dhooks>

// 全局变量 - 事件状态
bool g_RapidJockey = false;
bool g_RealMode = false;
bool g_ZEPZ = false;
bool g_MYZZ = false;
bool g_HunterHeal = false;
bool g_FireInfected = false;
bool g_StealthSmoker = false;
bool g_SpitterControl = false;
bool g_TankControl = false;
bool g_WitchRbirth = false;

// 全局变量 - 定时器
Handle g_hTimerLeftStart = null;
Handle g_hTimerSpecialEvents = null;

// 全局变量 - ConVars
ConVar g_cvEventMinInterval;
ConVar g_cvEventMaxInterval;

public Plugin myinfo = {
    name = "L4D2 Special Events Re",
    description = "Special Infected Events Modification",
    author = "R.T",
    version = "0.0.1alpha",
    url = "null"
}

public void OnPluginStart()
{
    // 创建配置文件
    CreateConVars();

    // 注册事件
    HookEvent("jockey_ride", Event_JockeyRide);
    HookEvent("round_start", Event_RoundStart);
    HookEvent("player_death", Event_PlayerDeath);
    HookEvent("mission_lost", Event_MissionLost);
    HookEvent("player_shoved", Event_PlayerShoved);
    HookEvent("player_hurt", Event_PlayerHurt);

    // 注册测试命令
    RegAdminCmd("sm_rapidjockey", Cmd_RapidJockey, ADMFLAG_ROOT, "Toggle RapidJockey event");
    RegAdminCmd("sm_realmode", Cmd_RealMode, ADMFLAG_ROOT, "Toggle RealMode event");
    RegAdminCmd("sm_zepz", Cmd_ZEPZ, ADMFLAG_ROOT, "Toggle ZEPZ event");
    RegAdminCmd("sm_myzz", Cmd_MYZZ, ADMFLAG_ROOT, "Toggle MYZZ event");
    RegAdminCmd("sm_hunterheal", Cmd_HunterHeal, ADMFLAG_ROOT, "Toggle HunterHeal event");
    RegAdminCmd("sm_fireinfected", Cmd_FireInfected, ADMFLAG_ROOT, "Toggle FireInfected event");
    RegAdminCmd("sm_stealthsmoker", Cmd_StealthSmoker, ADMFLAG_ROOT, "Toggle StealthSmoker event");
    RegAdminCmd("sm_spittercontrol", Cmd_SpitterControl, ADMFLAG_ROOT, "Toggle SpitterControl event");
    RegAdminCmd("sm_tankcontrol", Cmd_TankControl, ADMFLAG_ROOT, "Toggle TankControl event");
    RegAdminCmd("sm_witchrbirth", Cmd_WitchRbirth, ADMFLAG_ROOT, "Toggle WitchRbirth event");

    // 自动生成配置文件
    AutoExecConfig(true, "l4d2_specials_events_re");
}

// ====================================================================================================
// 创建 ConVars
// ====================================================================================================
void CreateConVars()
{
    g_cvEventMinInterval = CreateConVar(
        "l4d2_events_min_interval",
        "160.0",
        "随机事件触发的最小间隔时间（秒）",
        FCVAR_NOTIFY,
        true, 60.0,
        true, 600.0
    );

    g_cvEventMaxInterval = CreateConVar(
        "l4d2_events_max_interval",
        "230.0",
        "随机事件触发的最大间隔时间（秒）",
        FCVAR_NOTIFY,
        true, 60.0,
        true, 600.0
    );
}

public void OnMapStart()
{
    // 预缓存 ZEPZ 事件所需的模型
    PrecacheModel("models/infected/limbs/exploded_boomer.mdl", true);
    PrecacheModel("models/props/cs_militia/silo_01.mdl", true);

    // 预缓存 FireInfected 事件所需的模型
    PrecacheModel("models/props_junk/gascan001a.mdl", true);
}

public void OnClientPutInServer(int client)
{
    // Hook 玩家受伤事件用于 MYZZ 流血效果和 HunterHeal 效果
    SDKHook(client, SDKHook_OnTakeDamage, OnTakeDamage);
}

// ====================================================================================================
// 测试命令
// ====================================================================================================
public Action Cmd_RapidJockey(int client, int args)
{
    RapidJockey();
    return Plugin_Handled;
}

public Action Cmd_RealMode(int client, int args)
{
    RealMode();
    return Plugin_Handled;
}

public Action Cmd_ZEPZ(int client, int args)
{
    ZEPZ();
    return Plugin_Handled;
}

public Action Cmd_MYZZ(int client, int args)
{
    MYZZ();
    return Plugin_Handled;
}

public Action Cmd_HunterHeal(int client, int args)
{
    HunterHeal();
    return Plugin_Handled;
}

public Action Cmd_FireInfected(int client, int args)
{
    FireInfected();
    return Plugin_Handled;
}

public Action Cmd_StealthSmoker(int client, int args)
{
    StealthSmoker();
    return Plugin_Handled;
}

public Action Cmd_SpitterControl(int client, int args)
{
    SpitterControl();
    return Plugin_Handled;
}

public Action Cmd_TankControl(int client, int args)
{
    TankControl();
    return Plugin_Handled;
}

public Action Cmd_WitchRbirth(int client, int args)
{
    WitchRbirth();
    return Plugin_Handled;
}

// ====================================================================================================
// 随机事件: 深度控制 (RapidJockey)
// ====================================================================================================
void RapidJockey()
{
    g_RapidJockey = true;
    PrintToChatAll("☠触发永久特殊事件:深度控制(Jockey抓住幸存者之后会使用跳跃)");
}

// ====================================================================================================
// 随机事件: 真实模式 (RealMode)
// ====================================================================================================
void RealMode()
{
    g_RealMode = true;
    ConVar cvar = FindConVar("sv_disable_glow_survivors");
    if (cvar != null)
    {
        cvar.SetInt(1);
    }
    PrintToChatAll("☠触发特殊事件:真实模式(玩家的轮廓将消失，持续一局)");
}

// ====================================================================================================
// 随机事件: 憎恶胖子 (ZEPZ)
// ====================================================================================================
void ZEPZ()
{
    g_ZEPZ = true;
    PrintToChatAll("☠触发永久特殊事件:憎恶胖子（boomer死亡后会掉落一个陷阱,玩家经过会扣5血并禁锢10s）");
}

// ====================================================================================================
// 随机事件: 梦魇之爪 (MYZZ)
// ====================================================================================================
void MYZZ()
{
    g_MYZZ = true;
    PrintToChatAll("☠触发永久特殊事件:梦魇之爪（特感普通攻击会让幸存者持续5秒流血效果(团灭后事件消失)）");
}

// ====================================================================================================
// 随机事件: 猎取养分 (HunterHeal)
// ====================================================================================================
void HunterHeal()
{
    g_HunterHeal = true;
    PrintToChatAll("☠触发永久特殊事件:猎取养分（hunter的每次攻击，会使场中所有特感恢复50hp）");
}

// ====================================================================================================
// 随机事件: 自焚特感 (FireInfected)
// ====================================================================================================
void FireInfected()
{
    g_FireInfected = true;
    PrintToChatAll("☠触发特殊事件:自焚特感（特感死后有概率产生火焰，持续一局）");
}

// ====================================================================================================
// 随机事件: 硬直反弹 (StealthSmoker)
// ====================================================================================================
void StealthSmoker()
{
    g_StealthSmoker = true;
    PrintToChatAll("☠触发永久特殊事件:硬直反弹（hunter被推后会反弹硬直效果给该玩家(团灭后事件消失)）");
}

// ====================================================================================================
// 随机事件: 润滑毒液 (SpitterControl)
// ====================================================================================================
void SpitterControl()
{
    g_SpitterControl = true;
    PrintToChatAll("☠触发永久特殊事件:润滑毒液（口水的攻击会使玩家脚滑失去控制(团灭后事件消失)）");
}

// ====================================================================================================
// 随机事件: 地动山摇 (TankControl)
// ====================================================================================================
void TankControl()
{
    g_TankControl = true;
    PrintToChatAll("☠触发特殊事件:地动山摇（坦克每次攻击到一个玩家，所有玩家都会因此无法站稳被硬直，持续一局(提示：在空中可以规避硬直)）");
}

// ====================================================================================================
// 随机事件: 巫蛊坦克 (WitchRbirth)
// ====================================================================================================
void WitchRbirth()
{
    g_WitchRbirth = true;
    PrintToChatAll("☠触发永久特殊事件:巫蛊坦克（坦克每次击杀一个玩家，会生成一只额外的女巫(团灭后事件消失)）");
}

// ====================================================================================================
// 事件处理: 回合开始
// ====================================================================================================
public Action Event_RoundStart(Event event, const char[] name, bool dontBroadcast)
{
    // 重置真实模式
    if (g_RealMode)
    {
        g_RealMode = false;
        ConVar cvar = FindConVar("sv_disable_glow_survivors");
        if (cvar != null)
        {
            cvar.SetInt(0); // 恢复玩家轮廓
        }
    }

    // 重置自焚特感
    g_FireInfected = false;

    // 重置地动山摇
    g_TankControl = false;

    // 清理旧定时器
    if (g_hTimerLeftStart != null)
    {
        delete g_hTimerLeftStart;
        g_hTimerLeftStart = null;
    }

    if (g_hTimerSpecialEvents != null)
    {
        delete g_hTimerSpecialEvents;
        g_hTimerSpecialEvents = null;
    }

    // 启动检测玩家离开安全区域的定时器
    g_hTimerLeftStart = CreateTimer(0.1, Timer_CheckLeftStart, _, TIMER_REPEAT);

    return Plugin_Continue;
}

// ====================================================================================================
// 定时器: 检测玩家离开安全区域
// ====================================================================================================
public Action Timer_CheckLeftStart(Handle timer)
{
    // 检查是否有幸存者离开了安全区域
    for (int i = 1; i <= MaxClients; i++)
    {
        if (IsClientInGame(i) && GetClientTeam(i) == 2 && IsPlayerAlive(i))
        {
            if (!IsPlayerInStartArea(i))
            {
                // 有玩家离开安全区域，启动随机事件系统
                g_hTimerLeftStart = null;
                StartRandomEventSystem();
                return Plugin_Stop;
            }
        }
    }

    return Plugin_Continue;
}

// ====================================================================================================
// 启动随机事件系统
// ====================================================================================================
void StartRandomEventSystem()
{
    PrintToChatAll("\x04[特殊事件] \x01随机事件系统已启动！");

    // 立即触发第一个随机事件
    CreateTimer(10.0, Timer_TriggerRandomEvent, _, TIMER_FLAG_NO_MAPCHANGE);
}

// ====================================================================================================
// 定时器: 触发随机事件
// ====================================================================================================
public Action Timer_TriggerRandomEvent(Handle timer)
{
    g_hTimerSpecialEvents = null;

    // 触发随机事件
    ExecuteRandomEvent();

    // 设置下一次随机事件的时间
    float minInterval = g_cvEventMinInterval.FloatValue;
    float maxInterval = g_cvEventMaxInterval.FloatValue;
    float nextInterval = GetRandomFloat(minInterval, maxInterval);

    PrintToChatAll("\x04[特殊事件] \x01下一个随机事件将在 \x03%.0f秒 \x01后触发", nextInterval);

    g_hTimerSpecialEvents = CreateTimer(nextInterval, Timer_TriggerRandomEvent, _, TIMER_FLAG_NO_MAPCHANGE);

    return Plugin_Stop;
}

// ====================================================================================================
// 执行随机事件
// ====================================================================================================
void ExecuteRandomEvent()
{
    // 随机选择一个事件（1-10）
    int eventId = GetRandomInt(1, 10);

    switch (eventId)
    {
        case 1: RapidJockey();
        case 2: RealMode();
        case 3: ZEPZ();
        case 4: MYZZ();
        case 5: HunterHeal();
        case 6: FireInfected();
        case 7: StealthSmoker();
        case 8: SpitterControl();
        case 9: TankControl();
        case 10: WitchRbirth();
    }
}

// ====================================================================================================
// 辅助函数: 检测玩家是否在安全区域
// ====================================================================================================
bool IsPlayerInStartArea(int client)
{
    if (client <= 0 || client > MaxClients || !IsClientInGame(client))
        return false;

    Address navArea = L4D_GetLastKnownArea(client);
    if (navArea == Address_Null)
        return false;

    return view_as<bool>(L4D_GetNavArea_SpawnAttributes(navArea) & NAV_SPAWN_CHECKPOINT);
}

// ====================================================================================================
// 事件处理: 任务失败（团灭）
// ====================================================================================================
public Action Event_MissionLost(Event event, const char[] name, bool dontBroadcast)
{
    // 重置 ZEPZ 事件
    if (g_ZEPZ)
    {
        g_ZEPZ = false;
        PrintToChatAll("所有玩家已死亡，憎恶胖子事件已重置");
    }

    // 重置 MYZZ 事件
    if (g_MYZZ)
    {
        g_MYZZ = false;
        PrintToChatAll("所有玩家已死亡，梦魇之爪事件已重置");
    }

    // 重置 StealthSmoker 事件
    if (g_StealthSmoker)
    {
        g_StealthSmoker = false;
        PrintToChatAll("所有玩家已死亡，硬直反弹事件已重置");
    }

    // 重置 SpitterControl 事件
    if (g_SpitterControl)
    {
        g_SpitterControl = false;
        PrintToChatAll("所有玩家已死亡，润滑毒液事件已重置");
    }

    // 重置 WitchRbirth 事件
    if (g_WitchRbirth)
    {
        g_WitchRbirth = false;
        PrintToChatAll("所有玩家已死亡，巫蛊坦克事件已重置");
    }

    return Plugin_Continue;
}

// ====================================================================================================
// 事件处理: 玩家死亡
// ====================================================================================================
public Action Event_PlayerDeath(Event event, const char[] name, bool dontBroadcast)
{
    int client = GetClientOfUserId(event.GetInt("userid"));
    if (client <= 0 || !IsClientInGame(client))
        return Plugin_Continue;

    int attacker = GetClientOfUserId(event.GetInt("attacker"));

    // 处理感染者死亡事件
    if (GetClientTeam(client) == 3)
    {
        // 获取死亡位置
        float flPos[3];
        GetEntPropVector(client, Prop_Send, "m_vecOrigin", flPos);

        // ZEPZ 事件：Boomer 死亡掉落陷阱
        if (g_ZEPZ)
        {
            // 检查是否是 Boomer (僵尸类型 2)
            if (GetEntProp(client, Prop_Send, "m_zombieClass") == 2)
            {
                CreateBoomerTrap(flPos);
            }
        }

        // FireInfected 事件：特感死后有 25% 概率产生火焰
        if (g_FireInfected)
        {
            if (GetRandomInt(1, 100) <= 25)
            {
                SpawnFireProp(client, flPos);
            }
        }
    }

    // 处理幸存者死亡事件
    if (GetClientTeam(client) == 2)
    {
        // WitchRbirth 事件：Tank 击杀幸存者后生成 Witch
        if (g_WitchRbirth)
        {
            HandleWitchRbirth(client, attacker);
        }
    }

    return Plugin_Continue;
}

// ====================================================================================================
// 事件处理: 玩家推开
// ====================================================================================================
public Action Event_PlayerShoved(Event event, const char[] name, bool dontBroadcast)
{
    if (!g_StealthSmoker)
        return Plugin_Continue;

    int attacker = GetClientOfUserId(event.GetInt("attacker"));
    int victim = GetClientOfUserId(event.GetInt("userid"));

    // 检查攻击者是否是幸存者
    if (attacker <= 0 || attacker > MaxClients || !IsClientInGame(attacker))
        return Plugin_Continue;

    if (GetClientTeam(attacker) != 2)
        return Plugin_Continue;

    // 检查受害者是否是 Hunter
    if (victim <= 0 || victim > MaxClients || !IsClientInGame(victim))
        return Plugin_Continue;

    if (GetClientTeam(victim) != 3)
        return Plugin_Continue;

    // 检查是否是 Hunter (僵尸类型 3)
    if (GetEntProp(victim, Prop_Send, "m_zombieClass") != 3)
        return Plugin_Continue;

    // 对推开 Hunter 的幸存者施加硬直效果
    float vecNull[3];
    L4D_StaggerPlayer(attacker, victim, vecNull);

    return Plugin_Continue;
}

// ====================================================================================================
// 事件处理: Jockey骑乘
// ====================================================================================================
public Action Event_JockeyRide(Event event, const char[] name, bool dontBroadcast)
{
    if (!g_RapidJockey)
        return Plugin_Continue;

    int userid = event.GetInt("userid");
    if (userid == 0)
        return Plugin_Continue;

    int client = GetClientOfUserId(userid);
    if (client <= 0 || !IsClientInGame(client) || GetClientTeam(client) != 3)
        return Plugin_Continue;

    // 使用随机延迟创建跳跃定时器
    CreateTimer(GetRandomFloat(0.1, 0.5), Timer_DoJump, userid, TIMER_REPEAT);

    return Plugin_Continue;
}

// ====================================================================================================
// 定时器: 执行Jockey跳跃
// ====================================================================================================
public Action Timer_DoJump(Handle timer, int userid)
{
    if (!g_RapidJockey)
        return Plugin_Stop;

    int client = GetClientOfUserId(userid);
    if (client <= 0 || !IsClientInGame(client) || GetClientTeam(client) != 3 || !IsPlayerAlive(client))
        return Plugin_Stop;

    // 获取被骑乘的受害者
    int victim = GetEntPropEnt(client, Prop_Send, "m_jockeyVictim");
    if (victim <= 0 || !IsClientInGame(victim) || !IsPlayerAlive(victim))
        return Plugin_Stop;

    // 验证Jockey确实在控制这个受害者
    int jockeyAttacker = GetEntPropEnt(victim, Prop_Send, "m_jockeyAttacker");
    if (jockeyAttacker != client)
        return Plugin_Stop;

    // 检查受害者是否在地面上
    int groundEntity = GetEntPropEnt(victim, Prop_Send, "m_hGroundEntity");
    if (groundEntity != -1)
    {
        // 在地面上，执行跳跃
        float vel[3];
        GetEntPropVector(victim, Prop_Data, "m_vecBaseVelocity", vel);
        vel[2] += 610.0; // 向上的速度
        TeleportEntity(victim, NULL_VECTOR, NULL_VECTOR, vel);

        // 继续下一次跳跃
        CreateTimer(GetRandomFloat(0.1, 0.5), Timer_DoJump, userid, TIMER_FLAG_NO_MAPCHANGE);
        return Plugin_Stop;
    }
    else
    {
        // 不在地面上，使用RequestFrame继续检查
        RequestFrame(OnFrame_CheckJump, userid);
        return Plugin_Stop;
    }
}

// ====================================================================================================
// 帧回调: 检查跳跃状态
// ====================================================================================================
public void OnFrame_CheckJump(int userid)
{
    if (!g_RapidJockey)
        return;

    int client = GetClientOfUserId(userid);
    if (client <= 0 || !IsClientInGame(client) || GetClientTeam(client) != 3 || !IsPlayerAlive(client))
        return;

    // 获取被骑乘的受害者
    int victim = GetEntPropEnt(client, Prop_Send, "m_jockeyVictim");
    if (victim <= 0 || !IsClientInGame(victim) || !IsPlayerAlive(victim))
        return;

    // 验证Jockey确实在控制这个受害者
    int jockeyAttacker = GetEntPropEnt(victim, Prop_Send, "m_jockeyAttacker");
    if (jockeyAttacker != client)
        return;

    // 继续执行跳跃逻辑
    CreateTimer(GetRandomFloat(0.1, 0.5), Timer_DoJump, userid, TIMER_FLAG_NO_MAPCHANGE);
}

// ====================================================================================================
// 辅助函数: 创建 Boomer 陷阱
// ====================================================================================================
void CreateBoomerTrap(float pos[3])
{
    // 创建视觉实体（爆炸的 Boomer 尸体）
    int entity = CreateEntityByName("prop_dynamic_override");
    if (entity == -1)
        return;

    DispatchKeyValue(entity, "model", "models/infected/limbs/exploded_boomer.mdl");
    DispatchKeyValue(entity, "solid", "0");
    DispatchKeyValue(entity, "spawnflags", "256");
    DispatchSpawn(entity);

    // 设置实体标志
    int flags = GetEntProp(entity, Prop_Data, "m_iEFlags");
    SetEntProp(entity, Prop_Data, "m_iEFlags", flags | (1 << 25) | (1 << 9)); // EFL_DONTBLOCKLOS | EFL_FORCE_CHECK_TRANSMIT
    SetEntProp(entity, Prop_Send, "m_nSolidType", 6); // SOLID_VPHYSICS

    TeleportEntity(entity, pos, NULL_VECTOR, NULL_VECTOR);

    // 获取实体的边界框
    float vPos[3], vMins[3], vMaxs[3];
    GetEntPropVector(entity, Prop_Send, "m_vecOrigin", vPos);
    GetEntPropVector(entity, Prop_Send, "m_vecMaxs", vMaxs);
    GetEntPropVector(entity, Prop_Send, "m_vecMins", vMins);

    // 调整高度
    vMaxs[2] -= 30.0;

    // 创建触发器
    CreateTriggerTrap(entity, vPos, vMaxs, vMins);
}

// ====================================================================================================
// 辅助函数: 创建触发陷阱
// ====================================================================================================
void CreateTriggerTrap(int parent, float vPos[3], float vMaxs[3], float vMins[3])
{
    int trigger = CreateEntityByName("trigger_multiple");
    if (trigger == -1)
        return;

    DispatchKeyValue(trigger, "spawnflags", "1");
    DispatchKeyValue(trigger, "entireteam", "0");
    DispatchKeyValue(trigger, "allowincap", "1");
    DispatchKeyValue(trigger, "allowghost", "0");

    SetEntityModel(trigger, "models/props/cs_militia/silo_01.mdl");
    DispatchSpawn(trigger);

    SetEntProp(trigger, Prop_Send, "m_nSolidType", 2); // SOLID_BBOX
    SetEntPropVector(trigger, Prop_Send, "m_vecMins", vMins);
    SetEntPropVector(trigger, Prop_Send, "m_vecMaxs", vMaxs);

    TeleportEntity(trigger, vPos, NULL_VECTOR, NULL_VECTOR);

    // 将触发器附加到父实体
    SetVariantString("!activator");
    AcceptEntityInput(trigger, "SetParent", parent);

    // Hook 触摸事件
    SDKHook(trigger, SDKHook_StartTouch, OnTouchTriggerFreezer);
}

// ====================================================================================================
// SDKHook 回调: 触摸陷阱触发器
// ====================================================================================================
public Action OnTouchTriggerFreezer(int entity, int target)
{
    if (entity <= 0 || !IsValidEntity(entity))
        return Plugin_Continue;

    // 检查是否是幸存者
    if (target <= 0 || target > MaxClients || !IsClientInGame(target))
        return Plugin_Continue;

    if (GetClientTeam(target) != 2 || !IsPlayerAlive(target))
        return Plugin_Continue;

    // 获取父实体（视觉模型）
    int parent = -1;
    if (HasEntProp(entity, Prop_Send, "moveparent"))
    {
        parent = GetEntPropEnt(entity, Prop_Send, "moveparent");
    }

    // 造成伤害
    DealDamage(target, target, 5);

    PrintToChatAll("\x01 %N 踩中了胖胖死亡掉落的陷阱,被禁锢了!", target);

    // 移除触发器
    SDKUnhook(entity, SDKHook_StartTouch, OnTouchTriggerFreezer);
    RemoveEntity(entity);

    // 禁锢玩家
    SetEntityMoveType(target, MOVETYPE_NONE);

    // 创建定时器解除禁锢
    if (parent > 0)
    {
        DataPack dp;
        CreateDataTimer(10.0, Timer_UnFreeze, dp, TIMER_FLAG_NO_MAPCHANGE);
        dp.WriteCell(GetClientUserId(target));
        dp.WriteCell(EntIndexToEntRef(parent));
    }
    else
    {
        CreateTimer(10.0, Timer_UnFreezeSimple, GetClientUserId(target), TIMER_FLAG_NO_MAPCHANGE);
    }

    return Plugin_Continue;
}

// ====================================================================================================
// 定时器: 解除禁锢
// ====================================================================================================
public Action Timer_UnFreeze(Handle timer, DataPack dp)
{
    dp.Reset();
    int userid = dp.ReadCell();
    int entityRef = dp.ReadCell();

    int client = GetClientOfUserId(userid);
    if (client > 0 && IsClientInGame(client) && GetClientTeam(client) == 2 && IsPlayerAlive(client))
    {
        SetEntityMoveType(client, MOVETYPE_WALK);
    }

    int entity = EntRefToEntIndex(entityRef);
    if (entity > 0 && IsValidEntity(entity))
    {
        RemoveEntity(entity);
    }

    return Plugin_Stop;
}

// ====================================================================================================
// 定时器: 解除禁锢（简化版）
// ====================================================================================================
public Action Timer_UnFreezeSimple(Handle timer, int userid)
{
    int client = GetClientOfUserId(userid);
    if (client > 0 && IsClientInGame(client) && GetClientTeam(client) == 2 && IsPlayerAlive(client))
    {
        SetEntityMoveType(client, MOVETYPE_WALK);
    }

    return Plugin_Stop;
}

// ====================================================================================================
// 辅助函数: 造成伤害
// ====================================================================================================
void DealDamage(int victim, int attacker, int damage)
{
    if (!IsValidEdict(victim) || damage <= 0)
        return;

    char victimName[16];
    Format(victimName, sizeof(victimName), "victim%d", victim);

    int pointHurt = CreateEntityByName("point_hurt");
    if (pointHurt == -1)
        return;

    DispatchKeyValue(victim, "targetname", victimName);
    DispatchKeyValue(pointHurt, "DamageTarget", victimName);
    DispatchKeyValueFloat(pointHurt, "Damage", float(damage));
    DispatchKeyValue(pointHurt, "DamageType", "0");

    DispatchSpawn(pointHurt);

    if (attacker > 0 && attacker <= MaxClients && IsClientInGame(attacker))
    {
        AcceptEntityInput(pointHurt, "Hurt", attacker);
    }
    else
    {
        AcceptEntityInput(pointHurt, "Hurt");
    }

    RemoveEdict(pointHurt);
}

// ====================================================================================================
// SDKHook 回调: 玩家受伤
// ====================================================================================================
public Action OnTakeDamage(int victim, int &attacker, int &inflictor, float &damage, int &damagetype, int &weapon, float damageForce[3], float damagePosition[3])
{
    // 添加通用调试日志
    char victimName[64], attackerName[64];
    if (victim > 0 && victim <= MaxClients && IsClientInGame(victim))
        GetClientName(victim, victimName, sizeof(victimName));
    else
        strcopy(victimName, sizeof(victimName), "无效");

    if (attacker > 0 && attacker <= MaxClients && IsClientInGame(attacker))
        GetClientName(attacker, attackerName, sizeof(attackerName));
    else
        strcopy(attackerName, sizeof(attackerName), "无效");

    PrintToServer("[OnTakeDamage] 伤害事件: 受害者=%d(%s), 攻击者=%d(%s), 伤害类型=%d, 伤害=%.1f",
                  victim, victimName, attacker, attackerName, damagetype, damage);

    // 处理 HunterHeal 事件
    if (g_HunterHeal)
    {
        HandleHunterHeal(victim, attacker);
    }

    // 处理 MYZZ 事件
    if (g_MYZZ)
    {
        HandleMYZZ(victim, attacker, weapon, inflictor);
    }

    // 处理 SpitterControl 事件
    if (g_SpitterControl)
    {
        HandleSpitterControl(victim, attacker, damagetype);
    }

    // 处理 TankControl 事件
    if (g_TankControl)
    {
        HandleTankControl(victim, attacker);
    }

    return Plugin_Continue;
}

// ====================================================================================================
// 定时器: 流血伤害
// ====================================================================================================
public Action Timer_BleedDamage(Handle timer, int userid)
{
    int client = GetClientOfUserId(userid);
    if (client <= 0 || !IsClientInGame(client) || GetClientTeam(client) != 2)
        return Plugin_Stop;

    // 如果玩家倒地，不造成伤害
    if (GetEntProp(client, Prop_Send, "m_isIncapacitated"))
        return Plugin_Stop;

    if (!IsPlayerAlive(client))
        return Plugin_Stop;

    int health = GetClientHealth(client);
    float tempHealth = GetTempHealth(client);

    // 优先扣除虚血
    if (tempHealth > 2.0)
    {
        if (tempHealth >= 1.0)
        {
            SetTempHealth(client, tempHealth - 1.0);
            // 如果实血大于1，也扣1点实血
            if (health > 1)
            {
                SetEntityHealth(client, health - 1);
            }
            else
            {
                // 实血不足，倒地
                SetEntProp(client, Prop_Send, "m_isIncapacitated", 1);
            }
        }
        else
        {
            // 虚血不足1点，直接倒地
            if (health > 2)
            {
                SetEntityHealth(client, health - 1);
            }
            SetEntProp(client, Prop_Send, "m_isIncapacitated", 1);
        }
    }
    else
    {
        // 虚血不足，扣除虚血
        SetTempHealth(client, tempHealth - 1.0);
    }

    return Plugin_Stop;
}

// ====================================================================================================
// 辅助函数: 获取虚血
// ====================================================================================================
float GetTempHealth(int client)
{
    if (!IsClientConnected(client) || !IsClientInGame(client))
        return 0.0;

    float fullTemp = GetEntPropFloat(client, Prop_Send, "m_healthBuffer");
    float tempStarted = GetEntPropFloat(client, Prop_Send, "m_healthBufferTime");

    ConVar cvar = FindConVar("pain_pills_decay_rate");
    if (cvar == null)
        return 0.0;

    float decayRate = cvar.FloatValue;
    float tempDuration = (tempStarted - GetGameTime()) * -1.0;
    float tempHp = fullTemp - (tempDuration * decayRate);

    if (tempHp < 0.0)
        tempHp = 0.0;

    return tempHp;
}

// ====================================================================================================
// 辅助函数: 设置虚血
// ====================================================================================================
bool SetTempHealth(int client, float hp)
{
    if (!IsClientConnected(client) || !IsClientInGame(client))
        return false;

    SetEntPropFloat(client, Prop_Send, "m_healthBufferTime", GetGameTime());
    SetEntPropFloat(client, Prop_Send, "m_healthBuffer", hp);

    return true;
}

// ====================================================================================================
// 辅助函数: 获取特感控制的幸存者
// ====================================================================================================
int GetSurvivorVictim(int client)
{
    int victim = -1;

    // Hunter 扑倒
    victim = GetEntPropEnt(client, Prop_Send, "m_pounceVictim");
    if (victim > 0)
        return victim;

    // Smoker 舌头
    victim = GetEntPropEnt(client, Prop_Send, "m_tongueVictim");
    if (victim > 0)
        return victim;

    // Jockey 骑乘
    victim = GetEntPropEnt(client, Prop_Send, "m_jockeyVictim");
    if (victim > 0)
        return victim;

    // Charger 冲撞/抓取
    victim = GetEntPropEnt(client, Prop_Send, "m_pummelVictim");
    if (victim > 0)
        return victim;

    victim = GetEntPropEnt(client, Prop_Send, "m_carryVictim");
    if (victim > 0)
        return victim;

    return -1;
}

// ====================================================================================================
// 辅助函数: 处理 Hunter 治疗事件
// ====================================================================================================
void HandleHunterHeal(int victim, int attacker)
{
    // 检查受害者是否是幸存者
    if (victim <= 0 || victim > MaxClients || !IsClientInGame(victim))
        return;

    if (GetClientTeam(victim) != 2)
        return;

    // 检查攻击者是否是 Hunter
    if (attacker <= 0 || attacker > MaxClients || !IsClientInGame(attacker))
        return;

    if (GetClientTeam(attacker) != 3)
        return;

    // 检查是否是 Hunter (僵尸类型 3)
    if (GetEntProp(attacker, Prop_Send, "m_zombieClass") != 3)
        return;

    // 提示受害者
    PrintHintText(victim, "hunter正在窃取你的血给全部特感恢复生命...");

    // 治疗所有特感
    for (int i = 1; i <= MaxClients; i++)
    {
        if (IsClientInGame(i) && IsPlayerAlive(i) && GetClientTeam(i) == 3)
        {
            HealPlayer(i, 50);
        }
    }
}

// ====================================================================================================
// 辅助函数: 处理梦魇之爪事件
// ====================================================================================================
void HandleMYZZ(int victim, int attacker, int weapon, int inflictor)
{
    // 检查受害者是否是幸存者
    if (victim <= 0 || victim > MaxClients || !IsClientInGame(victim))
        return;

    if (GetClientTeam(victim) != 2)
        return;

    // 检查攻击者是否是感染者
    if (attacker <= 0 || attacker > MaxClients || !IsClientInGame(attacker))
        return;

    if (GetClientTeam(attacker) != 3)
        return;

    // 获取武器类名
    char weaponClass[64];
    if (weapon > 0 && IsValidEdict(weapon))
    {
        GetEdictClassname(weapon, weaponClass, sizeof(weaponClass));
    }
    else if (inflictor > 0 && IsValidEdict(inflictor))
    {
        GetEdictClassname(inflictor, weaponClass, sizeof(weaponClass));
    }
    else
    {
        return;
    }

    // 检查是否是玩家造成的伤害（爪击）
    if (StrContains(weaponClass, "player", false) == -1)
        return;

    // 获取感染者类型
    int zombieClass = GetEntProp(attacker, Prop_Send, "m_zombieClass");

    // Tank (8) 不触发流血效果
    if (zombieClass >= 8)
        return;

    // 检查受害者是否被控制（被控制时不触发流血）
    int controlledVictim = GetSurvivorVictim(attacker);
    if (controlledVictim == victim)
        return;

    // 触发流血效果：5秒内每秒扣1点血
    int userid = GetClientUserId(victim);
    CreateTimer(1.0, Timer_BleedDamage, userid, TIMER_FLAG_NO_MAPCHANGE);
    CreateTimer(2.0, Timer_BleedDamage, userid, TIMER_FLAG_NO_MAPCHANGE);
    CreateTimer(3.0, Timer_BleedDamage, userid, TIMER_FLAG_NO_MAPCHANGE);
    CreateTimer(4.0, Timer_BleedDamage, userid, TIMER_FLAG_NO_MAPCHANGE);
    CreateTimer(5.0, Timer_BleedDamage, userid, TIMER_FLAG_NO_MAPCHANGE);
}

// ====================================================================================================
// 辅助函数: 处理润滑毒液事件
// ====================================================================================================
void HandleSpitterControl(int victim, int attacker, int damagetype)
{
    // 添加详细调试日志
    char victimName[64], attackerName[64];
    if (victim > 0 && victim <= MaxClients && IsClientInGame(victim))
        GetClientName(victim, victimName, sizeof(victimName));
    else
        strcopy(victimName, sizeof(victimName), "无效");

    if (attacker > 0 && attacker <= MaxClients && IsClientInGame(attacker))
        GetClientName(attacker, attackerName, sizeof(attackerName));
    else
        strcopy(attackerName, sizeof(attackerName), "无效");

    PrintToServer("[SpitterControl] 伤害事件: 受害者=%d(%s), 攻击者=%d(%s), 伤害类型=%d",
                  victim, victimName, attacker, attackerName, damagetype);

    // 检查受害者是否有效且是幸存者
    if (victim <= 0 || victim > MaxClients || !IsClientInGame(victim))
    {
        PrintToServer("[SpitterControl] 受害者无效或不在游戏中");
        return;
    }

    int victimTeam = GetClientTeam(victim);
    if (victimTeam != 2)
    {
        PrintToServer("[SpitterControl] 受害者不是幸存者，队伍=%d，跳过处理", victimTeam);
        return;
    }

    // 检查是否是Spitter毒液伤害
    // 方法1: 检查伤害类型 (263168 或 265216 是Spitter毒液伤害)
    bool isSpitterDamage = false;

    if (damagetype == 263168 || damagetype == 265216)
    {
        PrintToServer("[SpitterControl] 检测到Spitter毒液伤害类型: %d", damagetype);
        isSpitterDamage = true;
    }
    // 方法2: 检查攻击者是否是Spitter
    else if (attacker > 0 && attacker <= MaxClients && IsClientInGame(attacker) &&
             GetClientTeam(attacker) == 3 && GetEntProp(attacker, Prop_Send, "m_zombieClass") == 4)
    {
        PrintToServer("[SpitterControl] 检测到Spitter直接攻击");
        isSpitterDamage = true;
    }

    if (!isSpitterDamage)
    {
        PrintToServer("[SpitterControl] 不是Spitter相关伤害，跳过处理");
        return;
    }

    // 确保受害者还活着才施加效果
    if (!IsPlayerAlive(victim))
    {
        PrintToServer("[SpitterControl] 受害者已死亡，不施加脚滑效果");
        return;
    }

    PrintToServer("[SpitterControl] ✓ Spitter伤害确认！对 %s 施加脚滑效果", victimName);

    // 对受害者施加硬直效果（脚滑失去控制）
    float vecNull[3];
    L4D_StaggerPlayer(victim, attacker, vecNull);

    // 提示消息
    PrintHintText(victim, "你踩到了Spitter的毒液，脚滑失去了控制！");
    PrintToChatAll("\x01 %N 踩到了Spitter的毒液，脚滑失去了控制！", victim);
}

// ====================================================================================================
// 辅助函数: 处理地动山摇事件
// ====================================================================================================
void HandleTankControl(int victim, int attacker)
{
    // 添加详细调试日志
    char victimName[64], attackerName[64];
    if (victim > 0 && victim <= MaxClients && IsClientInGame(victim))
        GetClientName(victim, victimName, sizeof(victimName));
    else
        strcopy(victimName, sizeof(victimName), "无效");

    if (attacker > 0 && attacker <= MaxClients && IsClientInGame(attacker))
        GetClientName(attacker, attackerName, sizeof(attackerName));
    else
        strcopy(attackerName, sizeof(attackerName), "无效");

    PrintToServer("[TankControl] 伤害事件: 受害者=%d(%s), 攻击者=%d(%s)",
                  victim, victimName, attacker, attackerName);

    // 检查受害者是否有效且是幸存者
    if (victim <= 0 || victim > MaxClients || !IsClientInGame(victim))
    {
        PrintToServer("[TankControl] 受害者无效或不在游戏中");
        return;
    }

    int victimTeam = GetClientTeam(victim);
    if (victimTeam != 2)
    {
        PrintToServer("[TankControl] 受害者不是幸存者，队伍=%d，跳过处理", victimTeam);
        return;
    }

    // 检查攻击者是否有效且是Tank
    if (attacker <= 0 || attacker > MaxClients || !IsClientInGame(attacker))
    {
        PrintToServer("[TankControl] 攻击者无效或不在游戏中");
        return;
    }

    int attackerTeam = GetClientTeam(attacker);
    if (attackerTeam != 3)
    {
        PrintToServer("[TankControl] 攻击者不是感染者，队伍=%d", attackerTeam);
        return;
    }

    // 检查是否是 Tank (僵尸类型 8)
    int zombieClass = GetEntProp(attacker, Prop_Send, "m_zombieClass");
    if (zombieClass != 8)
    {
        PrintToServer("[TankControl] 攻击者不是Tank，僵尸类型=%d", zombieClass);
        return;
    }

    PrintToServer("[TankControl] ✓ Tank攻击幸存者确认！开始施加硬直效果");

    // 提示消息
    PrintToChatAll("\x01 %N 被坦克攻击之后地表发生了颤抖!", victim);

    // 对所有幸存者施加硬直效果
    float vecNull[3];
    int staggerCount = 0;
    for (int i = 1; i <= MaxClients; i++)
    {
        if (IsClientInGame(i) && IsPlayerAlive(i) && GetClientTeam(i) == 2)
        {
            // 施加硬直效果
            L4D_StaggerPlayer(i, attacker, vecNull);
            staggerCount++;

            PrintToServer("[TankControl] 对玩家 %N 施加硬直效果", i);

            // 播放地震音效
            EmitSoundToClient(i, "physics/destruction/smash_cave_woodrockcollapse2.wav",
                SOUND_FROM_PLAYER, SNDCHAN_AUTO, SNDLEVEL_NORMAL, SND_NOFLAGS, 1.0, 100);
        }
    }

    PrintToServer("[TankControl] 总共对 %d 个幸存者施加了硬直效果", staggerCount);
}

// ====================================================================================================
// 辅助函数: 治疗玩家
// ====================================================================================================
void HealPlayer(int client, int healAmount)
{
    if (client <= 0 || client > MaxClients || !IsClientInGame(client))
        return;

    int maxHealth = GetEntProp(client, Prop_Data, "m_iMaxHealth");
    int currentHealth = GetClientHealth(client);

    int newHealth = currentHealth + healAmount;
    if (newHealth > maxHealth)
    {
        newHealth = maxHealth;
    }

    SetEntityHealth(client, newHealth);
}

// ====================================================================================================
// 辅助函数: 生成火焰道具（汽油桶）
// ====================================================================================================
void SpawnFireProp(int client, float pos[3])
{
    int iProp = CreateEntityByName("prop_physics");
    if (iProp == -1)
        return;

    // 设置模型
    SetEntityModel(iProp, "models/props_junk/gascan001a.mdl");

    // 禁用阴影
    DispatchKeyValue(iProp, "disableshadows", "1");

    // 调整位置（向上偏移 30 单位）
    pos[2] += 30.0;

    // 传送到位置
    TeleportEntity(iProp, pos, NULL_VECTOR, NULL_VECTOR);

    // 生成实体
    DispatchSpawn(iProp);

    // 设置所有者
    SetEntPropEnt(iProp, Prop_Data, "m_hOwnerEntity", client);

    // 添加调试日志
    PrintToServer("[FireInfected] 在位置 (%.1f, %.1f, %.1f) 生成火焰道具，所有者: %N", pos[0], pos[1], pos[2], client);

    // 设置碰撞组
    SetEntProp(iProp, Prop_Data, "m_CollisionGroup", 1);

    // 设置为不可见（透明）
    SetEntityRenderMode(iProp, RENDER_TRANSCOLOR);
    SetEntityRenderColor(iProp, 0, 0, 0, 0);

    // 立即破坏，产生火焰效果
    AcceptEntityInput(iProp, "Break", client);
}

// ====================================================================================================
// 辅助函数: 处理巫蛊坦克事件
// ====================================================================================================
void HandleWitchRbirth(int victim, int attacker)
{
    // 检查受害者是否是幸存者
    if (victim <= 0 || victim > MaxClients || !IsClientInGame(victim))
        return;

    // 检查攻击者是否是 Tank
    if (attacker <= 0 || attacker > MaxClients || !IsClientInGame(attacker))
        return;

    if (GetClientTeam(attacker) != 3)
        return;

    // 检查是否是 Tank (僵尸类型 8)
    if (GetEntProp(attacker, Prop_Send, "m_zombieClass") != 8)
        return;

    // 在 Tank 位置生成 Witch
    CheatCommand(attacker, "z_spawn", "witch");
    PrintToChatAll("\x01 %N 被坦克包装成可爱的女巫~", victim);
}

// ====================================================================================================
// 辅助函数: 执行作弊命令
// ====================================================================================================
void CheatCommand(int client, const char[] command, const char[] arguments)
{
    if (client <= 0 || client > MaxClients || !IsClientInGame(client))
        return;

    // 保存原始权限
    int userFlags = GetUserFlagBits(client);

    // 临时给予 Root 权限
    SetUserFlagBits(client, ADMFLAG_ROOT);

    // 保存命令原始标志
    int flags = GetCommandFlags(command);

    // 移除作弊标志
    SetCommandFlags(command, flags & ~FCVAR_CHEAT);

    // 执行命令
    FakeClientCommand(client, "%s %s", command, arguments);

    // 恢复命令标志
    SetCommandFlags(command, flags);

    // 恢复原始权限
    SetUserFlagBits(client, userFlags);
}

// ====================================================================================================
// 事件处理: 玩家受伤
// ====================================================================================================
public void Event_PlayerHurt(Event event, const char[] name, bool dontBroadcast)
{
    int victim = GetClientOfUserId(event.GetInt("userid"));
    int attacker = GetClientOfUserId(event.GetInt("attacker"));
    int damagetype = event.GetInt("type");
    int damage = event.GetInt("dmg_health");

    // 添加详细调试日志
    char victimName[64], attackerName[64];
    if (victim > 0 && victim <= MaxClients && IsClientInGame(victim))
        GetClientName(victim, victimName, sizeof(victimName));
    else
        strcopy(victimName, sizeof(victimName), "无效");

    if (attacker > 0 && attacker <= MaxClients && IsClientInGame(attacker))
        GetClientName(attacker, attackerName, sizeof(attackerName));
    else
        strcopy(attackerName, sizeof(attackerName), "无效");

    PrintToServer("[Event_PlayerHurt] 伤害事件: 受害者=%d(%s), 攻击者=%d(%s), 伤害类型=%d, 伤害=%d",
                  victim, victimName, attacker, attackerName, damagetype, damage);

    // 处理 SpitterControl 事件
    if (g_SpitterControl)
    {
        HandleSpitterControlEvent(victim, attacker, damagetype);
    }

    // 处理 TankControl 事件
    if (g_TankControl)
    {
        HandleTankControlEvent(victim, attacker, damagetype);
    }
}

// ====================================================================================================
// 辅助函数: 处理润滑毒液事件 (通过player_hurt事件)
// ====================================================================================================
void HandleSpitterControlEvent(int victim, int attacker, int damagetype)
{
    // 添加详细调试日志
    char victimName[64], attackerName[64];
    if (victim > 0 && victim <= MaxClients && IsClientInGame(victim))
        GetClientName(victim, victimName, sizeof(victimName));
    else
        strcopy(victimName, sizeof(victimName), "无效");

    if (attacker > 0 && attacker <= MaxClients && IsClientInGame(attacker))
        GetClientName(attacker, attackerName, sizeof(attackerName));
    else
        strcopy(attackerName, sizeof(attackerName), "无效");

    PrintToServer("[SpitterControlEvent] 伤害事件: 受害者=%d(%s), 攻击者=%d(%s), 伤害类型=%d",
                  victim, victimName, attacker, attackerName, damagetype);

    // 检查受害者是否有效且是幸存者
    if (victim <= 0 || victim > MaxClients || !IsClientInGame(victim))
    {
        PrintToServer("[SpitterControlEvent] 受害者无效或不在游戏中");
        return;
    }

    int victimTeam = GetClientTeam(victim);
    if (victimTeam != 2)
    {
        PrintToServer("[SpitterControlEvent] 受害者不是幸存者，队伍=%d，跳过处理", victimTeam);
        return;
    }

    // 只检查Spitter毒液伤害 (263168 或 265216)，不包括直接攻击
    if (damagetype != 263168 && damagetype != 265216)
    {
        PrintToServer("[SpitterControlEvent] 不是Spitter毒液伤害，伤害类型=%d，跳过处理", damagetype);
        return;
    }

    PrintToServer("[SpitterControlEvent] ✓ 检测到Spitter毒液伤害类型: %d", damagetype);

    // 确保受害者还活着才施加效果
    if (!IsPlayerAlive(victim))
    {
        PrintToServer("[SpitterControlEvent] 受害者已死亡，不施加脚滑效果");
        return;
    }

    PrintToServer("[SpitterControlEvent] ✓ Spitter伤害确认！对 %s 施加脚滑效果", victimName);

    // 对受害者施加硬直效果（脚滑失去控制）
    float vecNull[3];
    L4D_StaggerPlayer(victim, attacker, vecNull);

    // 提示消息
    PrintHintText(victim, "你踩到了Spitter的毒液，脚滑失去了控制！");
    PrintToChatAll("\x01 %N 踩到了Spitter的毒液，脚滑失去了控制！", victim);
}

// ====================================================================================================
// 辅助函数: 处理坦克控制事件 (通过player_hurt事件)
// ====================================================================================================
void HandleTankControlEvent(int victim, int attacker, int damagetype)
{
    // 添加详细调试日志
    char victimName[64], attackerName[64];
    if (victim > 0 && victim <= MaxClients && IsClientInGame(victim))
        GetClientName(victim, victimName, sizeof(victimName));
    else
        strcopy(victimName, sizeof(victimName), "无效");

    if (attacker > 0 && attacker <= MaxClients && IsClientInGame(attacker))
        GetClientName(attacker, attackerName, sizeof(attackerName));
    else
        strcopy(attackerName, sizeof(attackerName), "无效");

    PrintToServer("[TankControlEvent] 伤害事件: 受害者=%d(%s), 攻击者=%d(%s), 伤害类型=%d",
                  victim, victimName, attacker, attackerName, damagetype);

    // 检查受害者是否有效且是幸存者
    if (victim <= 0 || victim > MaxClients || !IsClientInGame(victim))
    {
        PrintToServer("[TankControlEvent] 受害者无效或不在游戏中");
        return;
    }

    int victimTeam = GetClientTeam(victim);
    if (victimTeam != 2)
    {
        PrintToServer("[TankControlEvent] 受害者不是幸存者，队伍=%d，跳过处理", victimTeam);
        return;
    }

    // 检查攻击者是否有效且是Tank
    if (attacker <= 0 || attacker > MaxClients || !IsClientInGame(attacker))
    {
        PrintToServer("[TankControlEvent] 攻击者无效或不在游戏中");
        return;
    }

    int attackerTeam = GetClientTeam(attacker);
    if (attackerTeam != 3)
    {
        PrintToServer("[TankControlEvent] 攻击者不是感染者，队伍=%d", attackerTeam);
        return;
    }

    // 检查是否是 Tank (僵尸类型 8)
    int zombieClass = GetEntProp(attacker, Prop_Send, "m_zombieClass");
    if (zombieClass != 8)
    {
        PrintToServer("[TankControlEvent] 攻击者不是Tank，僵尸类型=%d", zombieClass);
        return;
    }

    // 检查伤害类型，确保是Tank的直接攻击而不是其他类型的伤害
    // Tank的拳击伤害通常是特定的伤害类型
    PrintToServer("[TankControlEvent] Tank攻击检测: 伤害类型=%d", damagetype);

    PrintToServer("[TankControlEvent] ✓ Tank攻击幸存者确认！开始施加硬直效果");

    // 提示消息
    PrintToChatAll("\x01 %N 被坦克攻击之后地表发生了颤抖!", victim);

    // 对所有幸存者施加硬直效果
    float vecNull[3];
    int staggerCount = 0;
    for (int i = 1; i <= MaxClients; i++)
    {
        if (IsClientInGame(i) && IsPlayerAlive(i) && GetClientTeam(i) == 2)
        {
            // 施加硬直效果
            L4D_StaggerPlayer(i, attacker, vecNull);
            staggerCount++;

            PrintToServer("[TankControlEvent] 对玩家 %N 施加硬直效果", i);

            // 播放地震音效
            EmitSoundToClient(i, "physics/destruction/smash_cave_woodrockcollapse2.wav",
                SOUND_FROM_PLAYER, SNDCHAN_AUTO, SNDLEVEL_NORMAL, SND_NOFLAGS, 1.0, 100);
        }
    }

    PrintToServer("[TankControlEvent] 总共对 %d 个幸存者施加了硬直效果", staggerCount);
}