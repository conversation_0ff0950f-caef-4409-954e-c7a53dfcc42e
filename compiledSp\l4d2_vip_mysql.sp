#include <sourcemod>
#include <left4dhooks>
#include <sdktools>
#include <sdkhooks>
#include <l4d2_vip>

#pragma semicolon 1
#pragma newdecls required

ConVar KickStatus, KickFull, KickTime;

char cRunTime[32];

Handle KickPlayerTimer[MAXPLAYERS + 1];

int KickLookOnPlayer[MAXPLAYERS + 1];

//插件信息
public Plugin myinfo = {
        name = "l4d2_vip_mysql",
        author = "凛渃",
        description = "",
        version = "1.0",
        url = "N/A"
}

public void OnPluginStart() {
    KickStatus		= CreateConVar("l4d2_afk_kick_status", "0", "启用非VIP玩家闲置超时踢出? (0=禁用, 1=启用).", FCVAR_NOTIFY);
    KickFull		= CreateConVar("l4d2_afk_kick_full", "0", "启用仅满人踢出? (0=禁用, 1=启用).", FCVAR_NOTIFY);
    KickTime		= CreateConVar("l4d2_afk_kick_time", "300.0", "设置非VIP玩家闲置超时踢出的时间/秒.", FCVAR_NOTIFY);
    AddCommandListener(OnCmdCallvote, "callvote");
    HookEvent("player_team", Event_PlayerTeam);
    HookEvent("player_disconnect", Event_PlayerDisconnect);
    HookEvent("round_end", Event_RoundEnd);
    //AutoExecConfig(true);
}

public Action OnCmdCallvote(int client, const char[] command, int argc) {
    if (argc < 2) 
        return Plugin_Continue;

    static char arg[PLATFORM_MAX_PATH];
    GetCmdArg(1, arg, sizeof(arg));
    if (strcmp(arg, "kick", false) != 0) 
        return Plugin_Continue;

    GetCmdArg(2, arg, sizeof(arg));
    int target = GetClientOfUserId(StringToInt(arg));
    if (target > 0 && target <= MaxClients && IsClientInGame(target) && !!IsVip(target)) {
        if (client > 0 && client <= MaxClients && IsClientInGame(client) && !IsFakeClient(client)) 
                PrintToChat(client, "\x04[提示]\x05玩家\x03%N\x05为VIP,无法踢出.", target);

        return Plugin_Handled;
    }
    return Plugin_Continue;
}

public void Event_PlayerTeam(Event event, const char[] name, bool dontBroadcast) {
	int newteam = GetEventInt(event, "team");
	int client = GetClientOfUserId(event.GetInt("userid"));
	if (KickStatus.IntValue > 0) {
		if (client && IsClientConnected(client) && IsClientInGame(client) && !IsFakeClient(client)) {
			if (newteam == 1 && !IsVip(client))
				KickPlayer(client);
			else
				delete KickPlayerTimer[client];
		}
	}
}

void KickPlayer(int client) {
	KickLookOnPlayer[client] = 0;
	delete KickPlayerTimer[client];
	KickPlayerTimer[client] = CreateTimer(1.0, Timer_KickLookOnPlayer, GetClientUserId(client), TIMER_REPEAT);
}

public Action Timer_KickLookOnPlayer(Handle timer, any client) {
    if ((client = GetClientOfUserId(client))) {
        if (!!IsVip(client) || GetClientTeam(client) != 1) {
            KickPlayerTimer[client] = null;
            KickLookOnPlayer[client] = 0;
            return Plugin_Stop;
        }
        if (!!KickFull.IntValue && GetAllPlayerCount() < GetSurvivorLimit()) {
            KickPlayerTimer[client] = null;
            KickLookOnPlayer[client] = 0;
            return Plugin_Stop;
        }
        if (KickLookOnPlayer[client] >= KickTime.FloatValue) {
            StandardizeTime(KickTime.FloatValue, cRunTime);
            KickClient(client, "服务器自动踢出闲置超过 %s 的非VIP玩家", cRunTime);
            PrintToChatAll("\x04[提示]非VIP玩家\x03%N\x05闲置超过\x03%s\x05而被服务器踢出.", client, cRunTime);
            KickPlayerTimer[client] = null;
            KickLookOnPlayer[client] = 0;
            return Plugin_Stop;
        }else {
            StandardizeTime(KickTime.FloatValue - KickLookOnPlayer[client], cRunTime);
            PrintHintText(client, "非VIP玩家,你将会在 %s 后被踢出游戏.", cRunTime);
        }
        KickLookOnPlayer[client]++;
        return Plugin_Continue;
    }else{
        KickPlayerTimer[client] = null;
        KickLookOnPlayer[client] = 0;
        return Plugin_Stop;
    }
}

public void Event_PlayerDisconnect(Event event, const char[] name, bool dontBroadcast) {
    int client = GetClientOfUserId(event.GetInt("userid"));
    if (client && !IsFakeClient(client))
        delete KickPlayerTimer[client];

}

public void Event_RoundEnd(Event event, const char[] name, bool dontBroadcast) {
	OnMapEnd();
}

public void OnMapEnd() {
	for (int i = 1; i <= MaxClients; i++)
		delete KickPlayerTimer[i];

}

void StandardizeTime(float time, char str[32]) {
	char sD[32], sH[32], sM[32], sS[32];
	float remainder = time;

	int D = RoundToFloor(remainder / 86400.0);
	remainder = remainder - float(D * 86400);
	int H = RoundToFloor(remainder / 3600.0);
	remainder = remainder - float(H * 3600);
	int M = RoundToFloor(remainder / 60.0);
	remainder = remainder - float(M * 60);
	int S = RoundToFloor(remainder);

	Format(sD, sizeof(sD), "%d天", D);
	Format(sH, sizeof(sH), "%d%s", H, !D && !M && !S ? "小时" : "时");
	Format(sM, sizeof(sM), "%d%s", M, !D && !H && !S ? "分钟" : "分");
	Format(sS, sizeof(sS), "%d秒", S);
	FormatEx(str, sizeof(str), "%s%s%s%s", !D ? "" : sD, !H ? "" : sH, !M ? "" : sM, !S ? "" : sS);
}

int GetSurvivorLimit() {
	static int g_iMaxcl = 0;
	static Handle invalid = null, downtownrun = null, toolzrun = null;
	downtownrun = FindConVar("l4d_maxplayers");
	toolzrun	= FindConVar("sv_maxplayers");
	if (downtownrun != (invalid))
	{
		int downtown = (GetConVarInt(FindConVar("l4d_maxplayers")));
		if (downtown >= 1)
			g_iMaxcl = (GetConVarInt(FindConVar("l4d_maxplayers")));
	}
	if (toolzrun != (invalid))
	{
		int toolz = (GetConVarInt(FindConVar("sv_maxplayers")));
		if (toolz >= 1)
			g_iMaxcl = (GetConVarInt(FindConVar("sv_maxplayers")));
	}
	if (downtownrun == (invalid) && toolzrun == (invalid))
		g_iMaxcl = (MaxClients);

	return g_iMaxcl;
}

int GetAllPlayerCount() {
	int count = 0;
	for (int i = 1; i <= MaxClients; i++)
		if (IsClientConnected(i) && !IsFakeClient(i))
				count++;
	
	return count;
}

public void OnClientPostAdminCheck(int client) {
    if (IsFakeClient(client))
        return;

    if (!!KickStatus.IntValue && !!KickFull.IntValue)
        RequestFrame(IsGetPlayerNumber);

}

void IsGetPlayerNumber() {
    if (GetAllPlayerCount() == GetSurvivorLimit()) {
        for(int i = 1; i <= MaxClients; i++) {
            if (IsClientInGame(i) && GetClientTeam(i) == 1 && !IsFakeClient(i) && !IsVip(i)) {
                KickPlayer(i);
            }
        }
    }
}