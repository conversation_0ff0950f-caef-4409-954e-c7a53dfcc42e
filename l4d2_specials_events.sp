#pragma semicolon 1
#pragma newdecls required

#include <sourcemod>
#include <sdktools>
#include <sdkhooks>
#include <left4dhooks>

public Plugin myinfo = 
{
	name = "L4D2 Specials Events",
	description = "L4D2 Specials Events",
	author = "灵现江湖",
	version = "1.0",
	url = "qq 791347186"
};

bool g_bLeftStartArea;
Handle g_hTimer_SpecialEvents;
Handle g_hTimer_ThreePanic;
Handle g_hTimer_BeDizzy;
int g_iSprite;
int g_iUpdateRainNums;
int g_iUpdatePanicNums;
bool g_bFriendLyFire;
bool g_bIsDamaged[MAXPLAYERS + 1];
bool g_bBeDizzy;
bool g_bExplodeInfected;
bool g_bFireInfected;
bool g_bRapidJockey;
bool g_bStealthSmoker;
bool g_bStealthSpitter;
bool g_bIsMYZZ;
bool g_bStealthBoomer;
bool g_bIsZEPZ;
bool g_bIsShouMangJiaoLuan;
bool g_bIsSpitterControl;
bool g_bIsTankControl;
bool g_bIsWitchRbirth;
bool g_bIsHunterHeal;
bool g_bIsCommonPunch;
bool g_bIsSmokerPunch;
bool g_bIsChargerDamage;
bool g_bIsTankPunch;
bool g_bIsHardTank;
bool g_bIsFenShen;
bool g_bIsSpitterDod[MAXPLAYERS + 1];
bool g_bIsTimerBomb[MAXPLAYERS + 1];
bool g_bIsAddHeaklthTank[MAXPLAYERS + 1];
bool g_bIsAmmoLose[MAXPLAYERS + 1];
bool g_bIsFriendLyFireDeath[MAXPLAYERS + 1];
bool g_bIsDrugCircle[MAXPLAYERS + 1];
bool g_bIsReviveDamage[MAXPLAYERS + 1];
bool g_bIsShovedDamage;
bool g_bIsIncapKongju;
bool g_bIsBoomerBomb;
int g_iOld_zombie_player_l;
int g_iOld_zombie_minion_l;
int g_iOld_boomer_limit_l;
char g_sSpawnZombieClass[9][32] = 
{
	"z_spawn",
	"z_spawn smoker",
	"z_spawn boomer",
	"z_spawn hunter",
	"z_spawn spitter",
	"z_spawn jockey",
	"z_spawn charger",
	"z_spawn witch",
	"z_spawn tank"
};
char g_sModel[4][64] = {"models/infected/smoker.mdl", "models/infected/boomer.mdl", "models/infected/hunter.mdl", "models/infected/spitter.mdl"};
char g_sFire[4][32] = {"!self", "prop_dynamic", "prop_physics", "prop_ragdoll"};

Handle g_hTimer[MAXPLAYERS+1];
Handle g_hForward;
Handle g_hTimer_LeftStart;

public void OnPluginStart()
{
	HookEvent("player_death", Event_PlayerDeath);
	HookEvent("jockey_ride", Event_JockeyRide);
	HookEvent("player_shoved", PlayerShoved);
	HookEvent("revive_success", eReviveSuccess);
	HookEvent("player_hurt", Event_PlayerHurt);
	HookEvent("player_incapacitated", Event_PlayerIncapacitated);
	HookEvent("round_start", OnRoundStart);
	HookEvent("player_now_it", Event_PlayerNowIt);
	HookEvent("player_boomed", Event_PlayerBoomed);
	HookEvent("mission_lost", Event_MissionLost);

	RegAdminCmd("sm_test", Test, ADMFLAG_ROOT);
	g_hTimer_SpecialEvents = CreateTimer(60.0, SpecialEvents, _, TIMER_REPEAT);
	g_iSprite = PrecacheModel("materials/sprites/laser.vmt");
	CreateConVar("l4d2_specials_events", "1.0", "特殊事件", FCVAR_NOTIFY|FCVAR_REPLICATED);
	
	for (int i = 1; i <= MaxClients; i++)
	{
		if (IsClientInGame(i))
		{
			OnClientPutInServer(i);
		}
	}
	g_hTimer_LeftStart = CreateTimer(1.0, PlayerLeftStart, _, TIMER_REPEAT);
}

public void OnMapStart()
{
	g_iSprite = PrecacheModel("materials/sprites/laserbeam.vmt");
	PrecacheModel("models/infected/common_male_clown.mdl");
	PrecacheModel("models/infected/common_male_riot.mdl");
	PrecacheModel("models/infected/common_male_mud.mdl");
	PrecacheModel("models/infected/limbs/exploded_boomer.mdl");
	PrecacheModel("models/props_junk/gascan001a.mdl");
	PrecacheModel("models/props_junk/propanecanister001a.mdl");
	PrecacheModel("models/props/cs_militia/silo_01.mdl");
	PrecacheSound("physics/destruction/smash_cave_woodrockcollapse2.wav");
	PrecacheSound("player/hunter/hit/tackled_1.wav");
	PrecacheSound("vo/npc/male01/no01.wav");
	PrecacheSound("vo/npc/male01/no02.wav");
	PrecacheSound("vo/npc/male01/no03.wav");
}

public Action Test(int client, int args)
{
	ExecSpecialEvents();
	return Plugin_Handled;
}

public void OnClientPutInServer(int client)
{
	g_bIsDamaged[client] = false;
	SDKHook(client, SDKHook_OnTakeDamage, OnTakeDamage);
	SDKHook(client, SDKHook_StartTouch, OnStartTouch);
}

public void OnStartTouch(int entity, int other)
{
}

public void OnEntityDestroyed(int entity)
{
	char classname[64];
	GetEdictClassname(entity, classname, sizeof(classname));

	if (StrEqual(classname, "prop_physics_override"))
	{
		return;
	}

	int client = GetEntPropEnt(entity, Prop_Send, "m_hOwnerEntity");
	if (client <= 0 || client > MaxClients || !IsClientInGame(client))
	{
		client = GetClientOfUserId(entity);
		if (client <= 0 || client > MaxClients)
		{
			return;
		}
	}

	if (g_bIsSpitterDod[client])
	{
		g_bIsSpitterDod[client] = false;
	}
	if (g_bIsTimerBomb[client])
	{
		g_bIsTimerBomb[client] = false;
	}
	if (g_bIsAddHeaklthTank[client])
	{
		g_bIsAddHeaklthTank[client] = false;
	}
	if (g_bIsAmmoLose[client])
	{
		g_bIsAmmoLose[client] = false;
	}
	if (g_bIsFriendLyFireDeath[client])
	{
		g_bIsFriendLyFireDeath[client] = false;
	}
	if (g_bIsSpitterControl)
	{
		g_bIsSpitterControl = false;
	}
	if (g_bIsDrugCircle[client])
	{
		g_bIsDrugCircle[client] = false;
	}
	if (g_bIsReviveDamage[client])
	{
		g_bIsReviveDamage[client] = false;
	}
	
	if (g_bIsHardTank)
	{
		g_bIsHardTank = false;
	}
	if (g_bIsMYZZ)
	{
		g_bIsMYZZ = false;
	}
	if (g_bIsWitchRbirth)
	{
		g_bIsWitchRbirth = false;
	}
}

public Action OnPlayerRunCmd(int client, int &buttons, int &impulse, float vel[3], float angles[3], int &weapon, int &subtype, int &cmd, int &move)
{
	if (!IsSurvivor(client))
	{
		return Plugin_Continue;
	}

	if (g_bIsShouMangJiaoLuan)
	{
		int activeWeapon = GetEntPropEnt(client, Prop_Send, "m_hActiveWeapon");
		if (activeWeapon != -1 && GetEntProp(activeWeapon, Prop_Send, "m_bInReload"))
		{
			SetEntPropFloat(client, Prop_Send, "m_flLaggedMovementValue", 0.3);
		}
	}

	if (!g_bBeDizzy)
	{
		return Plugin_Continue;
	}

	if (buttons & IN_BACK)
	{
		buttons &= ~IN_BACK;
		buttons |= IN_FORWARD;
	}
	else if (buttons & IN_FORWARD)
	{
		buttons &= ~IN_FORWARD;
		buttons |= IN_BACK;
	}

	if (buttons & IN_MOVELEFT)
	{
		buttons &= ~IN_MOVELEFT;
		buttons |= IN_MOVERIGHT;
	}
	else if (buttons & IN_MOVERIGHT)
	{
		buttons &= ~IN_MOVERIGHT;
		buttons |= IN_MOVELEFT;
	}
	
	return Plugin_Changed;
}

public Action OnRoundStart(Event event, const char[] name, bool dontBroadcast)
{
	g_bFriendLyFire = false;
	g_bBeDizzy = false;
	g_bFireInfected = false;
	g_bIsTankControl = false;
	g_bIsShovedDamage = false;
	g_bIsFenShen = false;
	g_bIsIncapKongju = false;
	g_bIsFriendLyFireDeath = false;

	SetConVarInt(FindConVar("survivor_limp_health"), 40);
	SetConVarInt(FindConVar("sv_disable_glow_survivors"), 0);
	SetConVarInt(FindConVar("z_claw_hit_pitch_min"), -40);
	SetConVarInt(FindConVar("z_claw_hit_pitch_max"), 40);
	SetConVarInt(FindConVar("z_witch_always_kills"), 0);

	if (g_hTimer_SpecialEvents != null)
	{
		KillTimer(g_hTimer_SpecialEvents);
		g_hTimer_SpecialEvents = null;
	}
	if (g_hTimer_ThreePanic != null)
	{
		KillTimer(g_hTimer_ThreePanic);
		g_hTimer_ThreePanic = null;
	}
	if (g_hTimer_BeDizzy != null)
	{
		KillTimer(g_hTimer_BeDizzy);
		g_hTimer_BeDizzy = null;
	}
	
	g_bLeftStartArea = false;
	CreateTimer(1.0, Timer_CheckLeftStart, _, TIMER_REPEAT);
	
	return Plugin_Continue;
}

public Action PlayerLeftStart(Handle timer)
{
	if (!g_bLeftStartArea)
	{
		int i;
		for (i = 1; i <= MaxClients; i++)
		{
			if (IsClientInGame(i) && GetClientTeam(i) == 2 && GetEntProp(i, Prop_Send, "m_bInExitArea") == 0)
			{
				if (GetEntProp(FindEntityByClassname(-1, "info_player_start_l4d2"), Prop_Data, "m_bDisabled") == 1)
				{
					g_bLeftStartArea = true;
					float rnd_timer = GetRandomFloat(160.0, 230.0);
					g_hTimer_SpecialEvents = CreateTimer(rnd_timer, SpecialEvents);
					return Plugin_Stop;
				}
			}
		}
	}
	return Plugin_Continue;
}

public Action Timer_CheckLeftStart(Handle timer)
{
	if (!g_bLeftStartArea)
	{
		int i;
		for (i = 1; i <= MaxClients; i++)
		{
			if (IsClientInGame(i) && GetClientTeam(i) == 2 && GetEntProp(i, Prop_Send, "m_bInExitArea") == 0)
			{
				if (GetEntProp(FindEntityByClassname(-1, "info_player_start_l4d2"), Prop_Data, "m_bDisabled") == 1)
				{
					g_bLeftStartArea = true;
					float rnd_timer = GetRandomFloat(160.0, 230.0);
					g_hTimer_SpecialEvents = CreateTimer(rnd_timer, SpecialEvents);
					return Plugin_Stop;
				}
			}
		}
	}
	return Plugin_Continue;
}

public Action SpecialEvents(Handle timer)
{
	g_hTimer_SpecialEvents = null;
	ExecSpecialEvents();
	float rnd_timer = GetRandomFloat(160.0, 230.0);
	g_hTimer_SpecialEvents = CreateTimer(rnd_timer, SpecialEvents);
	return Plugin_Stop;
}

public Action DoJump(Handle timer, any userid)
{
	if (g_bRapidJockey)
	{
		int client = GetClientOfUserId(userid);
		if (client > 0 && IsClientInGame(client) && GetClientTeam(client) == 3 && IsPlayerAlive(client))
		{
			int victim = GetEntPropEnt(client, Prop_Send, "m_jockeyVictim");
			if (victim > 0 && IsClientInGame(victim) && IsPlayerAlive(victim) && client == GetEntPropEnt(victim, Prop_Send, "m_jockeyAttacker"))
			{
				if (GetEntPropEnt(victim, Prop_Send, "m_hGroundEntity") == -1)
				{
					float vel[3];
					GetEntPropVector(victim, Prop_Data, "m_vecBaseVelocity", vel);
					vel[2] += 610.0;
					TeleportEntity(victim, NULL_VECTOR, NULL_VECTOR, vel);
					CreateTimer(GetRandomFloat(0.1, 0.5), DoJump, userid);
				}
			}
		}
	}
	return Plugin_Stop;
}

public void Event_MissionLost(Event event, const char[] name, bool dontBroadcast)
{
	for (int i = 1; i <= MaxClients; i++)
	{
		g_bIsSpitterDod[i] = false;
		g_bIsTimerBomb[i] = false;
		g_bIsAddHeaklthTank[i] = false;
		g_bIsAmmoLose[i] = false;
		g_bIsFriendLyFireDeath[i] = false;
		g_bIsDrugCircle[i] = false;
		g_bIsReviveDamage[i] = false;
	}
	g_bStealthSmoker = false;
	g_bIsSpitterControl = false;
	g_bStealthSpitter = false;
	g_bStealthBoomer = false;
	g_bIsHardTank = false;
	g_bIsMYZZ = false;
	g_bIsWitchRbirth = false;
	g_bExplodeInfected = false;
	SetConVarInt(FindConVar("survivor_incap_health"), 300);
	SetConVarInt(FindConVar("survivor_ledge_grab_health"), 100);
	SetConVarInt(FindConVar("survivor_revive_health"), 30);
	SetConVarInt(FindConVar("survivor_revive_duration"), 5);
	SetConVarInt(FindConVar("defibrillator_use_duration"), 3);
	PrintToChatAll("所有玩家已死亡，事件难度将降低");
}

void TriggerMultipleDamage(int entity, float vPos[3], float vMaxs[3], float vMins[3])
{
	int trigger = CreateEntityByName("trigger_multiple");
	DispatchKeyValue(trigger, "spawnflags", "1");
	DispatchKeyValue(trigger, "wait", "1");
	DispatchKeyValue(trigger, "StartDisabled", "0");
	
	float Mins[3], Maxs[3];
	Mins = vMins;
	Maxs = vMaxs;
	
	TeleportEntity(trigger, vPos, NULL_VECTOR, NULL_VECTOR);
	SetEntPropVector(trigger, Prop_Send, "m_vecMins", Mins);
	SetEntPropVector(trigger, Prop_Send, "m_vecMaxs", Maxs);
	
	DispatchSpawn(trigger);
	ActivateEntity(trigger);
	
	SetVariantString("!activator");
	AcceptEntityInput(trigger, "SetParent", entity);
	SDKHook(trigger, SDKHook_StartTouch, OnTouchTriggerFreezer);
}

public void OnTouchTriggerFreezer(int entity, int other)
{
	if (entity > 0 && IsValidEntity(entity) && IsSurvivor(other) && IsPlayerAlive(other))
	{
		int parent = GetEntPropEnt(entity, Prop_Send, "moveparent");
		DataPack dp;
		CreateDataPack(dp);
		WritePackCell(dp, GetClientUserId(other));
		WritePackCell(dp, EntIndexToEntRef(parent));
		CreateTimer(10.0, Timer_UnFreeze, dp);
		
		SDK_DamageTarget(other, 5, DMG_GENERIC, other);
		PrintToChatAll("\x01 %N 踩中了胖胖死亡掉落的陷阱,被禁锢了!", other);
		
		SDKUnhook(entity, SDKHook_StartTouch, OnTouchTriggerFreezer);
		AcceptEntityInput(entity, "Kill");
		
		SetEntityMoveType(other, MOVETYPE_NONE);
	}
}

void ExecSpecialEvents()
{
	for (int i = 1; i <= MaxClients; i++)
	{
		if (IsSurvivor(i))
		{
			char sound[64];
			Format(sound, sizeof(sound), "vo/npc/male01/no0%d.wav", GetRandomInt(1,3));
			EmitSoundToAll(sound, i, SNDCHAN_VOICE, SNDLEVEL_NORMAL, _, _, _, _, NULL_VECTOR, NULL_VECTOR, true, 0.0);
		}
	}
	
	int rand = GetRandomInt(1, 60);
	switch (rand)
	{
		case 1: { FallBoomer(); }
		case 2: { ThreePanic(); }
		case 3: { SpawnTank(); }
		case 4: { SpawnWitch(); }
		case 5: { FriendLyFire(); }
		case 6: { NoHeal(); }
		case 7: { BeDizzy(); }
		case 8: { ExplodeInfected(); }
		case 9: { DeleteAmmo(); }
		case 10: { FireInfected(); }
		case 11: { SlowlyMan(); }
		case 12: { StealthSpitter(); }
		case 13: { SmokerHp(); }
		case 14: { ZombieStronger(); }
		case 15: { DaoDi(); }
		case 16: { WitchKill(); }
		case 17: { HappyCharger(); }
		case 18: { HappySpitter(); }
		case 19: { FriendlyHit(); }
		case 20: { HappySmoker(); }
		case 21: { HappyBoomer(); }
		case 22: { RealMode(); }
		case 23: { HardRevive(); }
		case 24: { RapidJockey(); }
		case 25: { StealthSmoker(); }
		case 26: { HardHeal(); }
		case 27: { BommerHp(); }
		case 28: { ChargerHp(); }
		case 29: { JockeyHp(); }
		case 30: { HunterHp(); }
		case 31: { LessHope(); }
		case 32: { HeavySpiter(); }
		case 33: { BackAttact(); }
		case 34: { MoreZombie(); }
		case 35: { FireTank(); }
		case 36: { ZombiePunch(); }
		case 37: { TankHp(); }
		case 38: { MYZZ(); }
		case 39: { ZEPZ(); }
		case 40: { ChargerSpeed(); }
		case 41: { StealthBoomer(); }
		case 42: { HunterRush(); }
		case 43: { SpitterDod(); }
		case 44: { TimerBomb(); }
		case 45: { AddHeaklthTank(); }
		case 46: { DrugCircle(); }
		case 47: { Headshotmode(); }
		case 48: { HealDown(); }
		case 49: { AmmoLose(); }
		case 50: { FriendLyFireDeath(); }
		case 51: { IncapKongju(); }
		case 52: { BoomerBomb(); }
		case 53: { WenYiTank(); }
		case 54: { SpitterControl(); }
		case 55: { TankControl(); }
		case 56: { WithRbirth(); }
		case 57: { HunterHeal(); }
		case 58: { CommonPunch(); }
		case 59: { HardTank(); }
		case 60: { FenShen(); }
	}
}

void FenShen()
{
	g_bIsFenShen = true;
	PrintToChatAll("☠触发特殊事件:影流之主（僵尸攻击到玩家会额外刷新一只新的小僵尸在身边，持续一局）");
}

void HardTank()
{
	g_bIsHardTank = true;
	PrintToChatAll("☠触发永久特殊事件:皮糙肉厚（tank受到低于30的攻击只会承受百分之10伤害(团灭后事件消失)）");
}

void CommonPunch()
{
	g_bIsCommonPunch = true;
	PrintToChatAll("☠触发永久特殊事件:拳拳到肉（小僵尸的攻击伤害大幅提升）");
}

void HunterHeal()
{
	g_bIsHunterHeal = true;
	PrintToChatAll("☠触发永久特殊事件:猎取养分（hunter的每次攻击，会使场中所有特感恢复50hp）");
}

void WithRbirth()
{
	g_bIsWitchRbirth = true;
	PrintToChatAll("☠触发永久特殊事件:巫蛊坦克（坦克每次击杀一个玩家，会生成一只额外的女巫(团灭后事件消失)）");
}

void TankControl()
{
	g_bIsTankControl = true;
	PrintToChatAll("☠触发特殊事件:地动山摇（坦克每次攻击到一个玩家，所有玩家都会因此无法站稳被硬直，持续一局(提示：在空中可以规避硬直)）");
}

void SpitterControl()
{
	g_bIsSpitterControl = true;
	PrintToChatAll("☠触发永久特殊事件:润滑毒液（口水的攻击会使玩家脚滑失去控制(团灭后事件消失)）");
}

void WenYiTank()
{
	PrintToChatAll("☠触发永久特殊事件:瘟疫坦克（坦克会对1.6米内的玩家持续造成伤害，每3秒扣除5hp）");
	CreateTimer(3.0, Timer_WenYiTank, _, TIMER_REPEAT);
}

void BoomerBomb()
{
	g_bIsBoomerBomb = true;
	PrintToChatAll("☠触发永久特殊事件:爆裂胖胖（Boomer死亡会造成额外的爆炸伤害，扣除20hp）");
}

void IncapKongju()
{
	g_bIsIncapKongju = true;
	PrintToChatAll("☠触发特殊事件:晕头转向（每次玩家倒地会使所有其他玩家操作打乱两秒钟，持续一局）");
}

void FriendLyFireDeath()
{
	g_bIsFriendLyFireDeath = true;
	SetConVarInt(FindConVar("survivor_friendly_fire_factor_normal"), 2);
	PrintToChatAll("☠触发特殊事件:生命连锁（玩家在本局杀死队友后自己也会死亡，且黑枪的伤害小幅度增加）");
}

void AmmoLose()
{
	g_bIsAmmoLose = true;
	PrintToChatAll("☠触发永久特殊事件:弹药窃取 (僵尸攻击到的玩家会有20概率使其武器子弹减少10发(团灭后事件消失))");
}

void HealDown()
{
	for (int i = 1; i <= MaxClients; i++)
	{
		if (IsSurvivor(i))
		{
			for (int j = 3; j < 5; j++)
			{
				int ent = GetPlayerWeaponSlot(i, j);
				if (ent > 0 && IsValidEntity(ent))
				{
					char sActiveWeapon[64];
					GetEntityClassname(ent, sActiveWeapon, sizeof(sActiveWeapon));
					if (strcmp(sActiveWeapon, "weapon_pain_pills", false) == 0)
					{
						RemovePlayerItem(i, ent);
						AcceptEntityInput(ent, "Kill");
					}
					else if (strcmp(sActiveWeapon, "weapon_adrenaline", false) == 0)
					{
						RemovePlayerItem(i, ent);
						AcceptEntityInput(ent, "Kill");
					}
				}
			}
		}
	}
	SetConVarInt(FindConVar("pain_pills_health_value"), 30);
	SetConVarInt(FindConVar("adrenaline_health_buffer"), 20);
	PrintToChatAll("☠触发永久特殊事件:治疗弱化(肾上腺素和止痛药全部将损坏，且止痛药和肾上腺素的回复量降低)");
}

void Headshotmode()
{
	g_bIsShovedDamage = true;
	PrintToChatAll("☠触发特殊事件:疼痛贴贴(玩家推到特感和队友时会收到2点伤害,持续一局)");
}

void DrugCircle()
{
	g_bIsDrugCircle = true;
	PrintToChatAll("☠触发永久特殊事件:毒雾笼罩(特感死亡后有小概率生成烟雾(团灭后事件消失))");
}

void TimerBomb()
{
	g_bIsTimerBomb = true;
	PrintToChatAll("☠触发永久特殊事件:定时炸弹(玩家死亡的时候会原地生成毒液或者胆汁爆炸(团灭后事件消失))");
}

void AddHeaklthTank()
{
	g_bIsAddHeaklthTank = true;
	PrintToChatAll("☠触发永久特殊事件:生命之拳(tank攻击到玩家每次恢复自身250hp(团灭后事件消失))");
}

void SpitterDod()
{
	g_bIsSpitterDod = true;
	PrintToChatAll("☠触发永久特殊事件:骑手保护(jockey造成伤害时获得3秒的无敌(团灭后事件消失)）");
}

void HunterRush()
{
	SetConVarInt(FindConVar("z_pounce_stumble_radius"), 120);
	PrintToChatAll("☠触发永久特殊事件:肉弹冲击（Hunter和Jockey控制玩家时会对附近幸存者造成硬直）");
}

void StealthBoomer()
{
	g_bStealthBoomer = true;
	PrintToChatAll("☠触发永久特殊事件:忍者胖胖(bommer受伤之后会进入隐身状态(团灭后事件消失))");
}

void ChargerSpeed()
{
	SetConVarInt(FindConVar("z_charge_max_force"), 1050);
	SetConVarInt(FindConVar("z_charge_min_force"), 850);
	SetConVarInt(FindConVar("z_charge_max_speed"), 12000);
	SetConVarInt(FindConVar("z_charge_warmup"), 1);
	SetConVarInt(FindConVar("z_charge_duration"), 100);
	SetConVarInt(FindConVar("z_charge_impact_radius"), 150);
	PrintToChatAll("☠触发永久特殊事件:大力神牛（charger技能持续时间极大幅度增加，撞击力度增加）");
}

void ZEPZ()
{
	g_bIsZEPZ = true;
	PrintToChatAll("☠触发永久特殊事件:憎恶胖子（boomer死亡后会掉落一个陷阱,玩家经过会扣5血并禁锢10s）");
}

void MYZZ()
{
	g_bIsMYZZ = true;
	PrintToChatAll("☠触发永久特殊事件:梦魇之爪（特感普通攻击会让幸存者持续5秒流血效果(团灭后事件消失)）");
}

void TankHp()
{
	SetConVarInt(FindConVar("z_tank_speed"), 300);
	g_bIsTankPunch = true;
	PrintToChatAll("☠触发永久特殊事件:狂暴坦克（tank的速度小幅度提升，且攻击伤害大幅提升）");
}

void ZombiePunch()
{
	SetConVarInt(FindConVar("z_claw_hit_pitch_max"), 80);
	SetConVarInt(FindConVar("z_claw_hit_pitch_min"), -80);
	PrintToChatAll("☠触发特殊事件:沉重打击（玩家更容易被打晃视角，持续一局）");
}

void FireTank()
{
	SetConVarInt(FindConVar("tank_burn_duration"), 300);
	SetConVarInt(FindConVar("z_special_burn_dmg_scale"), 1);
	PrintToChatAll("☠触发永久特殊事件:火抗特感（所有特感的燃烧抗性提升）");
}

void MoreZombie()
{
	g_bIsReviveDamage = true;
	PrintToChatAll("☠触发永久特殊事件:刺痛救援（玩家救起队友后自身承受5点伤害(团灭后事件消失)）");
}

void BackAttact()
{
	g_bIsChargerDamage = true;
	PrintToChatAll("☠触发永久特殊事件:暴击神拳（牛牛攻击时有50概率会造成双倍伤害）");
}

void HeavySpiter()
{
	SetConVarInt(FindConVar("z_spitter_health"), 500);
	SetConVarInt(FindConVar("z_spitter_speed"), 470);
	PrintToChatAll("☠触发永久特殊事件:强化口水（spitter的生命和速度提升）");
}

void LessHope()
{
	SetConVarInt(FindConVar("survivor_incap_health"), 150);
	SetConVarInt(FindConVar("survivor_ledge_grab_health"), 70);
	SetConVarInt(FindConVar("survivor_revive_health"), 25);
	PrintToChatAll("☠触发永久特殊事件:难以支撑（幸存者倒地、挂边和被救起的血量降低了(团灭后事件消失)）");
}

void HunterHp()
{
	SetConVarInt(FindConVar("z_hunter_health"), 500);
	SetConVarInt(FindConVar("z_hunter_speed"), 410);
	PrintToChatAll("☠触发永久特殊事件:强化猎人（hunter的生命值和速度提升）");
}

void JockeyHp()
{
	SetConVarInt(FindConVar("z_jockey_health"), 500);
	SetConVarInt(FindConVar("z_jockey_speed"), 370);
	SetConVarInt(FindConVar("z_jockey_ride_damage"), 3);
	PrintToChatAll("☠触发永久特殊事件:强化猴猴（jockey的生命值和速度提升,抓住玩家造成伤害增加）");
}

void ChargerHp()
{
	SetConVarInt(FindConVar("z_charger_health"), 800);
	PrintToChatAll("☠触发永久特殊事件:肉化牛牛（charger的生命值提升）");
}

void BommerHp()
{
	SetConVarInt(FindConVar("z_exploding_health"), 500);
	PrintToChatAll("☠触发永久特殊事件:肉化胖胖（bommer的生命值大幅度提升）");
}

void HardHeal()
{
	SetConVarInt(FindConVar("first_aid_kit_use_duration"), 10);
	PrintToChatAll("☠触发永久特殊事件:缓慢治疗(玩家需要使用医疗包的时间大幅度提升)");
}

void StealthSmoker()
{
	g_bStealthSmoker = true;
	PrintToChatAll("☠触发永久特殊事件:硬直反弹(hunter被推后会反弹硬直效果给该玩家(团灭后事件消失))");
}

void RapidJockey()
{
	g_bRapidJockey = true;
	PrintToChatAll("☠触发永久特殊事件:深度控制(Jockey抓住幸存者之后会使用跳跃)");
}

void HardRevive()
{
	SetConVarInt(FindConVar("survivor_revive_duration"), 10);
	SetConVarInt(FindConVar("defibrillator_use_duration"), 10);
	SetConVarInt(FindConVar("upgrade_pack_use_duration"), 5);
	PrintToChatAll("☠触发永久特殊事件:沉重负担(救起队友和安放弹药包需要的时间增加(团灭后事件消失))");
}

void RealMode()
{
	SetConVarInt(FindConVar("sv_disable_glow_survivors"), 1);
	PrintToChatAll("☠触发特殊事件:真实模式(玩家的轮廓将消失，持续一局)");
}

void HappyBoomer()
{
	SetConVarInt(FindConVar("z_exploding_inner_radius"), 240);
	SetConVarInt(FindConVar("z_exploding_outer_radius"), 275);
	PrintToChatAll("☠触发永久特殊事件:胆汁扩散(bommer自爆范围大幅度增强)");
}

void HappySmoker()
{
	SetConVarInt(FindConVar("tongue_miss_delay"), 2);
	SetConVarInt(FindConVar("tongue_hit_delay"), 2);
	SetConVarInt(FindConVar("tongue_range"), 3000);
	PrintToChatAll("☠触发永久特殊事件:王牌狙击(smoker技能CD缩短，距离大幅度增强)");
}

void FriendlyHit()
{
	SetConVarInt(FindConVar("survivor_friendly_fire_factor_normal"), 3);
	PrintToChatAll("☠触发特殊事件:友军之围(黑枪队友的伤害大幅度增加了，持续一局)");
}

void SlowlyMan()
{
	SetConVarInt(FindConVar("survivor_limp_health"), 500);
	PrintToChatAll("☠触发特殊事件:行动缓慢(玩家的移速下降，持续一局)");
}

void HappySpitter()
{
	SetConVarInt(FindConVar("z_spit_interval"), 3);
	SetConVarInt(FindConVar("z_spit_range"), 200);
	PrintToChatAll("☠触发永久特殊事件:口水狂欢(spitter的技能范围缩小，技能CD大幅度降低)");
}

void HappyCharger()
{
	SetConVarInt(FindConVar("z_charge_interval"), 1);
	PrintToChatAll("☠触发永久特殊事件:快乐牛牛(charger的冲撞技能CD大幅度降低)");
}

void WitchKill()
{
	SetConVarInt(FindConVar("z_witch_always_kills"), 1);
	SetConVarInt(FindConVar("z_witch_speed"), 9999);
	SetConVarInt(FindConVar("z_witch_speed_inured"), 9999);
	SetConVarInt(FindConVar("z_witch_health"), 2800);
	PrintToChatAll("☠触发特殊事件:女巫狂怒(witch的生命和速度永久增加，并且会在本局一下击杀玩家)");
}

void DaoDi()
{
	g_bIsSmokerPunch = true;
	PrintToChatAll("☠触发永久特殊事件:强力触手(舌头的伤害增加)");
}

void ZombieStronger()
{
	SetConVarInt(FindConVar("z_speed"), 300);
	SetConVarInt(FindConVar("z_health"), 70);
	SetConVarInt(FindConVar("z_door_pound_damage"), 300);
	PrintToChatAll("☠触发永久特殊事件:血月升起(所有僵尸的生命和速度都小幅提升了...)");
}

void SmokerHp()
{
	SetConVarInt(FindConVar("z_gas_health"), 500);
	SetConVarInt(FindConVar("tongue_health"), 50);
	SetConVarInt(FindConVar("z_gas_speed"), 450);
	SetConVarInt(FindConVar("tongue_victim_max_speed"), 1000);
	PrintToChatAll("☠触发永久特殊事件:强化舌头（smoker生命值和速度提升,并且可以一瞬间拽走玩家）");
}

void StealthSpitter()
{
	g_bStealthSpitter = true;
	PrintToChatAll("☠触发永久特殊事件:隐匿射手(spitter被攻击之后会进入隐匿状态(团灭后事件消失))");
}

void FireInfected()
{
	g_bFireInfected = true;
	PrintToChatAll("☠触发特殊事件:自焚特感（特感死后有概率产生火焰，持续一局）");
}

void DeleteAmmo()
{
	SetConVarInt(FindConVar("ammo_assaultrifle_max"), 400);
	SetConVarInt(FindConVar("ammo_autoshotgun_max"), 120);
	SetConVarInt(FindConVar("ammo_shotgun_max"), 120);
	SetConVarInt(FindConVar("ammo_huntingrifle_max"), 70);
	SetConVarInt(FindConVar("ammo_grenadelauncher_max"), 20);
	SetConVarInt(FindConVar("ammo_smg_max"), 500);
	PrintToChatAll("☠触发永久特殊事件:弹药紧缺（武器的后备弹药上限降低）");
}

void ExplodeInfected()
{
	g_bExplodeInfected = true;
	PrintToChatAll("☠触发永久特殊事件:自爆特感（特感死后有小概率产生爆炸(团灭后事件消失)）");
}

void BeDizzy()
{
	g_bBeDizzy = true;
	g_hTimer_BeDizzy = CreateTimer(15.0, BeDizzy_Stop);
	PrintToChatAll("☠触发特殊事件:头晕目眩（玩家的方向操控将打乱，持续15秒");
}

public Action BeDizzy_Stop(Handle timer)
{
	g_bBeDizzy = false;
	g_hTimer_BeDizzy = null;
	PrintToChatAll("头晕目眩效果结束");
	return Plugin_Stop;
}

void NoHeal()
{
	for (int i = 1; i <= MaxClients; i++)
	{
		if (IsSurvivor(i))
		{
			for (int j = 3; j < 5; j++)
			{
				int ent = GetPlayerWeaponSlot(i, j);
				if (ent > 0 && IsValidEntity(ent))
				{
					char sActiveWeapon[64];
					GetEntityClassname(ent, sActiveWeapon, sizeof(sActiveWeapon));
					if (strcmp(sActiveWeapon, "weapon_first_aid_kit", false) == 0)
					{
						RemovePlayerItem(i, ent);
						AcceptEntityInput(ent, "Kill");
					}
					else if (strcmp(sActiveWeapon, "weapon_defibrillator", false) == 0)
					{
						RemovePlayerItem(i, ent);
						AcceptEntityInput(ent, "Kill");
					}
				}
			}
		}
	}
	PrintToChatAll("☠触发特殊事件:没有治疗（玩家的医疗包电击器将损坏，且医疗包无法补充生命，持续一局）");
	SetConVarInt(FindConVar("first_aid_heal_percent"), 0);
}

void FriendLyFire()
{
	g_bFriendLyFire = true;
	PrintToChatAll("☠触发特殊事件:队友克星（击中友军将会自己扣15血，持续一局）");
}

void SpawnWitch()
{
	int client = GetRandomSurvivor();
	if (client == 0) return;

	for (int i = 0; i < 5; i++)
	{
		int flags = GetCommandFlags("z_spawn");
		SetCommandFlags("z_spawn", flags & ~ADMFLAG_ROOT);
		FakeClientCommand(client, g_sSpawnZombieClass[7]);
		SetCommandFlags("z_spawn", flags);
	}
	PrintToChatAll("☠触发特殊事件:女巫狂暴（witch将会随机出现在玩家附近!）");
}

void SpawnTank()
{
	int client = GetRandomSurvivor();
	if (client == 0) return;

	float vPos[3];
	bool b_success = false;
	for (int j = 8; j > 0; j--)
	{
		if (L4D_GetRandomPZSpawnPosition(0, j, vPos))
		{
			int tank = L4D2_SpawnTank(vPos, NULL_VECTOR);
			if (tank != -1)
			{
				b_success = true;
				if (IsValidPlayer(tank, true, true))
				{
					SetEntityHealth(tank, 8000);
				}
				PrintToChatAll("☠触发特殊事件:Tank Rush!(已经生成一只8000血的tank...)");
				return;
			}
		}
	}

	if (!b_success)
	{
		int tank = CreateSpecial(client, L4D2ZombieClass_Tank);
		if (IsValidPlayer(tank, true, true))
		{
			SetEntityHealth(tank, 8000);
		}
		PrintToChatAll("☠触发特殊事件:Tank Rush!(已经生成一只8000血的tank...)");
	}
}

void ThreePanic()
{
	g_iUpdatePanicNums = 0;
	TriggerPanicEvent();
	g_hTimer_ThreePanic = CreateTimer(55.0, ThreePanic_Stop, _, TIMER_REPEAT);
	PrintToChatAll("☠触发特殊事件:持续1分钟的尸潮!");
}

public Action ThreePanic_Stop(Handle timer)
{
	g_iUpdatePanicNums++;
	if (g_iUpdatePanicNums > 3)
	{
		g_hTimer_ThreePanic = null;
		PrintToChatAll("持续1分钟的尸潮已停止!");
		return Plugin_Stop;
	}
	TriggerPanicEvent();
	return Plugin_Continue;
}

void TriggerPanicEvent()
{
	int flager = GetRandomSurvivor();
	if (flager == 0)
	{
		return;
	}
	int flag = GetCommandFlags("director_force_panic_event");
	SetCommandFlags("director_force_panic_event", flag & ~ADMFLAG_ROOT);
	FakeClientCommand(flager, "director_force_panic_event");
	SetCommandFlags("director_force_panic_event", flag);
}

void FallBoomer()
{
	g_iUpdateRainNums = 0;
	g_iOld_zombie_player_l = GetConVarInt(FindConVar("z_max_player_zombies"));
	g_iOld_zombie_minion_l = GetConVarInt(FindConVar("z_minion_limit"));
	g_iOld_boomer_limit_l = GetConVarInt(FindConVar("z_boomer_limit"));
	Handle zombie_player_l = FindConVar("z_max_player_zombies");
	SetConVarBounds(zombie_player_l, ConVarBound_Upper, true, 32.0);
	SetConVarBounds(FindConVar("z_max_player_zombies"), ConVarBound_Lower, false, 0.0);
	Handle zombie_minion_l = FindConVar("z_minion_limit");
	SetConVarBounds(zombie_minion_l, ConVarBound_Upper, true, 32.0);
	SetConVarInt(FindConVar("z_boomer_limit"), 32, false);
	CreateTimer(0.2, UpdateRain, _, TIMER_REPEAT);
	PrintToChatAll("☠触发特殊事件:天降boomer!");
}

public Action UpdateRain(Handle timer)
{
	g_iUpdateRainNums++;
	if (g_iUpdateRainNums > 10)
	{
		ConVar zombie_player_l = FindConVar("z_max_player_zombies");
		SetConVarBounds(zombie_player_l, ConVarBound_Upper, true, float(g_iOld_zombie_player_l));
		SetConVarInt(zombie_player_l, g_iOld_zombie_player_l);
		
		ConVar zombie_minion_l = FindConVar("z_minion_limit");
		SetConVarBounds(zombie_minion_l, ConVarBound_Upper, true, float(g_iOld_zombie_minion_l));
		
		SetConVarInt(FindConVar("z_boomer_limit"), g_iOld_boomer_limit_l);
		return Plugin_Stop;
	}

	float hitpos[3];
	int client = GetRandomSurvivor();
	client = SelectAndidata(2, client, hitpos);
	if (client > 0)
	{
		int boomer = CreateSpecial(client, L4D2ZombieClass_Boomer);
		if (boomer > 0)
		{
			SetGodMode(boomer, 5.0);
			TeleportEntity(boomer, hitpos, NULL_VECTOR, NULL_VECTOR);
		}
	}
	return Plugin_Continue;
}

void SetGodMode(int client, float duration)
{
	if (!IsClientInGame(client))
	{
		return;
	}
	SetEntProp(client, Prop_Data, "m_takedamage", 0, 1);
	if (duration > 0.0)
	{
		CreateTimer(duration, Timer_mortal, GetClientUserId(client));
	}
}

public Action Timer_mortal(Handle timer, any client)
{
	if (IsValidClient(client) && IsPlayerAlive(client))
	{
		SetEntityHealth(client, GetEntityHealth(client) - 5);
	}
	return Plugin_Continue;
}

public bool IsValidPlayer(int client, bool AllowBot, bool AllowDeath)
{
	if (client < 1 || client > MaxClients)
	{
		return false;
	}
	if (!IsClientConnected(client) || !IsClientInGame(client))
	{
		return false;
	}
	if (!AllowBot && IsFakeClient(client))
	{
		return false;
	}
	if (!AllowDeath && !IsPlayerAlive(client))
	{
		return false;
	}
	return true;
}

public float GetTempHealth(int client)
{
	return GetEntPropFloat(client, Prop_Send, "m_healthBuffer");
}

public bool TraceRayDontHitSelfAndLive(int entity, int mask, any data)
{
	if (data == entity)
	{
		return false;
	}
	if (entity > 0 && entity <= MaxClients)
	{
		if (IsClientInGame(entity))
		{
			return false;
		}
	}
	return true;
}

void ShowLarserByPos(float pos1[3], float pos2[3], int flag, float life)
{
	int color[4];
	if (flag == 0)
	{
		color = {200, 200, 200, 230};
	}
	else
	{
		color = {200, 0, 0, 230};
	}
	
	TE_SetupBeamPoints(pos1, pos2, g_iSprite, 0, 0, 0, life, 0.5, 0.3, 1, 0.0, color, 0);
	TE_SendToAll();
}

bool IsSurvivor(int client)
{
	return client > 0 && client <= MaxClients && IsClientInGame(client) && GetClientTeam(client) == 2;
}

bool IsInfected(int client)
{
	return client > 0 && client <= MaxClients && IsClientInGame(client) && GetClientTeam(client) == 3;
}

bool IsCommonInfected(int iEntity)
{
	if (iEntity > 0 && IsValidEntity(iEntity))
	{
		char strClassName[64];
		GetEdictClassname(iEntity, strClassName, sizeof(strClassName));
		return strcmp(strClassName, "infected", false) == 0;
	}
	return false;
}

bool IsWitch(int iEntity)
{
	if (iEntity > 0 && IsValidEntity(iEntity))
	{
		char strClassName[64];
		GetEdictClassname(iEntity, strClassName, sizeof(strClassName));
		return strcmp(strClassName, "witch", false) == 0;
	}
	return false;
}

void CheatCommand(int client, const char[] command, const char[] arguments)
{
	if (!client)
	{
		return;
	}
	int admindata = GetUserFlagBits(client);
	SetUserFlagBits(client, ADMFLAG_ROOT);
	int flags = GetCommandFlags(command);
	SetCommandFlags(command, flags & ~FCVAR_CHEAT);
	FakeClientCommand(client, "%s %s", command, arguments);
	SetCommandFlags(command, flags);
	SetUserFlagBits(client, admindata);
}

stock bool IsValidClient(int client)
{
	if (client > 0 && client <= MaxClients && IsClientInGame(client) && !IsFakeClient(client))
	{
		return true;
	}
	return false;
}



stock int SelectAndidata(int team, int client, float hitpos[3])
{
	int selected = 0;
	int andidate[MAXPLAYERS+1];
	int index = 0;
	if (client > 0)
	{
		GetClientAbsOrigin(client, hitpos);
	}
	else
	{
		for (int i = 1; i <= MaxClients; i++)
		{
			if (IsClientInGame(i) && GetClientTeam(i) == team && IsPlayerAlive(i))
			{
				andidate[index++] = i;
			}
		}
		if (index > 0)
		{
			client = andidate[GetRandomInt(0, index - 1)];
			GetClientAbsOrigin(client, hitpos);
		}
	}

	while (index > 0)
	{
		float pos[3];
		GetClientAbsOrigin(andidate[index-1], pos);
		float distance = GetVectorDistance(pos, hitpos);
		if (distance < 200.0)
		{
			selected = andidate[index-1];
			break;
		}
		index--;
	}
	return selected;
}

stock void CreateSpecial(int type, float pos[3], float ang[3])
{
	int client = CreateEntityByName("infected");
	if (client > 0)
	{
		char class[32];
		switch (type)
		{
			case 1: strcopy(class, sizeof(class), "smoker");
			case 2: strcopy(class, sizeof(class), "boomer");
			case 3: strcopy(class, sizeof(class), "hunter");
			case 4: strcopy(class, sizeof(class), "spitter");
			case 5: strcopy(class, sizeof(class), "jockey");
			case 6: strcopy(class, sizeof(class), "charger");
		}
		DispatchKeyValue(client, "vscripts", "ai/infected_special_vs.nut");
		DispatchKeyValue(client, "model", g_sModel[type-1]);
		DispatchSpawn(client);
		SetEntProp(client, Prop_Send, "m_zombieClass", type);
		TeleportEntity(client, pos, ang, NULL_VECTOR);
	}
}

stock void CreateWitch(float pos[3], float ang[3])
{
	int client = CreateEntityByName("infected");
	if (client > 0)
	{
		DispatchKeyValue(client, "vscripts", "ai/infected_special_vs.nut");
		DispatchKeyValue(client, "model", "models/infected/witch.mdl");
		DispatchSpawn(client);
		SetEntProp(client, Prop_Send, "m_zombieClass", 8);
		TeleportEntity(client, pos, ang, NULL_VECTOR);
	}
}

public Action Timer_UnFreeze(Handle timer, DataPack dp)
{
	ResetPack(dp);
	int client = GetClientOfUserId(ReadPackCell(dp));
	int entity = EntRefToEntIndex(ReadPackCell(dp));
	
	if (IsSurvivor(client) && IsPlayerAlive(client))
	{
		SetEntityMoveType(client, MOVETYPE_WALK);
		if (entity > 0 && IsValidEntity(entity))
		{
			AcceptEntityInput(entity, "Kill");
		}
	}
	return Plugin_Stop;
}

stock bool IsSpitter(int client)
{
	return GetEntProp(client, Prop_Send, "m_zombieClass") == L4D2ZombieClass_Spitter;
}

stock bool IsJockey(int client)
{
	return GetEntProp(client, Prop_Send, "m_zombieClass") == L4D2ZombieClass_Jockey;
}

stock bool IsCharger(int client)
{
	return GetEntProp(client, Prop_Send, "m_zombieClass") == L4D2ZombieClass_Charger;
}

stock bool IsHunter(int client)
{
	return GetEntProp(client, Prop_Send, "m_zombieClass") == L4D2ZombieClass_Hunter;
}

stock bool IsSmoker(int client)
{
	return GetEntProp(client, Prop_Send, "m_zombieClass") == L4D2ZombieClass_Smoker;
}

stock bool IsBoomer(int client)
{
	return GetEntProp(client, Prop_Send, "m_zombieClass") == L4D2ZombieClass_Boomer;
}

stock bool IsTank(int client)
{
	return GetEntProp(client, Prop_Send, "m_zombieClass") == L4D2ZombieClass_Tank;
}

stock void vSpawnBreakProp(int client, const char[] model, float force)
{
	int entity = CreateEntityByName("prop_physics_override");
	if (entity > 0)
	{
		float pos[3], ang[3];
		GetClientAbsOrigin(client, pos);
		GetClientAbsAngles(client, ang);
		pos[2] += 50.0;
		DispatchKeyValue(entity, "model", model);
		DispatchSpawn(entity);
		TeleportEntity(entity, pos, ang, NULL_VECTOR);
		
		float vec[3];
		MakeVectorFromPoints(pos, pos, vec);
		ScaleVector(vec, force);
		SDKHooks_TakeDamage(entity, 0, 0, DMG_GENERIC, vec);
	}
}

public Action OnTakeDamage(int victim, int &attacker, int &inflictor, float &damage, int &damagetype, char &weapon)
{
	float damage_multiplier = 1.0;
	if (g_bIsHardTank[victim])
	{
		if (damage < 30.0)
		{
			damage = 1.0;
		}
		return Plugin_Changed;
	}

	if (IsSurvivor(victim) && IsTank(attacker) && g_bIsTankPunch)
	{
		damage_multiplier *= 2.0;
	}

	if (IsSurvivor(victim) && IsCommonInfected(attacker) && g_bIsCommonPunch)
	{
		damage_multiplier *= 3.0;
	}

	if (IsSurvivor(victim) && IsSmoker(attacker) && g_bIsSmokerPunch)
	{
		damage_multiplier *= 2.5;
	}

	if (IsSurvivor(victim) && IsCharger(attacker) && g_bIsChargerDamage)
	{
		if (GetRandomInt(1, 100) <= 50)
		{
			damage_multiplier *= 2.0;
			EmitSoundToClient(attacker, "player/hunter/hit/tackled_1.wav", _, SNDCHAN_AUTO, SNDLEVEL_NORMAL);
		}
	}

	if (IsCommonInfected(attacker) && IsSurvivor(victim) && g_bIsFenShen)
	{
		CheatCommand(victim, "z_spawn", "common");
	}

	if (g_bFriendLyFire && IsSurvivor(attacker) && IsSurvivor(victim) && attacker != victim)
	{
		if (!g_bIsDamaged[attacker])
		{
			SDKHooks_TakeDamage(attacker, victim, attacker, 15.0, DMG_CLUB);
			g_bIsDamaged[attacker] = true;
			CreateTimer(0.3, TimerCheckDamage, attacker);
		}
	}

	if (g_bIsMYZZ && IsInfected(attacker) && IsSurvivor(victim))
	{
		char WeaponUsed[64];
		if (IsValidEdict(weapon))
		{
			GetEdictClassname(weapon, WeaponUsed, sizeof(WeaponUsed));
		}
		else if (IsValidEdict(inflictor))
		{
			GetEdictClassname(inflictor, WeaponUsed, sizeof(WeaponUsed));
		}
		
		if (StrContains(WeaponUsed, "player", true) != -1)
		{
			int zombie_class = GetEntProp(attacker, Prop_Send, "m_zombieClass");
			int hasvictim = L4D2_GetSurvivorVictim(attacker);
			if (victim != hasvictim && zombie_class < 7)
			{
				CreateTimer(1.0, Time_damage, GetClientUserId(victim), TIMER_REPEAT | TIMER_FLAG_NO_MAPCHANGE);
				CreateTimer(5.0, Timer_StopBleed, GetClientUserId(victim));
			}
		}
	}
	
	damage = damage * damage_multiplier;
	if(damage > 0.0) return Plugin_Changed;
	return Plugin_Continue;
}

public Action Timer_StopBleed(Handle timer, any userid)
{
	KillTimer(timer);
	return Plugin_Stop;
}

public void PlayerShoved(Event event, const char[] name, bool dontBroadcast)
{
	int attacker = GetClientOfUserId(event.GetInt("attacker"));
	int victim = GetClientOfUserId(event.GetInt("userid"));

	if (g_bIsShovedDamage && IsSurvivor(attacker) && IsPlayerAlive(attacker))
	{
		SDK_DamageTarget(attacker, 2, DMG_GENERIC);
	}

	if (IsHunter(victim) && IsSurvivor(attacker) && g_bStealthSmoker)
	{
		L4D_StaggerPlayer(attacker, victim);
	}
}

public void eReviveSuccess(Event event, const char[] name, bool dontBroadcast)
{
	int Reviver = GetClientOfUserId(event.GetInt("userid"));
	if (g_bIsReviveDamage && IsSurvivor(Reviver) && IsPlayerAlive(Reviver))
	{
		SDK_DamageTarget(Reviver, 5, DMG_GENERIC);
	}
}

public Action Time_damage(Handle timer, any userid)
{
	int client = GetClientOfUserId(userid);
	if (IsSurvivor(client))
	{
		if (IsIncapacitated(client))
		{
			return Plugin_Stop;
		}
		
		int health = GetClientHealth(client);
		float temphealth = GetTempHealth(client);
		
		if (temphealth < 2.0)
		{
			if (temphealth == 1.0)
			{
				SetTempHealth(client, temphealth - 1.0);
				if (GetClientHealth(client) > 1)
				{
					SetEntityHealth(client, health - 1);
				}
				else
				{
					ForcePlayerSuicide(client);
				}
			}
			else
			{
				if (GetClientHealth(client) > 2)
				{
					SetEntityHealth(client, health - 2);
				}
				else
				{
					ForcePlayerSuicide(client);
				}
			}
		}
		else
		{
			SetTempHealth(client, temphealth - 2.0);
		}
	}
	return Plugin_Continue;
}

public Action TimerCheckDamage(Handle timer, any client)
{
	if(client > 0 && IsClientInGame(client))
		g_bIsDamaged[client] = false;
	return Plugin_Stop;
}

public Action Timer_WenYiTank(Handle timer)
{
	for (int i = 1; i <= MaxClients; i++)
	{
		if (IsTank(i) && IsPlayerAlive(i))
		{
			float pos[3];
			GetClientAbsOrigin(i, pos);
			for (int j = 1; j <= MaxClients; j++)
			{
				if (IsSurvivor(j) && IsPlayerAlive(j) && !IsFakeClient(j))
				{
					float vpos[3];
					GetClientAbsOrigin(j, vpos);
					float dis = GetVectorDistance(pos, vpos);
					if (dis <= 160.0)
					{
						SDK_DamageTarget(j, 5, DMG_GENERIC, .attacker=i);
					}
				}
			}
		}
	}
	return Plugin_Continue;
}

public bool SetTempHealth(int client, float fHp)
{
	if (IsClientConnected(client) && IsClientInGame(client))
	{
		SetEntPropFloat(client, Prop_Send, "m_healthBufferTime", GetGameTime());
		SetEntPropFloat(client, Prop_Send, "m_healthBuffer", fHp);
		return true;
	}
	return false;
}

stock bool IsIncapacitated(int client)
{
	return GetEntProp(client, Prop_Send, "m_isIncapacitated");
}

stock int GetEntityHealth(int entity)
{
	return GetEntProp(entity, Prop_Send, "m_iHealth");
}

stock void SDK_DamageTarget(int victim, int damage, int damagetype, int attacker=0, float force[3], float position[3])
{
	int inflictor = -1;
	int weapon = 0;
	
	if (g_hForward == null)
	{
		g_hForward = CreateGlobalForward("SDK_OnTakeDamage", ET_Hook, Param_Cell, Param_Cell, Param_Cell, Param_Cell, Param_Cell, Param_CellByRef, Param_FloatByRef, Param_FloatByRef, Param_Cell);
	}
	
	SDKCall(g_hForward, 5, victim, attacker, inflictor, damage, damagetype, weapon, position, force, 0);
}

public void Event_PlayerDeath(Event event, const char[] name, bool dontBroadcast)
{
}

public void Event_JockeyRide(Event event, const char[] name, bool dontBroadcast)
{
}

public void Event_PlayerHurt(Event event, const char[] name, bool dontBroadcast)
{
}

public void Event_PlayerIncapacitated(Event event, const char[] name, bool dontBroadcast)
{
}

public void Event_PlayerBoomed(Event event, const char[] name, bool dontBroadcast)
{
}

public void Event_PlayerNowIt(Event event, const char[] name, bool dontBroadcast)
{
}

