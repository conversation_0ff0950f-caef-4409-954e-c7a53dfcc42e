
/* Plugin Template generated by Pawn Studio */
 
#include <sourcemod>
#include <sdktools>
#include <sdktools_functions>
#include <sdkhooks>
 
 
new g_sprite;
new g_iVelocity ;
new GameMode;
new L4D2Version;
new g_iOffsetGlow;

#define LEN64 64
#define PARTICLE_MUZZLE_FLASH		"weapon_muzzle_flash_autoshotgun"  
#define PARTICLE_WEAPON_TRACER		"weapon_tracers_50cal" 
#define PARTICLE_BOMBEXPLODE		"weapon_grenadelauncher"
#define PARTICLE_BLOOD		"blood_gore_arterial_drip"

#define SOUND_ENGINE "vehicles/helicopter/helicopter_loop09louder.wav"
#define SOUND_SHOT		"weapons/50cal/50cal_shoot.wav"  
#define SOUND_BOMBEXPLODE		"weapons/grenade_launcher/grenadefire/grenade_launcher_explode_1.wav"  
#define SOUND_BOMBDROP		"weapons/grenade_launcher/grenadefire/grenade_launcher_fire_1.wav"  
#define SOUND_FLAME		"ambient/gas/steam2.wav"  
#define MODEL_W_PIPEBOMB "models/w_models/weapons/w_eq_pipebomb.mdl"
#define MODEL_W_MOLOTOV "models/w_models/weapons/w_eq_molot5v.mdl"
#define MODEL_helicopter "models/props_vehicles/helicopter_rescue.mdl" //"models/f18/f18.mdl"  //"models/f18/f18_sb.mdl" "models/c2m5_helicopter_extraction/c2m5_helicopter_small.mdl"
 
#define MODEL_MISSILE "models/w_models/weapons/w_HE_grenade.mdl"
new Handle:l4d_helicopter_size;
new Handle:l4d_helicopter_gun_accuracy;
new Handle:l4d_helicopter_gun_damage; 
new Handle:l4d_helicopter_password;   
new Handle:l4d_helicopter_chance_tankdrop; 
new Handle:l4d_helicopter_speed; 

new Handle:l4d_helicopter_fuel;   
new Handle:l4d_helicopter_bullet; 
new Handle:l4d_helicopter_bomb; 

new Handle:l4d_helicopter_range; 

new LastButton[MAXPLAYERS+1];  
new LastFlag[MAXPLAYERS+1];  
new Float:LastTime[MAXPLAYERS+1];  
 
new bool:g_gamestart=false;
new g_PointHurt=0;
new g_offsNextPrimaryAttack=0;
new g_iActiveWO=0;
new HelicopterCount=0;

public Plugin:myinfo = 
{
	name = "Helicopter",
	author = "Pan Xiaohai ",
	description = "<- Description ->",
	version = "1.6",
	url = "<- URL ->"
}
 
public OnPluginStart()
{
	GameCheck();
	if(!L4D2Version)return;
	if(GameMode==2)return;
	l4d_helicopter_size = CreateConVar("l4d_helicopter_size", "1.0", "直升机的大小[1.0, 2.0]");
	l4d_helicopter_speed = CreateConVar("l4d_helicopter_speed", "220", "直升机的飞行速度[100.0, 500.0]"); 
	l4d_helicopter_gun_accuracy = CreateConVar("l4d_helicopter_gun_accuracy", "0.5", "枪的精准度[0.0, 1.0]");
	l4d_helicopter_gun_damage = CreateConVar("l4d_helicopter_gun_damage", "100", "机枪的伤害值[1.0, 100.0]"); 
	l4d_helicopter_password = CreateConVar("l4d_helicopter_password", "256", "直升机启动密码"); 
	l4d_helicopter_chance_tankdrop = CreateConVar("l4d_helicopter_chance_tankdrop", "25.0", "坦克死亡多少几率掉落直升机"); 

	l4d_helicopter_fuel= CreateConVar("l4d_helicopter_fuel", "500", "燃料"); 
	l4d_helicopter_bullet = CreateConVar("l4d_helicopter_bullet", "5000", "子弹数量"); 
	l4d_helicopter_bomb = CreateConVar("l4d_helicopter_bomb", "300", "炸弹数量"); 

	l4d_helicopter_range = CreateConVar("l4d_helicopter_range", "16000.0", "直升机飞出多远无法控制"); 
	
	HookEvent("tank_killed", tank_killed); 
	 
	//AutoExecConfig(true, "l4d2_helicopte"); 
	
	HookEvent("player_spawn", player_spawn);	
	HookEvent("player_death", player_death); 
	HookEvent("player_bot_replace", player_bot_replace );	  
	HookEvent("bot_player_replace", bot_player_replace );	
 	
	HookEvent("round_start", round_start); 
	HookEvent("round_end", round_end ); 
	HookEvent("map_transition", map_transition, EventHookMode_Pre);	
	
	RegConsoleCmd("sm_h", sm_h);
	
	ResetAllState( );
 	g_gamestart=false;
	
	g_sprite=g_sprite+0;
	g_iVelocity = FindSendPropOffs("CBasePlayer", "m_vecVelocity[0]");
	g_offsNextPrimaryAttack = FindSendPropOffs("CBaseCombatWeapon", "m_flNextPrimaryAttack");
	g_iActiveWO =	FindSendPropInfo("CBaseCombatCharacter","m_hActiveWeapon");

	g_PointHurt=0; 
	HelicopterCount=0;
	
} 


new DummyEnt[MAXPLAYERS+1];
new HelicopterEnt[MAXPLAYERS+1];
new HelicopterEnt_other[MAXPLAYERS+1];
new Float:Pitch[MAXPLAYERS+1]; 
new Float:Roll[MAXPLAYERS+1]; 

new Float:Gravity[MAXPLAYERS+1];
new Float:Fuel[MAXPLAYERS+1]; 
new Float:TimerIndicator[MAXPLAYERS+1];
new Float:LastPos[MAXPLAYERS+1][3]; 
new Float:MaxSpeed[MAXPLAYERS+1]; 

new Bullet[MAXPLAYERS+1]; 
new Bomb[MAXPLAYERS+1]; 
new Float:BombTime[MAXPLAYERS+1];
new Float:ShotTime[MAXPLAYERS+1];

new Float:RangeCheckTime[MAXPLAYERS+1]; 
new Float:AloneStartTime[MAXPLAYERS+1]; 

new Info[MAXPLAYERS+1][4];

public Action:sm_h(client,args)
{ 
	 
 	if(client>0 && IsClientInGame(client) && IsPlayerAlive(client))
	{
		if(DummyEnt[client]>0)
		{
			RemoveHelicopter(client);
		}
		else 
		{ 
			decl String:password[20]="";
			decl String:arg[20];
			GetConVarString(l4d_helicopter_password, password, sizeof(password));
			GetCmdArg(1, arg, sizeof(arg));
			//PrintToChatAll("arg %s, password %s", arg, password);
			if(StrEqual(arg, password))CreateHelicopter(client, -1);
			else PrintToChat(client, "Your password is incorrect");
		}
	}
 
}

CreateHelicopter(client, infoIndex)
{ 
	if(DummyEnt[client]>0)
	{
		RemoveHelicopter(client);
	}
	 
	new Float:modelScale=GetConVarFloat(l4d_helicopter_size); 
	if(modelScale<1.0)modelScale=1.0;
	else if(modelScale>2.0)modelScale=2.0;
	if(IsValidClient(client))
	{ 
		decl Float:ang[3];
		decl Float:pos[3]; 
	 
		GetClientAbsAngles(client, ang);
		GetClientAbsOrigin(client, pos);
		ang[0]=0.0;
  
		new dummy=CreateEntityByName("molotov_projectile");		 
		DispatchKeyValue(dummy, "model", MODEL_W_MOLOTOV);  
		SetEntProp(dummy, Prop_Data, "m_CollisionGroup", 2);
		SetEntityMoveType(dummy, MOVETYPE_NOCLIP);
		DispatchSpawn(dummy);  
		VisiblePlayer(dummy, false);
		
		new String:tname[20];
		Format(tname, 20, "target%d", client);
		DispatchKeyValue(client, "targetname", tname);
		
		SetVariantString(tname);
		AcceptEntityInput(dummy, "SetParent",dummy, dummy, 0);
		
		
		SetVector(pos,   0.0, 0.0, 0.0);
		SetVector(ang,  0.0, 0.0, 0.0);
		TeleportEntity(dummy, pos, ang, NULL_VECTOR);
		 
		SetEntPropVector(dummy, Prop_Send, "m_angRotation", ang);
		
	 //AcceptEntityInput(dummy, "ClearParent"); 
	 
	 
		new ment=CreateEntityByName("prop_dynamic");  
		DispatchKeyValue(ment, "model", MODEL_helicopter);  
		SetEntProp(ment, Prop_Data, "m_CollisionGroup", 2); 
		SetEntPropFloat(ment, Prop_Send,"m_flModelScale",modelScale*0.12);
		 	 
		DispatchSpawn(ment);  
		
		Format(tname, 20, "target%d", dummy);
		DispatchKeyValue(dummy, "targetname", tname); 
		SetVariantString(tname);
		AcceptEntityInput(ment, "SetParent",ment, ment, 0);
		SetVector(pos,  -0.0, 0.0, 0.0 ); //front,, up
		SetVector(ang,  0.0, 0.0, 0.0);
		TeleportEntity(ment, pos, NULL_VECTOR,NULL_VECTOR);
	 
		SetEntPropVector(ment, Prop_Send, "m_angRotation", ang);	
		
		DispatchKeyValueFloat(ment, "fademindist", 10000.0);
		DispatchKeyValueFloat(ment, "fademaxdist", 20000.0);
		DispatchKeyValueFloat(ment, "fadescale", 0.0); 
  		
		SetVariantString("3ready");//3ready
		AcceptEntityInput(ment, "SetAnimation");
		SetEntPropFloat(ment, Prop_Send, "m_flPlaybackRate" ,0.3); 
		
		SetEntityMoveType(dummy, MOVETYPE_NONE);
		SetEntityMoveType(ment, MOVETYPE_NONE);
		
		VisiblePlayer(client,false);
		GotoThirdPerson(client);
		SetEntProp(client, Prop_Send, "m_bDrawViewmodel", 0);
		DummyEnt[client]=dummy;		
		HelicopterEnt[client]=ment;
		HelicopterEnt_other[client]=CreateModel(client);
		MaxSpeed[client]=GetConVarFloat(l4d_helicopter_speed);
		Pitch[client]=0.0;
		Roll[client]=0.0;
		EmitSoundToAll(SOUND_ENGINE, dummy, SNDCHAN_AUTO, SNDLEVEL_NORMAL, SND_NOFLAGS, 0.6, SNDPITCH_NORMAL, -1, pos, NULL_VECTOR, true, 0.0);
		
		RangeCheckTime[client]=0.0;
		AloneStartTime[client]=GetEngineTime();
		
		LastTime[client]=GetEngineTime();
		LastButton[client]=0;
		LastFlag[client]=0;
		GetClientAbsOrigin(client, LastPos[client]);
		
		if(infoIndex<0)
		{
			Fuel[client]=GetConVarFloat(l4d_helicopter_fuel); 
			Bullet[client]=GetConVarInt(l4d_helicopter_bullet);
			Bomb[client]=GetConVarInt(l4d_helicopter_bomb);
		}
		else
		{			
			Bullet[client]=Info[infoIndex][1];
			Bomb[client]=Info[infoIndex][2];
			Fuel[client]=Info[infoIndex][3]*1.0;
		}
		
		ShotTime[client]=0.0;
		BombTime[client]=0.0;
		
		SDKUnhook( client, SDKHook_PreThink,  PreThink); 
		SDKHook( client, SDKHook_PreThink,  PreThink); 
		
		SDKUnhook( client, SDKHook_PostThinkPost,  PostThinkPost); 
		SDKHook( client, SDKHook_PostThinkPost,  PostThinkPost);  
		
		SDKUnhook( client, SDKHook_SetTransmit,  OnSetTransmitClient); 
		//SDKHook( client, SDKHook_SetTransmit, OnSetTransmitClient);
		
		SDKUnhook( HelicopterEnt[client], SDKHook_SetTransmit,  OnSetTransmitModel); 
		SDKHook( HelicopterEnt[client], SDKHook_SetTransmit, OnSetTransmitModel);
		
		SDKUnhook( HelicopterEnt_other[client], SDKHook_SetTransmit,  OnSetTransmitModel_Other); 
		SDKHook( HelicopterEnt_other[client], SDKHook_SetTransmit, OnSetTransmitModel_Other);		
	} 
}
CreateModel(client)
{
	new Float:modelScale=GetConVarFloat(l4d_helicopter_size);
	 
	if(modelScale<1.0)modelScale=1.0;
	else if(modelScale>2.0)modelScale=2.0;	
	modelScale*=1.3;
	
	decl Float:ang[3];
	decl Float:pos[3]; 
	
	new ment=CreateEntityByName("prop_dynamic");  
	DispatchKeyValue(ment, "model", MODEL_helicopter);  
	SetEntProp(ment, Prop_Data, "m_CollisionGroup", 2); 
	SetEntPropFloat(ment, Prop_Send,"m_flModelScale",modelScale*0.12);
		 
	DispatchSpawn(ment);  
	new String:tname[20];
	Format(tname, 20, "target%d", client);
	DispatchKeyValue(client, "targetname", tname); 
	SetVariantString(tname);
	AcceptEntityInput(ment, "SetParent",ment, ment, 0);
	
	SetVector(pos,  -0.0, 0.0, 0.0 ); //front,, up
	SetVector(ang,  0.0, 0.0, 0.0);
	TeleportEntity(ment, pos, NULL_VECTOR,NULL_VECTOR);
 
	SetEntPropVector(ment, Prop_Send, "m_angRotation", ang);	
	
	DispatchKeyValueFloat(ment, "fademindist", 10000.0);
	DispatchKeyValueFloat(ment, "fademaxdist", 20000.0);
	DispatchKeyValueFloat(ment, "fadescale", 0.0); 
	
	SetVariantString("3ready");//3ready
	AcceptEntityInput(ment, "SetAnimation");
	SetEntPropFloat(ment, Prop_Send, "m_flPlaybackRate" ,0.3); 	
	SetEntityMoveType(ment, MOVETYPE_NOCLIP);	

	
	return ment;
}
RemoveHelicopter(client)
{ 
	if(client>0 && IsClientInGame(client))
	{
		SDKUnhook( client, SDKHook_SetTransmit,  OnSetTransmitClient); 
		SDKUnhook( client, SDKHook_PostThinkPost,  PostThinkPost); 
		SDKUnhook( client, SDKHook_PreThink,  PreThink); 
		GotoFirstPerson(client);
		VisiblePlayer(client, true);
		SetEntProp(client, Prop_Send, "m_bDrawViewmodel", 1);
		SetEntityMoveType(client, MOVETYPE_WALK); 
		SetEntityGravity(client, 1.0); 
		SetEntProp(client, Prop_Send, "m_iHideHUD", 2048);
		
		SetEntProp(client, Prop_Send, "m_iGlowType", 0);
		SetEntProp(client, Prop_Send, "m_nGlowRange", 0 );  
		SetEntProp(client, Prop_Send, "m_glowColorOverride",0 ); 
		
	}	
	if(client>0 && DummyEnt[client]>0 )
	{ 
 
		StopSound(DummyEnt[client], SNDCHAN_AUTO,SOUND_ENGINE);
		StopSound(HelicopterEnt[client], SNDCHAN_AUTO,SOUND_ENGINE);   
	 		
 	
		if(IsValidEnt(HelicopterEnt[client]))
		{ 
			SDKUnhook( HelicopterEnt[client], SDKHook_SetTransmit,  OnSetTransmitModel); 
			AcceptEntityInput(HelicopterEnt[client], "kill");
		}	
		if(IsValidEnt(HelicopterEnt_other[client]))
		{ 
			SDKUnhook( HelicopterEnt_other[client], SDKHook_SetTransmit,  OnSetTransmitModel_Other); 
			AcceptEntityInput(HelicopterEnt_other[client], "kill");
		}			
		if(IsValidEnt(DummyEnt[client]))
		{			
			AcceptEntityInput(DummyEnt[client], "ClearParent"); 
			AcceptEntityInput(DummyEnt[client], "kill");
		}
  
 	} 
	DummyEnt[client]=0;
	HelicopterEnt[client]=0;
	HelicopterEnt_other[client]=0;	
}
DropHelicopter(client) 
{
	decl Float:ang[3];
	decl Float:pos[3]; 
 
	GetClientAbsAngles(client, ang);
	GetClientAbsOrigin(client, pos);
	pos[2]+=20.0;
	ang[0]=0.0;
	new dummy=CreateEntityByName("molotov_projectile");		//pipe_bomb_projectile  
	DispatchKeyValue(dummy, "model", MODEL_W_MOLOTOV);  
	SetEntProp(dummy, Prop_Data, "m_CollisionGroup", 2);
	SetEntityMoveType(dummy, MOVETYPE_FLYGRAVITY);
	SetEntityGravity(dummy, 0.1);
	DispatchSpawn(dummy);  
	TeleportEntity(dummy, pos, ang, NULL_VECTOR);	
	VisiblePlayer(dummy, false);
	 
	new ment=CreateModel(client);
	DispatchSpawn(ment);  
	new String:tname[20];
	Format(tname, 20, "target%d", dummy);
	DispatchKeyValue(dummy, "targetname", tname); 
	SetVariantString(tname);
	AcceptEntityInput(ment, "SetParent",ment, ment, 0);
	
	SetVector(pos,  -0.0, 0.0, 0.0 ); //front,, up
	SetVector(ang,  0.0, 0.0, 0.0);
	TeleportEntity(ment, pos, ang,NULL_VECTOR);	 
	SetEntityMoveType(ment, MOVETYPE_NOCLIP);

	SetVariantString("3ready");//3ready
	AcceptEntityInput(ment, "SetAnimation");
	SetEntPropFloat(ment, Prop_Send, "m_flPlaybackRate" ,0.3); 	
	
	new button=CreateButton(dummy);
	SetEntPropFloat(button, Prop_Send, "m_fadeMaxDist", dummy*1.0); 	
	
	EmitSoundToAll(SOUND_ENGINE, dummy, SNDCHAN_AUTO, SNDLEVEL_NORMAL, SND_NOFLAGS, 0.6, SNDPITCH_NORMAL, -1, pos, NULL_VECTOR, true, 0.0);
	
	new index=-1;
 	for(new i=0; i<=MaxClients; i++)
	{
		if(Info[i][0]==0)
		{
			index=i;		
			break;
		}
	}
	if(index>=0)
	{
		Info[index][0]=dummy;
		Info[index][1]=Bullet[client];
		Info[index][2]=Bomb[client];
		Info[index][3]=RoundFloat( Fuel[client] );
	}
	
}
LostControl(client)
{
	if(client>0 && DummyEnt[client]>0 )
	{
		RemoveHelicopter(client);
		DropHelicopter(client);	
	}
}
 
public PreThink(client)
{
	if(IsClientInGame(client) && IsPlayerAlive(client))
	{
		new Float:time=GetEngineTime( );
		new Float:intervual=time-LastTime[client]; 
		if(intervual<0.01)intervual=0.01;
		else if(intervual>0.1)intervual=0.1;
		new button=GetClientButtons(client); 
		new flag=GetEntityFlags(client);   
		Fly(client,button ,flag, intervual, time); 
		LastTime[client]=time; 
		LastButton[client]=button;	 
		LastFlag[client]=flag;
	}
	else
	{
		LostControl(client);
	}

}
public PostThinkPost(client)
{
	if(IsClientInGame(client) && IsPlayerAlive(client) && DummyEnt[client]>0 )
	{
		new button=GetClientButtons(client);
		if((button & IN_USE))SetEntProp(client, Prop_Send, "m_iHideHUD",2048);
		else 	SetEntProp(client, Prop_Send, "m_iHideHUD", 64);
	
		SetEntProp(client, Prop_Send, "m_iAddonBits", 0);
		SetEntProp(client, Prop_Send, "m_bDrawViewmodel", 0);		 
		SetEntityMoveType(client, MOVETYPE_FLYGRAVITY); 	
		new weapon= GetEntDataEnt2(client, g_iActiveWO);		 
		if(weapon>0)
		{
			new Float:flNextPrimaryAttack = GetEntDataFloat(weapon, g_offsNextPrimaryAttack) ; 
			SetEntDataFloat(weapon, g_offsNextPrimaryAttack, flNextPrimaryAttack****,true);
		} 
		SetEntProp(client, Prop_Send, "m_iGlowType", 3 ); //3
		SetEntProp(client, Prop_Send, "m_nGlowRange", 0 ); //0
		SetEntProp(client, Prop_Send, "m_glowColorOverride",  1); //1
	}
 
}
public Action:OnSetTransmitClient (polit, client)
{
	//PrintToChatAll("%N client %d", client , polit);
	if(polit!=client)
	{
		return Plugin_Handled; 
	}
	else return Plugin_Continue;
}
public Action:OnSetTransmitModel (model, client)
{  
	if(HelicopterEnt[client]==model)
	{
		 return Plugin_Continue;
	}
	else return Plugin_Handled; 
}
public Action:OnSetTransmitModel_Other (model, client)
{ 
	if(HelicopterEnt_other[client]!=model)
	{
		 return Plugin_Continue;
	}
	else return Plugin_Handled; 
}
Fly(client,button , flag, Float:intervual, Float:time)
{
	new dummy=DummyEnt[client];
	new modelEnt=HelicopterEnt[client];
	decl Float:clientAngle[3]; 
	decl Float:modelAng[3]; 
	decl Float:modelPos[3]; 
 	GetEntPropVector(modelEnt, Prop_Send, "m_angRotation", modelAng);
 	GetClientEyeAngles(client, clientAngle); 
	
	modelAng[0]=0.0-clientAngle[0];
	modelAng[1]=0.0;
	

 		
		decl Float:clientPos[3];  
		decl Float:temp[3]; 
		decl Float:volicity[3]; 
		decl Float:pushForce[3]; 
		decl Float:pushForceVertical[3]; 
		new Float:liftForce=50.0; 
		new Float:speedLimit=MaxSpeed[client];
		new Float:fuelUsed=intervual;
		new Float:gravity=0.001;
		new Float:gravityNormal=0.001;
		GetEntDataVector(client, g_iVelocity, volicity);
		
		GetClientAbsOrigin(client, clientPos);
		CopyVector(clientPos,LastPos[client]);
		clientAngle[0]=0.0;
		
		SetVector(pushForce, 0.0, 0.0, 0.0);
		SetVector(pushForceVertical, 0.0, 0.0,  0.0);
		new bool:up=false;
		new bool:down=false;
		new bool:speed=false;
		new bool:speedStart=false;
		new bool:move=false;
		new Float:pitch=0.0;
		new Float:roll=0.0;
		 
		 
		if((button & IN_JUMP) ) 
		{ 
			SetVector(pushForceVertical, 0.0, 0.0, 1.5);
			up=true;

			if(gravity>0.0)gravity=-0.01;
			gravity=Gravity[client]-1.0*intervual; 
		}

		if((button & IN_DUCK) && !up) 
		{ 
			SetVector(pushForceVertical, 0.0, 0.0, -2.0);
			down=true; 
			if(gravity<0.0)gravity=0.01;
			gravity=Gravity[client]*****intervual;  
		}
		//PrintToChatAll("g %f %f ",Gravity[client], gravity);
		if(button & IN_FORWARD)
		{ 
			GetAngleVectors(clientAngle, temp, NULL_VECTOR, NULL_VECTOR);
			NormalizeVector(temp,temp); 
			AddVectors(pushForce,temp,pushForce); 
			move=true;
			pitch=1.0;
			
		}
		else if(button & IN_BACK)
		{
			GetAngleVectors(clientAngle, temp, NULL_VECTOR, NULL_VECTOR);
			NormalizeVector(temp,temp); 
			SubtractVectors(pushForce, temp, pushForce); 
			move=true;  
			pitch=-1.0;
		}
		if(button & IN_MOVELEFT)
		{ 
			GetAngleVectors(clientAngle, NULL_VECTOR, temp, NULL_VECTOR);
			NormalizeVector(temp,temp); 
			SubtractVectors(pushForce,temp,pushForce);
			move=true;
			roll=-1.0;
		}
		else if(button & IN_MOVERIGHT)
		{
			GetAngleVectors(clientAngle, NULL_VECTOR, temp, NULL_VECTOR);
			NormalizeVector(temp,temp); 
			AddVectors(pushForce,temp,pushForce);
			roll=1.0;
		}
		if((button & IN_SPEED))
		{
			if(!(LastButton[client] & IN_SPEED))
			{ 
				speedStart=true;
			}
			speed=true;
		}
 
		if(move && up)
		{
			ScaleVector(pushForceVertical, 0.3);
			ScaleVector(pushForce, 1.5);
		}
		
		//NormalizeVector(pushForce, pushForce); 
		if(speed || up || down)
		{ 
			fuelUsed*=3.0;
			speedLimit*=1.5;
			liftForce*=2.0;
		}
		
		AddVectors(pushForceVertical,pushForce,pushForce);
		NormalizeVector(pushForce, pushForce);
		//ShowDir(client, clientPos, pushForce, 0.06);
		//PrintToChatAll("v %f", GetVectorLength(volicity));
		ScaleVector(pushForce,liftForce*intervual);
		if(!(up || down)  )
		{			 
			if(FloatAbs(volicity[2])>40.0)gravity=volicity[2]*intervual;
			else gravity=gravityNormal;  
		}
		new Float:v=GetVectorLength(volicity);
		 
		{
			if(gravity>0.5)gravity=0.5;
			if(gravity<-0.5)gravity=-0.5; 
			
			
			if( speed && !( up || down))
			{ 
				volicity[2]*=0.8;	  
				TeleportEntity(client, NULL_VECTOR, NULL_VECTOR, volicity);
			}
			else if(v>speedLimit)
			{
				NormalizeVector(volicity,volicity);
				ScaleVector(volicity, speedLimit);
				TeleportEntity(client, NULL_VECTOR, NULL_VECTOR, volicity);
				 
			} 
			
			SetEntityGravity(client, gravity);
			Gravity[client]=gravity;
		}
		Fuel[client]-=fuelUsed;		
		if(Fuel[client]<=0.0)
		{
 
			//PrintHintText(client, "You jet pack is short of fuel , please look for oxygentank, propanetank or gascan");
		}
		else 
		{
			//PrintCenterText(client, "fuel left\n    %d  ", RoundFloat(Fuel[client]));
		} 
		if(pitch==0.0)
		{
			if(Pitch[client]>0.0)pitch=-1.0;
			else if(Pitch[client]<0.0)pitch=1.0;
			else pitch=0.0;
			if(FloatAbs(Pitch[client])<5.0)
			{
				Pitch[client]=0.0;
				pitch=0.0;
			}
		}
		else
		{
			if(Pitch[client]>0.0 && pitch<0.0)pitch=-3.0;
			else if(Pitch[client]<0.0 && pitch>0.0)pitch=3.0;
		}
		Pitch[client]+=pitch*30.0*intervual;
		if(Pitch[client]>30.0 )Pitch[client]=30.0;
		else if(Pitch[client]<-35.0 )Pitch[client]=-35.0;
		
		if(roll==0.0)
		{
			if(Roll[client]>0.0)roll=-1.0;
			else if(Roll[client]<0.0)roll=1.0;
			else roll=0.0;
			if(FloatAbs(Roll[client])<5.0)
			{
				Roll[client]=0.0;
				roll=0.0;
			}
		}
		else
		{
			if(Roll[client]>0.0 && roll<0.0)roll=-3.0;
			else if(Roll[client]<0.0 && roll>0.0)roll=3.0;
		}
		Roll[client]+=roll*60.0*intervual;
		if(Roll[client]>35.0 )Roll[client]=35.0;
		else if(Roll[client]<-35.0 )Roll[client]=-35.0;	
		  
		new bool:shot1=false;
		new bool:shot2=false;
		new bool:shot3=false;
		if( button & IN_ATTACK)
		{
			if(time>ShotTime[client])
			{			 
				ShotTime[client]=time+0.06;			
				if(Bullet[client]>0)
				{
					Bullet[client]--;
					shot1=true;			
				}		
			}
		}
		if(button & IN_ATTACK2)
		{
			if(time>BombTime [client])
			{			 
				BombTime[client]=time****;
				if(Bomb[client]>0)
				{
					Bomb[client]--;
					shot2=true;			
				}
			}
		}
		else if(button & IN_ZOOM)
		{
			if(time>BombTime [client])
			{			 
				BombTime[client]=time****;
				if(Bomb[client]>0)
				{
					Bomb[client]--;
					shot3=true;			
				}
			}
		}	

		
		if((flag & FL_ONGROUND))
		{			
			modelAng[2]=0.0;
			shot1=shot2=shot3=false;
			SetVector(volicity, 0.0, 0.0, 100.0);
			if(button & IN_JUMP)
			{
				TeleportEntity(client, NULL_VECTOR, NULL_VECTOR,volicity);
				SetEntityGravity(client, -0.5);
			}
			else
			{
				SetEntityGravity(client, 1.0);
			}

		}
		else 	 
		{			
			modelAng[0]+=Pitch[client];
			modelAng[2]=Roll[client];
		}
		if((button & IN_USE) && (button & IN_DUCK))
		{
			LostControl(client); 
			return;
		}
		//PrintToChatAll("modelAng  %f", modelAng[0]);
		GetClientEyeAngles(client, clientAngle); 
		
		new Float:zero[3]; 
		SetVector(zero,  0.0, 0.0, 0.0);
		TeleportEntity(dummy , zero, zero, NULL_VECTOR);
		TeleportEntity(modelEnt , zero, zero, NULL_VECTOR);
		SetEntPropVector(modelEnt, Prop_Send, "m_angRotation", modelAng); 
		if(HelicopterEnt_other[client]>0)
		{
			modelAng[0]=Pitch[client];
			modelAng[1]=clientAngle[1];
			TeleportEntity(HelicopterEnt_other[client] , zero, zero, NULL_VECTOR);
			SetEntPropVector(HelicopterEnt_other[client], Prop_Send, "m_angRotation", modelAng); 
		}
		 
		if(shot1)
		{
			decl Float:clientEyePos[3];   
			GetClientEyePosition(client, clientEyePos); 
			Shot(client,  clientPos,clientEyePos, clientAngle);
		}
		if(shot2 || shot3)
		{
			decl Float:clientEyePos[3];   
			GetClientEyePosition(client, clientEyePos); 
			DropBomb(client, clientPos,clientEyePos, clientAngle, shot3);
		}
	 
		new m_pounceAttacker=GetEntProp(client, Prop_Send, "m_pounceAttacker");
		new m_tongueOwner=GetEntProp(client, Prop_Send, "m_tongueOwner");
		new m_isIncapacitated=GetEntProp(client, Prop_Send, "m_isIncapacitated", 1);
		new m_isHangingFromLedge=GetEntProp(client, Prop_Send, "m_isHangingFromLedge", 1);
		 
		new m_pummelAttacker=GetEntProp(client, Prop_Send, "m_pummelAttacker", 1);
		new m_jockeyAttacker=GetEntProp(client, Prop_Send, "m_jockeyAttacker", 1);
		if(m_pounceAttacker>0 || m_tongueOwner>0 || m_isHangingFromLedge>0 || m_isIncapacitated>0 || m_pummelAttacker>0 || m_jockeyAttacker>0 ) 
		{
			//SetEntityMoveType(client, MOVETYPE_WALK);
			//SetEntityGravity(client, 1.0);
			LostControl(client);
			return;
		}
		
		if((button & IN_USE)  )
		{
			if(Fuel[client]<0.0)Fuel[client]=-1.0;
			PrintCenterText(client, "Bullet %d \nBomb %d \nFuel %d", Bullet[client], Bomb[client], RoundFloat( Fuel[client]) );
		}
		if(Fuel[client]<0.0)
		{
			SetEntityGravity(client, 1.0);
		}
		if(time-RangeCheckTime[client]>1.0)
		{
			RangeCheckTime[client]=time; 
			if(IsAlone(client))
			{			 
				new tick=RoundFloat(8.0-(time-AloneStartTime[client]) );
				if(tick<0)tick=0;
				PrintCenterText(client, "You can not fly too far from your teamate! %d" ,tick); 
			}
			else
			{
				AloneStartTime[client]=time;
			}
		} 
		if(time-AloneStartTime[client]>=8.0)
		{
			 
			SetEntityGravity(client, 1.0);
		}
		 
} 
bool:IsAlone(client)
{
	decl Float:pos[3];
	decl Float:pos2[3];
	GetClientEyePosition(client, pos); 
	 
	new Float:range=GetConVarFloat(l4d_helicopter_range);
	new Float:Min=9999.0
 	for( new i=1; i<=MaxClients; i++)
	{
		if(IsClientInGame(i) && GetClientTeam(i)==2  && client!=i)
		{
			if(IsPlayerAlive(i))
			{ 
				GetClientEyePosition(i, pos2);
				new Float:dis=GetVectorDistance(pos, pos2); 
				if(dis<Min)
				{
					Min=dis;					 
				}  
			}  
		} 
	}  
 
	if(Min>range)return true;
	return false;	
	
}
Float:FloatSign(Float:value)
{
	if(value>0.0)return 1.0;
	else if(value<0.0)return -1.0;
	else return 0.0;
}
Shot(client, Float:helpos[3], Float:clientEyePos[3], Float:clientAngle[3])
{	
	decl Float:hitpos[3];   
	decl Float:gunpos[3];  
	decl Float:pos[3]; 
	decl Float:angle[3]; 
	 
	decl Float:right[3]; 
	decl Float:dir[3]; 
 	
	CopyVector(helpos, pos);
	GetHitPos(client, clientEyePos, clientAngle, hitpos);

	GetAngleVectors(clientAngle, NULL_VECTOR, right, NULL_VECTOR); 	
	CopyVector(right, gunpos);
	new bool:leftgun=Bullet[client]%2==0;
 	if(leftgun)ScaleVector(gunpos, 20.0);
	else ScaleVector(gunpos, -20.0); 
	AddVectors(pos, gunpos, gunpos);  
	
	SubtractVectors(hitpos,  gunpos, dir); 
	NormalizeVector(dir,dir);
	  
	new Float:acc=GetConVarFloat(l4d_helicopter_gun_accuracy);
	if(acc<0.0)acc=0.0;
	else if(acc>1.0)acc=1.0;	
	
	acc=0.005+acc*0.018;
	
	dir[0]+=GetRandomFloat(-1.0, 1.0)*acc;
	dir[1]+=GetRandomFloat(-1.0, 1.0)*acc;
	dir[2]+=GetRandomFloat(-1.0, 1.0)*acc;
	GetVectorAngles(dir, angle);  
	
	FireBullet(client, gunpos, angle, hitpos);  
	ShowTrack(client, gunpos, hitpos);  
	
	ShowMuzzleFlash(client,gunpos, clientAngle );
 	
	EmitSoundToAll(SOUND_SHOT, DummyEnt[client],  SNDCHAN_WEAPON, SNDLEVEL_NORMAL, SND_NOFLAGS,1.0, SNDPITCH_NORMAL, -1, NULL_VECTOR, NULL_VECTOR, true, 0.0);
 	
}
GetHitPos(client, Float:pos[3], Float:ang[3], Float:hitpos[3])
{
	new Handle:trace= TR_TraceRayFilterEx(pos, ang, MASK_SHOT, RayType_Infinite, TraceRayDontHitSelfAndSurvivor, client); 
 	new bool:hit=false; 
	if(TR_DidHit(trace))
	{		 
		TR_GetEndPosition(hitpos, trace); 
	}
	CloseHandle(trace);  
}
 
DropBomb(client,  Float:helpos[3], Float:clientEyePos[3], Float:clientAngle[3],bool:missile)
{	
  
	decl Float:pos[3]; 
	decl Float:dir[3];  
	decl Float:hitpos[3]; 
	
	 
	SetVector(pos, 0.0, 0.0, -30.0);   
	AddVectors(helpos, pos, pos);	
	
	new ent=0;
	 
	if(missile)
	{
		GetHitPos(client, clientEyePos, clientAngle, hitpos);
		SubtractVectors(hitpos, pos, dir);
		NormalizeVector(dir,dir); 
		 
		ent=CreateGLprojectile(client, pos,  dir, 900.0, 0.01); 
	}
	else
	{
		GetAngleVectors(clientAngle, dir, NULL_VECTOR, NULL_VECTOR);
		dir[2]=0.0;
		ent=CreateGLprojectile(client, pos,  dir, 500.0, 1.0);
	} 

	 
	SDKHook(ent, SDKHook_StartTouch , BombTouch);
	
	EmitSoundToAll(SOUND_BOMBDROP, 0,  SNDCHAN_WEAPON, SNDLEVEL_NORMAL, SND_NOFLAGS,1.0, SNDPITCH_NORMAL, -1, pos, NULL_VECTOR, true, 0.0);
 	
}
public BombTouch(ent, other)
{	 
	SDKUnhook(ent, SDKHook_StartTouch, BombTouch);
	decl Float:pos[3];
	GetEntPropVector(ent, Prop_Send, "m_vecOrigin", pos); 
	AcceptEntityInput(ent, "kill");
	Explode(pos, 200.0, 100.0);
}
Explode(Float:pos[3], Float:radius=160.0, Float:damage=100.0)
{
	new pointHurt = CreateEntityByName("point_hurt");    	
 	DispatchKeyValueFloat(pointHurt, "Damage", damage);        
	DispatchKeyValueFloat(pointHurt, "DamageRadius", radius);   
	if(L4D2Version)	DispatchKeyValue(pointHurt, "DamageType", "64"); 
	else DispatchKeyValue(pointHurt, "DamageType", "64"); 
 	DispatchKeyValue(pointHurt, "DamageDelay", "0.0");   
	DispatchSpawn(pointHurt);
	TeleportEntity(pointHurt, pos, NULL_VECTOR, NULL_VECTOR);  
	AcceptEntityInput(pointHurt, "Hurt");    
	CreateTimer(0.1, DeletePointHurt, pointHurt); 
 
	new push = CreateEntityByName("point_push");         
  	DispatchKeyValueFloat (push, "magnitude",damage*2.0);                     
	DispatchKeyValueFloat (push, "radius", radius);                     
  	SetVariantString("spawnflags 24");                     
	AcceptEntityInput(push, "AddOutput");
 	DispatchSpawn(push);   
	TeleportEntity(push, pos, NULL_VECTOR, NULL_VECTOR);  
 	AcceptEntityInput(push, "Enable");
	CreateTimer(0.5, DeletePushForce, push); 
	
	
	ShowParticle(pos, NULL_VECTOR,  PARTICLE_BOMBEXPLODE, 0.1);	
	EmitSoundToAll(SOUND_BOMBEXPLODE, 0,  SNDCHAN_WEAPON, SNDLEVEL_NORMAL, SND_NOFLAGS,1.0, SNDPITCH_NORMAL, -1, pos, NULL_VECTOR, true, 0.0);
 		
}
CreateGLprojectile(client, Float:pos[3], Float:dir[3], Float:volicity=500.0, Float:gravity=1.0)
{
	decl Float:v[3];
	CopyVector(dir, v);
	NormalizeVector(v,v);
	ScaleVector(v, volicity);
	new ent=CreateEntityByName("grenade_launcher_projectile");
	//SetEntPropEnt(ent, Prop_Data, "m_hOwnerEntity", client)	;	
	DispatchKeyValue(ent, "model", MODEL_MISSILE); 
	SetEntityGravity(ent, gravity);
	//IgniteEntity(ent, 10.0);
	TeleportEntity(ent, pos, NULL_VECTOR, v);
	DispatchSpawn(ent);  
	SetEntPropFloat(ent, Prop_Send,"m_flModelScale", 4.0);
	
	SetEntProp(ent, Prop_Send, "m_iGlowType", 3);
	SetEntProp(ent, Prop_Send, "m_nGlowRange", 0);
	SetEntProp(ent, Prop_Send, "m_nGlowRangeMin", 10);
	//SetEntProp(ent, Prop_Send, "m_glowColorOverride", 255);
	

	SetEntPropFloat(ent, Prop_Send, "m_fadeMinDist", 10000.0); 
	SetEntPropFloat(ent, Prop_Send, "m_fadeMaxDist", 20000.0); 	
	//SetEntPropFloat(ent, Prop_Send, "fadescale", 0.0); 	
	return ent;
}
FireBullet(client, Float:pos[3], Float:angle[3], Float:hitpos[3])
{
	new Handle:trace= TR_TraceRayFilterEx(pos, angle, MASK_SHOT, RayType_Infinite, TraceRayDontHitSelfAndSurvivor, client); 
	new ent=0;
	new bool:hit=false;
	new bool:alive=false;
	if(TR_DidHit(trace))
	{			
		 
		TR_GetEndPosition(hitpos, trace);
		ent=TR_GetEntityIndex(trace);
		hit=true;
		if(ent>0)
		{			
			decl String:classname[64];
			GetEdictClassname(ent, classname, 64);	
			
			if(ent >=1 && ent<=MaxClients)
			{
				if(GetClientTeam(ent)==2) {}
				alive=true;
			}		 
			else if(StrContains(classname, "door")!=-1){  }
			else if(StrContains(classname, "infected")!=-1){ alive=true;} 			
			else ent=0;
		} 
	}
	CloseHandle(trace); 
	if(ent>0)
	{
		DoPointHurtForInfected(ent, client);
	}
	if(alive)
	{		
		decl Float:Direction[3];
		GetAngleVectors(angle, Direction, NULL_VECTOR, NULL_VECTOR);
		ScaleVector(Direction, -1.0);
		GetVectorAngles(Direction,Direction);
		ShowParticle(hitpos, Direction, PARTICLE_BLOOD, 0.1);
	}	
	else if(hit)
	{
		decl Float:Direction[3];
		Direction[0] = GetRandomFloat(-1.0, 1.0);
		Direction[1] = GetRandomFloat(-1.0, 1.0);
		Direction[2] = GetRandomFloat(-1.0, 1.0);
		TE_SetupSparks(hitpos,Direction,1,3);
		TE_SendToAll();
	}
	return ent;
}

ShowMuzzleFlash(client, Float:pos[3],  Float:angle[3] )
{  
 	new particle = CreateEntityByName("info_particle_system");
	DispatchKeyValue(particle, "effect_name", PARTICLE_MUZZLE_FLASH); 
	DispatchSpawn(particle);
	ActivateEntity(particle); 
	TeleportEntity(particle, pos, angle, NULL_VECTOR);
	AcceptEntityInput(particle, "start");	
	CreateTimer(0.01, DeleteParticles, particle, TIMER_FLAG_NO_MAPCHANGE);
	
}
ShowTrack(client, Float:pos[3], Float:endpos[3] )
{  
 	decl String:temp[16]="";		
	new target = CreateEntityByName("info_particle_target");
	Format(temp, 64, "cptarget%d", target);
	DispatchKeyValue(target, "targetname", temp);	
	TeleportEntity(target, endpos, NULL_VECTOR, NULL_VECTOR); 
	ActivateEntity(target); 
	
	new particle = CreateEntityByName("info_particle_system");
	DispatchKeyValue(particle, "effect_name", PARTICLE_WEAPON_TRACER);
	DispatchKeyValue(particle, "cpoint1", temp);
	DispatchSpawn(particle);
	ActivateEntity(particle); 
	TeleportEntity(particle, pos, NULL_VECTOR, NULL_VECTOR);
	AcceptEntityInput(particle, "start");	
	CreateTimer(0.01, DeleteParticletargets, target, TIMER_FLAG_NO_MAPCHANGE);
	CreateTimer(0.01, DeleteParticles, particle, TIMER_FLAG_NO_MAPCHANGE);
}

 
 
public Action:OnPlayerRunCmd(client, &buttons, &impuls, Float:vel[3], Float:angles[3], &weapon)
{ 
 
	new Float:time=GetEngineTime();
	new lastButton=LastButton[client];
 
	return Plugin_Continue;
} 
public Action:Hook_SetTransmit(entity, client)
{  
 
	return Plugin_Continue;
}
 
IsValidEntS(ent, String:classname[LEN64])
{
	if(IsValidEnt(ent))
	{ 
		decl String:name[LEN64]; 
		GetEdictClassname(ent, name, LEN64); 
		if(StrEqual(classname, name) )
		{
			return true;
		}
	}
	return false;
}
IsValidEnt(ent)
{
	if(ent>0 && IsValidEdict(ent) && IsValidEntity(ent))
	{
		return true;		 
	}
	return false;
}
 

CopyVector(Float:source[3], Float:target[3])
{
	target[0]=source[0];
	target[1]=source[1];
	target[2]=source[2];
}
 
SetVector(Float:target[3], Float:x, Float:y, Float:z)
{
	target[0]=x;
	target[1]=y;
	target[2]=z;
}
 
IsValidClient(client, team=0, bool:includeBot=true, bool:alive=true)
{
	if(client>0 && client<=MaxClients)
	{
		if(IsClientInGame(client))
		{
			if(GetClientTeam(client)!=team && team!=0)return false;
			if(IsFakeClient(client) && !includeBot)return false;			
			if(!IsPlayerAlive(client) && alive)return false;
			return true;
		}
	}
	return false;
}
public Action:tank_killed(Handle:hEvent, const String:strName[], bool:DontBroadcast)
{ 
	new client = GetClientOfUserId(GetEventInt(hEvent, "userid")); 
	if(client>0 && client<=MaxClients)
	{
		new Float:c=GetConVarFloat(l4d_helicopter_chance_tankdrop);
		if(GetRandomFloat(0.0, 100.0)<=c)
		{
			Fuel[client]=GetConVarFloat(l4d_helicopter_fuel); 
			Bullet[client]=GetConVarInt(l4d_helicopter_bullet);
			Bomb[client]=GetConVarInt(l4d_helicopter_bomb);
			DropHelicopter(client);
		}
	}
}
public player_bot_replace(Handle:Spawn_Event, const String:Spawn_Name[], bool:Spawn_Broadcast)
{
	new client = GetClientOfUserId(GetEventInt(Spawn_Event, "player"));
	new bot = GetClientOfUserId(GetEventInt(Spawn_Event, "bot"));   
 
	LostControl(client);
	RemoveHelicopter(bot);
	ResetClientState(client);
	ResetClientState(bot);	

}
public bot_player_replace(Handle:Spawn_Event, const String:Spawn_Name[], bool:Spawn_Broadcast)
{
	new client = GetClientOfUserId(GetEventInt(Spawn_Event, "player"));
	new bot = GetClientOfUserId(GetEventInt(Spawn_Event, "bot"));    
	 
	ResetClientState(client);
	ResetClientState(bot);	 

}
public Action:player_spawn(Handle:hEvent, const String:strName[], bool:DontBroadcast)
{
	new client = GetClientOfUserId(GetEventInt(hEvent, "userid")); 
	ResetClientState(client); 
}
public Action:player_death(Handle:hEvent, const String:strName[], bool:DontBroadcast)
{
	new client = GetClientOfUserId(GetEventInt(hEvent, "userid")); 
	LostControl(client);
	ResetClientState(client);
}
 
public Action:round_start(Handle:event, const String:name[], bool:dontBroadcast)
{
	ResetAllState();
} 
public Action:round_end(Handle:event, const String:name[], bool:dontBroadcast)
{  
	RemoveHelicopterAll();
	ResetAllState();
}
public Action:map_transition(Handle:event, const String:name[], bool:dontBroadcast)
{   
	RemoveHelicopterAll();
	ResetAllState();	
}
 
ResetClientState(client)
{
	DummyEnt[client]=0;
	HelicopterEnt[client]=0;
	HelicopterEnt_other[client]=0;
}
RemoveHelicopterAll()
{
	for(new i=1; i<=MaxClients; i++)
	{
		if(IsClientInGame(i))
		{
			RemoveHelicopter(i);
		}
	}
}
ResetAllState( )
{	
	g_PointHurt=0; 
	
	for(new i=1; i<=MaxClients; i++)
	{
		ResetClientState(i); 
	}
} 
public OnMapStart()
{
	if(L4D2Version)
	{
	 
		g_sprite = PrecacheModel("materials/sprites/laserbeam.vmt");	
		PrecacheModel(MODEL_W_PIPEBOMB);
		PrecacheModel(MODEL_W_MOLOTOV);
		PrecacheModel(MODEL_helicopter); 
		PrecacheModel(MODEL_MISSILE);
		PrecacheSound(SOUND_FLAME, true);
		PrecacheSound(SOUND_ENGINE, true);
		PrecacheSound(SOUND_SHOT, true);	
		PrecacheSound(SOUND_BOMBEXPLODE, true);
		PrecacheSound(SOUND_BOMBDROP, true);
		PrecacheParticle(PARTICLE_BOMBEXPLODE);
		PrecacheParticle(PARTICLE_WEAPON_TRACER);
		PrecacheParticle(PARTICLE_MUZZLE_FLASH);
		PrecacheParticle(PARTICLE_BLOOD);
 	}
	else
	{
		g_sprite = PrecacheModel("materials/sprites/laser.vmt");	
		 
	} 
	ResetAllState();
}
public PrecacheParticle(String:particlename[])
{
	new particle = CreateEntityByName("info_particle_system");
	if (IsValidEdict(particle))
	{
		DispatchKeyValue(particle, "effect_name", particlename);
		DispatchSpawn(particle);
		ActivateEntity(particle);
		AcceptEntityInput(particle, "start");
		CreateTimer(0.01, DeleteParticles, particle, TIMER_FLAG_NO_MAPCHANGE);
	} 
}
public Action:DeleteParticles(Handle:timer, any:particle)
{
	 if (IsValidEntity(particle))
	 {
		 decl String:classname[64];
		 GetEdictClassname(particle, classname, sizeof(classname));
		 if (StrEqual(classname, "info_particle_system", false))
			{
				AcceptEntityInput(particle, "stop");
				AcceptEntityInput(particle, "kill");
				RemoveEdict(particle);
				 
			}
	 }
}
public Action:DeleteParticletargets(Handle:timer, any:target)
{
	 if (IsValidEntity(target))
	 {
		 decl String:classname[64];
		 GetEdictClassname(target, classname, sizeof(classname));
		 if (StrEqual(classname, "info_particle_target", false))
			{
				AcceptEntityInput(target, "stop");
				AcceptEntityInput(target, "kill");
				RemoveEdict(target);
				 
			}
	 }
}
public ShowParticle(Float:pos[3], Float:ang[3],String:particlename[], Float:time)
{
 new particle = CreateEntityByName("info_particle_system");
 if (IsValidEdict(particle))
 {
		
		DispatchKeyValue(particle, "effect_name", particlename); 
		DispatchSpawn(particle);
		ActivateEntity(particle);
		
		
		TeleportEntity(particle, pos, ang, NULL_VECTOR);
		AcceptEntityInput(particle, "start");		
		CreateTimer(time, DeleteParticles, particle, TIMER_FLAG_NO_MAPCHANGE);
		return particle;
 }  
 return 0;
}
GameCheck()
{
	decl String:GameName[16];
	GetConVarString(FindConVar("mp_gamemode"), GameName, sizeof(GameName));
	
	
	if (StrEqual(GameName, "survival", false))
		GameMode = 3;
	else if (StrEqual(GameName, "versus", false) || StrEqual(GameName, "teamversus", false) || StrEqual(GameName, "scavenge", false) || StrEqual(GameName, "teamscavenge", false))
		GameMode = 2;
	else if (StrEqual(GameName, "coop", false) || StrEqual(GameName, "realism", false))
		GameMode = 1;
	else
	{
		GameMode = 0;
 	} 
 
	GetGameFolderName(GameName, sizeof(GameName));
	if (StrEqual(GameName, "left4dead2", false)) 
	{		 
		L4D2Version=true;
	}	
	else
	{
		L4D2Version=false;
	}
	GameMode+=0;
}
 VisiblePlayer(client, bool:visible=true)
{
	if(visible)
	{
		SetEntityRenderMode(client, RENDER_NORMAL);
		SetEntityRenderColor(client, 255, 255, 255, 255);		 
	}
    else
	{
		SetEntityRenderMode(client, RENDER_TRANSCOLOR);
		SetEntityRenderColor(client, 0, 0, 0, 0);
	} 
}
/*
* code from SilverShot, [L4D2] Incapped Crawling with Animation
* */
GotoThirdPerson(client)
{
	SetEntPropEnt(client, Prop_Send, "m_hObserverTarget", 0);
	SetEntProp(client, Prop_Send, "m_iObserverMode", 1);
	SetEntProp(client, Prop_Send, "m_bDrawViewmodel", 0);
}

GotoFirstPerson(client)
{
	SetEntPropEnt(client, Prop_Send, "m_hObserverTarget", -1);
	SetEntProp(client, Prop_Send, "m_iObserverMode", 0);
	SetEntProp(client, Prop_Send, "m_bDrawViewmodel", 1);
} 
 
public bool:TraceRayDontHitSelfAndSurvivor(entity, mask, any:data)
{
	if(entity == data) 
	{
		return false; 
	}
	if(entity>=1 && entity<=MaxClients)
	{
		if(GetClientTeam(entity)==2)
		{
			return false;
		}
	}
	return true;
	if(data>=1 && data<=MaxClients)
	{
		if(HelicopterEnt[data]>0 && entity==HelicopterEnt[data])return false;
	}
	
}
CreatePointHurt()
{
	new pointHurt=CreateEntityByName("point_hurt");
	if(pointHurt)
	{

		DispatchKeyValue(pointHurt,"Damage","10");
		DispatchKeyValue(pointHurt,"DamageType","2");
		DispatchSpawn(pointHurt);
	}
	return pointHurt;
}
new String:N[20];
DoPointHurtForInfected(victim, attacker=0)
{
	if(g_PointHurt > 0)
	{
		if(IsValidEdict(g_PointHurt))
		{
			if(victim>0 && IsValidEdict(victim))
			{		
				Format(N, 20, "target%d", victim);
				DispatchKeyValue(victim,"targetname", N);
				DispatchKeyValue(g_PointHurt,"DamageTarget", N);
				//DispatchKeyValue(g_PointHurt,"classname","");
				DispatchKeyValueFloat(g_PointHurt,"Damage", GetConVarFloat(l4d_helicopter_gun_damage));
				DispatchKeyValue(g_PointHurt,"DamageType","-2130706430");
				AcceptEntityInput(g_PointHurt,"Hurt",(attacker>0)?attacker:-1);
			}
		}
		else g_PointHurt=CreatePointHurt();
	}
	else g_PointHurt=CreatePointHurt();
}
 public Action:DeletePointHurt(Handle:timer, any:ent)
{
	 if (ent> 0 && IsValidEntity(ent) && IsValidEdict(ent))
	 {
		 decl String:classname[64];
		 GetEdictClassname(ent, classname, sizeof(classname));
		 if (StrEqual(classname, "point_hurt", false))
				{
					AcceptEntityInput(ent, "Kill"); 
					RemoveEdict(ent);
				}
		 }

}
public Action:DeletePushForce(Handle:timer, any:ent)
{
	 if (ent> 0 && IsValidEntity(ent) && IsValidEdict(ent))
	 {
		 decl String:classname[64];
		 GetEdictClassname(ent, classname, sizeof(classname));
		 if (StrEqual(classname, "point_push", false))
				{
 					AcceptEntityInput(ent, "Disable");
					AcceptEntityInput(ent, "Kill"); 
					RemoveEdict(ent);
				}
	 }
}
//code modify from  "[L4D & L4D2] Extinguisher and Flamethrower", SilverShot;
CreateButton(entity )
{ 
	decl String:sTemp[16];
	new button;
	new bool:type=false;
	if(type)button = CreateEntityByName("func_button");
	else button = CreateEntityByName("func_button_timed"); 

	Format(sTemp, sizeof(sTemp), "target%d",  button );
	DispatchKeyValue(entity, "targetname", sTemp);
	DispatchKeyValue(button, "glow", sTemp);
	DispatchKeyValue(button, "rendermode", "3");
 
	if(type )
	{
		DispatchKeyValue(button, "spawnflags", "1025");
		DispatchKeyValue(button, "wait", "1");
	}
	else
	{
		DispatchKeyValue(button, "spawnflags", "0");
		DispatchKeyValue(button, "auto_disable", "1");
		Format(sTemp, sizeof(sTemp), "%f", 5.0);
		DispatchKeyValue(button, "use_time", sTemp);
	}
	DispatchSpawn(button);
	AcceptEntityInput(button, "Enable");
	ActivateEntity(button);

	Format(sTemp, sizeof(sTemp), "ft%d", button);
	DispatchKeyValue(entity, "targetname", sTemp);
	SetVariantString(sTemp);
	AcceptEntityInput(button, "SetParent", button, button, 0);
	TeleportEntity(button, Float:{0.0, 0.0, 0.0}, NULL_VECTOR, NULL_VECTOR);

	SetEntProp(button, Prop_Send, "m_nSolidType", 0, 1);
	SetEntProp(button, Prop_Send, "m_usSolidFlags", 4, 2);

	new Float:vMins[3] = {-5.0, -5.0, -5.0}, Float:vMaxs[3] = {5.0, 5.0, 5.0};
	SetEntPropVector(button, Prop_Send, "m_vecMins", vMins);
	SetEntPropVector(button, Prop_Send, "m_vecMaxs", vMaxs);

	if( L4D2Version )
	{
		SetEntProp(button, Prop_Data, "m_CollisionGroup", 1);
		SetEntProp(button, Prop_Send, "m_CollisionGroup", 1);
	}
 

	if( type )
	{	
		HookSingleEntityOutput(button, "OnPressed", OnPressed);
	}
	else
	{
		SetVariantString("OnTimeUp !self:Enable::1:-1");
		AcceptEntityInput(button, "AddOutput");
		HookSingleEntityOutput(button, "OnTimeUp", OnPressed);
	}
	 
	return button;
}
public OnPressed(const String:output[], caller, activator, Float:delay)
{ 
	new Float:f=GetEntPropFloat(caller, Prop_Send, "m_fadeMaxDist");	
	new ent=RoundFloat(f); 
	StopSound(ent, SNDCHAN_AUTO,SOUND_ENGINE);
	AcceptEntityInput(ent, "kill");
	new index=-1;
	for(new i=0; i<=MaxClients; i++)
	{
		if(Info[i][0]==ent)
		{
			Info[i][0]=0;
			index=i;
			break;
		}
	}
	CreateHelicopter(activator, index);
}