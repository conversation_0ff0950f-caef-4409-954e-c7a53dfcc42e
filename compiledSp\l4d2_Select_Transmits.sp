#pragma semicolon 1
#pragma newdecls required

#include <sourcemod>
#include <sdktools>

#define PLUGIN_VERSION "1.0"

float G_SwitchMessageTime;

ConVar C_SwitchMessageTime, C_TransmitsLimit;

int G_TransmitsLimit;

int G_Player[MAXPLAYERS+1];

public Plugin myinfo =
{
	name = "l4d2_Select_Transmits",
	author = "X光",
	description = "指定传送菜单",
	version = PLUGIN_VERSION,
	url = "QQ群59046067"
};

public void OnPluginStart()
{
	RegConsoleCmd("sm_tp", MenuFunc_MainMenu, "传送菜单");

	C_SwitchMessageTime = CreateConVar("switch_message_time",	"10.0",		"设置开局提示卡住传送延迟显示时间/秒(如果为0同时关闭提示和传送功能).", FCVAR_NOTIFY);
	C_TransmitsLimit = CreateConVar("transmits_limit",			"3",		"玩家每回合传送使用次数.", FCVAR_NOTIFY);

	C_SwitchMessageTime.AddChangeHook(CvarChanged);
	C_TransmitsLimit.AddChangeHook(CvarChanged);

	//想要生成cfg把下面一行最前面双斜杠删除
	AutoExecConfig(true, "l4d2_Select_Transmits");
}

public void OnClientPutInServer(int client)
{
	if (IsFakeClient(client))
		return;

	if (G_SwitchMessageTime > 0)
	{
		CreateTimer(G_SwitchMessageTime, TimerAnnounce, GetClientUserId(client), TIMER_FLAG_NO_MAPCHANGE);
	}
}

public Action TimerAnnounce(Handle timer, any client)
{
	if ((client = GetClientOfUserId(client)))
	{
		if (IsClientInGame(client) && GetClientTeam(client) != 3) {
			PrintToChat(client,"\x04[提示]\x05如果卡住或者不知道路可在聊天窗口输入\x03!tp\x05可传送到幸存者队伍中.");
		}
	}
	return Plugin_Continue;
}

//被特感控制判定
bool go_away_from(int client)
{
	if(GetEntPropEnt(client, Prop_Send, "m_pummelAttacker") > 0)
		return true;
	if(GetEntPropEnt(client, Prop_Send, "m_carryAttacker") > 0)
		return true;
	if(GetEntPropEnt(client, Prop_Send, "m_pounceAttacker") > 0)
		return true;
	if(GetEntPropEnt(client, Prop_Send, "m_jockeyAttacker") > 0)
		return true;
	if(GetEntPropEnt(client, Prop_Send, "m_tongueOwner") > 0)
		return true;
	return false;
}

//挂边判定
bool is_survivor_hanging(int client)
{
	return !!GetEntProp(client, Prop_Send, "m_isHangingFromLedge");
}

//倒地判定
bool is_survivor_down(int client)
{
	return !is_survivor_hanging(client) && GetEntProp(client, Prop_Send, "m_isIncapacitated");
}

//传送时强制蹲下防止卡住
void ForceCrouch(int client)
{
	SetEntProp(client, Prop_Send, "m_bDucked", 1);
	SetEntProp(client, Prop_Send, "m_fFlags", GetEntProp(client, Prop_Send, "m_fFlags") | FL_DUCKING);
}

void get_cvars()
{
	G_SwitchMessageTime = C_SwitchMessageTime.FloatValue;
	G_TransmitsLimit = C_TransmitsLimit.IntValue;
}

void CvarChanged(ConVar convar, const char[] oldValue, const char[] newValue)
{
	get_cvars();
}

public void OnConfigsExecuted()
{
	get_cvars();
}

public void OnClientDisconnect_Post(int client)
{
	G_Player[client] = 0;
}

//传送主菜单
public Action MenuFunc_MainMenu(int client, int args)
{
	if (G_SwitchMessageTime <= 0)
	{
		PrintToChat(client, "\x04[提示]\x05传送功能已经关闭.");
		return Plugin_Handled;
	}

	if(G_Player[client] >= G_TransmitsLimit)
	{
		PrintToChat(client, "\x04[提示]\x05你的传送次数已用完.");
		return Plugin_Handled;
	}

	if(go_away_from(client))
	{
		PrintToChat(client, "\x04[提示]\x05被特感控制时禁止使用传送功能.");
		return Plugin_Handled;
	}

	if(is_survivor_hanging(client))
	{
		PrintToChat(client, "\x04[提示]\x05挂边时禁止使用传送功能.");
		return Plugin_Handled;
	}

	if(is_survivor_down(client))
	{
		PrintToChat(client, "\x04[提示]\x05倒地时禁止使用传送功能.");
		return Plugin_Handled;
	}

	if(GetClientTeam(client) == 1)
	{
		PrintToChat(client, "\x04[提示]\x05闲置或旁观时禁止使用传送功能.");
		return Plugin_Handled;
	}

	if(!IsPlayerAlive(client))
	{
		PrintToChat(client, "\x04[提示]\x05死亡状态时禁止使用传送功能.");
		return Plugin_Handled;
	}

	Handle menu = CreateMenu(MenuHandler_EnablePlayer);
	SetMenuTitle(menu, "传送到谁旁边:");
	SetMenuExitBackButton(menu, true);
	char name[32];
	char info[32];

	for (int i = 1; i <= MaxClients; i++)
	{
		if (IsClientInGame(i) && GetClientTeam(i) == 2 && i != client)
		{
			Format(name, sizeof(name), "%N", i);
			Format(info, sizeof(info), "%i", i);
			AddMenuItem(menu, info, name);
		}
	}

	DisplayMenu(menu, client, MENU_TIME_FOREVER);
	return Plugin_Handled;
}

public int MenuHandler_EnablePlayer(Handle menu, MenuAction action, int client, int param)
{
	char name[32];
	char info[32];
	GetMenuItem(menu, param, info, sizeof(info), _, name, sizeof(name));
	int target = StringToInt(info);

	if (action == MenuAction_End)	
		CloseHandle(menu);

	if(action == MenuAction_Select)
	{
		if(!IsClientInGame(target))
		{
			PrintToChat(client, "\x04[提示]\x05传送目标已失效,传送失败.");
			return 0;
		}
		if(GetClientTeam(target) == 3)
		{
			PrintToChat(client, "\x04[提示]\x05传送目标已变更感染者,传送失败.");
			return 0;
		}
		if(is_survivor_hanging(target))
		{
			PrintToChat(client, "\x04[提示]\x05传送目标处于挂边状态,传送失败.");
			return 0;
		}
		if(!IsPlayerAlive(target))
		{
			PrintToChat(client, "\x04[提示]\x05传送目标已死亡,传送失败.");
			return 0;
		}
		if (GetClientTeam(client) == 2)
		{
			G_Player[client]++;
			float Origin[3];
			//传送时强制蹲下防止卡住
			ForceCrouch(client);
			GetClientAbsOrigin(target, Origin);
			TeleportEntity(client, Origin, NULL_VECTOR, NULL_VECTOR);
			PrintToChat(client, "\x04[提示]\x05你已经传送到幸存者\x03%N\x05身边,传送次数还剩\x03%d\x05次.", target, G_TransmitsLimit - G_Player[client]);
			PrintToChat(target, "\x04[提示]\x05幸存者\x03%N已经传送到你身边.", client);
		}
		else
			PrintToChat(client, "\x04[提示]\x05传送功能只限于幸存者使用.");
	}
	return 0;
}