# L4D2 Special Events Bug Fixes Summary

## 修复的Bug

### 1. FireInfected事件 - m_hPhysicsAttacker错误
**问题**: 在SpawnFireProp函数中尝试设置不存在的"m_hPhysicsAttacker"属性，导致报错：
```
Property "m_hPhysicsAttacker" not found (entity 555/prop_physics)
```

**修复**: 
- 移除了错误的SetEntPropEnt调用
- 添加了调试日志来跟踪火焰道具的生成

**修复位置**: l4d2_specials_events_Re.sp 第1177-1181行

### 2. TankControl事件 - 硬直效果无法正常工作
**问题**: Tank攻击玩家时，其他玩家没有受到硬直效果

**修复**:
- 添加了详细的调试日志来跟踪Tank攻击检测
- 改进了伤害检测逻辑的验证
- 添加了硬直效果施加的计数和确认

**修复位置**: l4d2_specials_events_Re.sp 第1091-1158行

### 3. SpitterControl事件 - 脚滑效果无法正常工作
**问题**: 玩家踩到Spitter毒液时没有脚滑失去控制

**修复**:
- 修改了伤害检测逻辑，从检查攻击者是否是Spitter改为检查伤害类型
- 使用正确的Spitter毒液伤害类型：263168 或 265216
- 添加了详细的调试日志和用户提示

**修复位置**: l4d2_specials_events_Re.sp 第1063-1100行

## 技术细节

### Spitter毒液伤害检测
原来的代码错误地检查攻击者是否是Spitter，但Spitter毒液伤害的攻击者通常不是Spitter本身。
正确的方法是检查伤害类型：
- 263168: Spitter毒液伤害类型1
- 265216: Spitter毒液伤害类型2

### 调试日志
为了便于问题诊断，所有修复都添加了详细的调试日志：
- TankControl: 记录Tank攻击检测过程和硬直效果施加
- SpitterControl: 记录毒液伤害检测和脚滑效果施加
- FireInfected: 记录火焰道具生成位置和所有者

## 测试建议

1. **FireInfected测试**:
   - 杀死特感，观察是否还有m_hPhysicsAttacker错误
   - 检查服务器日志中的火焰道具生成记录

2. **TankControl测试**:
   - 激活TankControl事件
   - 让Tank攻击一个玩家
   - 观察其他玩家是否受到硬直效果
   - 检查服务器日志中的详细记录

3. **SpitterControl测试**:
   - 激活SpitterControl事件
   - 让Spitter吐毒液
   - 让玩家踩到毒液
   - 观察玩家是否受到脚滑效果
   - 检查服务器日志中的毒液伤害检测记录

## 注意事项

- 所有调试日志都使用PrintToServer输出到服务器控制台
- 可以通过查看服务器日志来诊断事件是否正常触发
- 如果需要关闭调试日志，可以注释掉相关的PrintToServer调用
