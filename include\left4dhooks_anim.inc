/*
*	Left 4 DHooks Direct
*	Copyright (C) 2023 Silvers
*
*	This program is free software: you can redistribute it and/or modify
*	it under the terms of the GNU General Public License as published by
*	the Free Software Foundation, either version 3 of the License, or
*	(at your option) any later version.
*
*	This program is distributed in the hope that it will be useful,
*	but WITHOUT ANY WARRANTY; without even the implied warranty of
*	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
*	GNU General Public License for more details.
*
*	You should have received a copy of the GNU General Public License
*	along with this program.  If not, see <https://www.gnu.org/licenses/>.
*/

#if defined _l4d_anim_included
 #endinput
#endif
#define _l4d_anim_included

#pragma newdecls required

#include <sdktools>
#include <sdkhooks>

#tryinclude <left4dhooks_silver>
#tryinclude <left4dhooks_stocks>
#tryinclude <left4dhooks_lux_library>





// ====================================================================================================
//									ACT_* ANIMATION VALUES
// ====================================================================================================
// To find list: Search for "ACT_RESET" for example, in the server binary. XRef to the "ActivityList_RegisterSharedActivities" function. View in HexRays to see the full list of constants and values.
// These constants can be used in a plugins source code instead of hard coding the Activity number when working with the animation pre-hook.
// Only for Survivors, not Special Infected.



// ====================================================================================================
//										LEFT 4 DEAD 1
// ====================================================================================================
enum
{
	L4D1_ACT_RESET,
	L4D1_ACT_IDLE,
	L4D1_ACT_TRANSITION,
	L4D1_ACT_COVER,
	L4D1_ACT_COVER_MED,
	L4D1_ACT_COVER_LOW,
	L4D1_ACT_WALK,
	L4D1_ACT_WALK_AIM,
	L4D1_ACT_WALK_CROUCH,
	L4D1_ACT_WALK_CROUCH_AIM,
	L4D1_ACT_RUN,
	L4D1_ACT_RUN_AIM,
	L4D1_ACT_RUN_CROUCH,
	L4D1_ACT_RUN_CROUCH_AIM,
	L4D1_ACT_RUN_PROTECTED,
	L4D1_ACT_SCRIPT_CUSTOM_MOVE,
	L4D1_ACT_RANGE_ATTACK1,
	L4D1_ACT_RANGE_ATTACK2,
	L4D1_ACT_RANGE_ATTACK1_LOW,
	L4D1_ACT_RANGE_ATTACK2_LOW,
	L4D1_ACT_DIESIMPLE,
	L4D1_ACT_DIEBACKWARD,
	L4D1_ACT_DIEFORWARD,
	L4D1_ACT_DIEVIOLENT,
	L4D1_ACT_DIERAGDOLL,
	L4D1_ACT_FLY,
	L4D1_ACT_HOVER,
	L4D1_ACT_GLIDE,
	L4D1_ACT_SWIM,
	L4D1_ACT_JUMP,
	L4D1_ACT_HOP,
	L4D1_ACT_LEAP,
	L4D1_ACT_LAND,
	L4D1_ACT_CLIMB_UP,
	L4D1_ACT_CLIMB_DOWN,
	L4D1_ACT_CLIMB_DISMOUNT,
	L4D1_ACT_SHIPLADDER_UP,
	L4D1_ACT_SHIPLADDER_DOWN,
	L4D1_ACT_STRAFE_LEFT,
	L4D1_ACT_STRAFE_RIGHT,
	L4D1_ACT_ROLL_LEFT,
	L4D1_ACT_ROLL_RIGHT,
	L4D1_ACT_TURN_LEFT,
	L4D1_ACT_TURN_RIGHT,
	L4D1_ACT_CROUCH,
	L4D1_ACT_CROUCHIDLE,
	L4D1_ACT_STAND,
	L4D1_ACT_USE,
	L4D1_ACT_SIGNAL1,
	L4D1_ACT_SIGNAL2,
	L4D1_ACT_SIGNAL3,
	L4D1_ACT_SIGNAL_ADVANCE,
	L4D1_ACT_SIGNAL_FORWARD,
	L4D1_ACT_SIGNAL_GROUP,
	L4D1_ACT_SIGNAL_HALT,
	L4D1_ACT_SIGNAL_LEFT,
	L4D1_ACT_SIGNAL_RIGHT,
	L4D1_ACT_SIGNAL_TAKECOVER,
	L4D1_ACT_LOOKBACK_RIGHT,
	L4D1_ACT_LOOKBACK_LEFT,
	L4D1_ACT_COWER,
	L4D1_ACT_SMALL_FLINCH,
	L4D1_ACT_BIG_FLINCH,
	L4D1_ACT_MELEE_ATTACK1,
	L4D1_ACT_MELEE_ATTACK2,
	L4D1_ACT_RELOAD,
	L4D1_ACT_RELOAD_START,
	L4D1_ACT_RELOAD_FINISH,
	L4D1_ACT_RELOAD_LOW,
	L4D1_ACT_ARM,
	L4D1_ACT_DISARM,
	L4D1_ACT_DROP_WEAPON,
	L4D1_ACT_DROP_WEAPON_SHOTGUN,
	L4D1_ACT_PICKUP_GROUND,
	L4D1_ACT_PICKUP_RACK,
	L4D1_ACT_IDLE_ANGRY,
	L4D1_ACT_IDLE_RELAXED,
	L4D1_ACT_IDLE_STIMULATED,
	L4D1_ACT_IDLE_AGITATED,
	L4D1_ACT_IDLE_STEALTH,
	L4D1_ACT_IDLE_HURT,
	L4D1_ACT_WALK_RELAXED,
	L4D1_ACT_WALK_STIMULATED,
	L4D1_ACT_WALK_AGITATED,
	L4D1_ACT_WALK_STEALTH,
	L4D1_ACT_RUN_RELAXED,
	L4D1_ACT_RUN_STIMULATED,
	L4D1_ACT_RUN_AGITATED,
	L4D1_ACT_RUN_STEALTH,
	L4D1_ACT_IDLE_AIM_RELAXED,
	L4D1_ACT_IDLE_AIM_STIMULATED,
	L4D1_ACT_IDLE_AIM_AGITATED,
	L4D1_ACT_IDLE_AIM_STEALTH,
	L4D1_ACT_WALK_AIM_RELAXED,
	L4D1_ACT_WALK_AIM_STIMULATED,
	L4D1_ACT_WALK_AIM_AGITATED,
	L4D1_ACT_WALK_AIM_STEALTH,
	L4D1_ACT_RUN_AIM_RELAXED,
	L4D1_ACT_RUN_AIM_STIMULATED,
	L4D1_ACT_RUN_AIM_AGITATED,
	L4D1_ACT_RUN_AIM_STEALTH,
	L4D1_ACT_CROUCHIDLE_STIMULATED,
	L4D1_ACT_CROUCHIDLE_AIM_STIMULATED,
	L4D1_ACT_CROUCHIDLE_AGITATED,
	L4D1_ACT_WALK_HURT,
	L4D1_ACT_RUN_HURT,
	L4D1_ACT_SPECIAL_ATTACK1,
	L4D1_ACT_SPECIAL_ATTACK2,
	L4D1_ACT_COMBAT_IDLE,
	L4D1_ACT_WALK_SCARED,
	L4D1_ACT_RUN_SCARED,
	L4D1_ACT_VICTORY_DANCE,
	L4D1_ACT_DIE_HEADSHOT,
	L4D1_ACT_DIE_CHESTSHOT,
	L4D1_ACT_DIE_GUTSHOT,
	L4D1_ACT_DIE_BACKSHOT,
	L4D1_ACT_FLINCH_HEAD,
	L4D1_ACT_FLINCH_CHEST,
	L4D1_ACT_FLINCH_STOMACH,
	L4D1_ACT_FLINCH_LEFTARM,
	L4D1_ACT_FLINCH_RIGHTARM,
	L4D1_ACT_FLINCH_LEFTLEG,
	L4D1_ACT_FLINCH_RIGHTLEG,
	L4D1_ACT_FLINCH_PHYSICS,
	L4D1_ACT_FLINCH_HEAD_BACK,
	L4D1_ACT_FLINCH_CHEST_BACK,
	L4D1_ACT_FLINCH_STOMACH_BACK,
	L4D1_ACT_FLINCH_CROUCH_FRONT,
	L4D1_ACT_FLINCH_CROUCH_BACK,
	L4D1_ACT_FLINCH_CROUCH_LEFT,
	L4D1_ACT_FLINCH_CROUCH_RIGHT,
	L4D1_ACT_IDLE_ON_FIRE,
	L4D1_ACT_WALK_ON_FIRE,
	L4D1_ACT_RUN_ON_FIRE,
	L4D1_ACT_RAPPEL_LOOP,
	L4D1_ACT_180_LEFT,
	L4D1_ACT_180_RIGHT,
	L4D1_ACT_90_LEFT,
	L4D1_ACT_90_RIGHT,
	L4D1_ACT_STEP_LEFT,
	L4D1_ACT_STEP_RIGHT,
	L4D1_ACT_STEP_BACK,
	L4D1_ACT_STEP_FORE,
	L4D1_ACT_GESTURE_RANGE_ATTACK1,
	L4D1_ACT_GESTURE_RANGE_ATTACK2,
	L4D1_ACT_GESTURE_MELEE_ATTACK1,
	L4D1_ACT_GESTURE_MELEE_ATTACK2,
	L4D1_ACT_GESTURE_RANGE_ATTACK1_LOW,
	L4D1_ACT_GESTURE_RANGE_ATTACK2_LOW,
	L4D1_ACT_MELEE_ATTACK_SWING_GESTURE,
	L4D1_ACT_GESTURE_SMALL_FLINCH,
	L4D1_ACT_GESTURE_BIG_FLINCH,
	L4D1_ACT_GESTURE_FLINCH_BLAST,
	L4D1_ACT_GESTURE_FLINCH_BLAST_SHOTGUN,
	L4D1_ACT_GESTURE_FLINCH_BLAST_DAMAGED,
	L4D1_ACT_GESTURE_FLINCH_BLAST_DAMAGED_SHOTGUN,
	L4D1_ACT_GESTURE_FLINCH_HEAD,
	L4D1_ACT_GESTURE_FLINCH_CHEST,
	L4D1_ACT_GESTURE_FLINCH_STOMACH,
	L4D1_ACT_GESTURE_FLINCH_LEFTARM,
	L4D1_ACT_GESTURE_FLINCH_RIGHTARM,
	L4D1_ACT_GESTURE_FLINCH_LEFTLEG,
	L4D1_ACT_GESTURE_FLINCH_RIGHTLEG,
	L4D1_ACT_GESTURE_TURN_LEFT,
	L4D1_ACT_GESTURE_TURN_RIGHT,
	L4D1_ACT_GESTURE_TURN_LEFT45,
	L4D1_ACT_GESTURE_TURN_RIGHT45,
	L4D1_ACT_GESTURE_TURN_LEFT90,
	L4D1_ACT_GESTURE_TURN_RIGHT90,
	L4D1_ACT_GESTURE_TURN_LEFT45_FLAT,
	L4D1_ACT_GESTURE_TURN_RIGHT45_FLAT,
	L4D1_ACT_GESTURE_TURN_LEFT90_FLAT,
	L4D1_ACT_GESTURE_TURN_RIGHT90_FLAT,
	L4D1_ACT_BARNACLE_HIT,
	L4D1_ACT_BARNACLE_PULL,
	L4D1_ACT_BARNACLE_CHOMP,
	L4D1_ACT_BARNACLE_CHEW,
	L4D1_ACT_DO_NOT_DISTURB,
	L4D1_ACT_VM_DRAW,
	L4D1_ACT_VM_HOLSTER,
	L4D1_ACT_VM_IDLE,
	L4D1_ACT_VM_FIDGET,
	L4D1_ACT_VM_PULLBACK,
	L4D1_ACT_VM_PULLBACK_HIGH,
	L4D1_ACT_VM_PULLBACK_LOW,
	L4D1_ACT_VM_THROW,
	L4D1_ACT_VM_PULLPIN,
	L4D1_ACT_VM_PRIMARYATTACK,
	L4D1_ACT_VM_SECONDARYATTACK,
	L4D1_ACT_VM_RELOAD,
	L4D1_ACT_VM_DRYFIRE,
	L4D1_ACT_VM_HITLEFT,
	L4D1_ACT_VM_HITLEFT2,
	L4D1_ACT_VM_HITRIGHT,
	L4D1_ACT_VM_HITRIGHT2,
	L4D1_ACT_VM_HITCENTER,
	L4D1_ACT_VM_HITCENTER2,
	L4D1_ACT_VM_MISSLEFT,
	L4D1_ACT_VM_MISSLEFT2,
	L4D1_ACT_VM_MISSRIGHT,
	L4D1_ACT_VM_MISSRIGHT2,
	L4D1_ACT_VM_MISSCENTER,
	L4D1_ACT_VM_MISSCENTER2,
	L4D1_ACT_VM_HAULBACK,
	L4D1_ACT_VM_SWINGHARD,
	L4D1_ACT_VM_SWINGMISS,
	L4D1_ACT_VM_SWINGHIT,
	L4D1_ACT_VM_IDLE_TO_LOWERED,
	L4D1_ACT_VM_IDLE_LOWERED,
	L4D1_ACT_VM_LOWERED_TO_IDLE,
	L4D1_ACT_VM_RECOIL1,
	L4D1_ACT_VM_RECOIL2,
	L4D1_ACT_VM_RECOIL3,
	L4D1_ACT_VM_PICKUP,
	L4D1_ACT_VM_RELEASE,
	L4D1_ACT_VM_ATTACH_SILENCER,
	L4D1_ACT_VM_DETACH_SILENCER,
	L4D1_ACT_SLAM_STICKWALL_IDLE,
	L4D1_ACT_SLAM_STICKWALL_ND_IDLE,
	L4D1_ACT_SLAM_STICKWALL_ATTACH,
	L4D1_ACT_SLAM_STICKWALL_ATTACH2,
	L4D1_ACT_SLAM_STICKWALL_ND_ATTACH,
	L4D1_ACT_SLAM_STICKWALL_ND_ATTACH2,
	L4D1_ACT_SLAM_STICKWALL_DETONATE,
	L4D1_ACT_SLAM_STICKWALL_DETONATOR_HOLSTER,
	L4D1_ACT_SLAM_STICKWALL_DRAW,
	L4D1_ACT_SLAM_STICKWALL_ND_DRAW,
	L4D1_ACT_SLAM_STICKWALL_TO_THROW,
	L4D1_ACT_SLAM_STICKWALL_TO_THROW_ND,
	L4D1_ACT_SLAM_STICKWALL_TO_TRIPMINE_ND,
	L4D1_ACT_SLAM_THROW_IDLE,
	L4D1_ACT_SLAM_THROW_ND_IDLE,
	L4D1_ACT_SLAM_THROW_THROW,
	L4D1_ACT_SLAM_THROW_THROW2,
	L4D1_ACT_SLAM_THROW_THROW_ND,
	L4D1_ACT_SLAM_THROW_THROW_ND2,
	L4D1_ACT_SLAM_THROW_DRAW,
	L4D1_ACT_SLAM_THROW_ND_DRAW,
	L4D1_ACT_SLAM_THROW_TO_STICKWALL,
	L4D1_ACT_SLAM_THROW_TO_STICKWALL_ND,
	L4D1_ACT_SLAM_THROW_DETONATE,
	L4D1_ACT_SLAM_THROW_DETONATOR_HOLSTER,
	L4D1_ACT_SLAM_THROW_TO_TRIPMINE_ND,
	L4D1_ACT_SLAM_TRIPMINE_IDLE,
	L4D1_ACT_SLAM_TRIPMINE_DRAW,
	L4D1_ACT_SLAM_TRIPMINE_ATTACH,
	L4D1_ACT_SLAM_TRIPMINE_ATTACH2,
	L4D1_ACT_SLAM_TRIPMINE_TO_STICKWALL_ND,
	L4D1_ACT_SLAM_TRIPMINE_TO_THROW_ND,
	L4D1_ACT_SLAM_DETONATOR_IDLE,
	L4D1_ACT_SLAM_DETONATOR_DRAW,
	L4D1_ACT_SLAM_DETONATOR_DETONATE,
	L4D1_ACT_SLAM_DETONATOR_HOLSTER,
	L4D1_ACT_SLAM_DETONATOR_STICKWALL_DRAW,
	L4D1_ACT_SLAM_DETONATOR_THROW_DRAW,
	L4D1_ACT_SHOTGUN_RELOAD_START,
	L4D1_ACT_SHOTGUN_RELOAD_FINISH,
	L4D1_ACT_SHOTGUN_PUMP,
	L4D1_ACT_SMG2_IDLE2,
	L4D1_ACT_SMG2_FIRE2,
	L4D1_ACT_SMG2_DRAW2,
	L4D1_ACT_SMG2_RELOAD2,
	L4D1_ACT_SMG2_DRYFIRE2,
	L4D1_ACT_SMG2_TOAUTO,
	L4D1_ACT_SMG2_TOBURST,
	L4D1_ACT_PHYSCANNON_UPGRADE,
	L4D1_ACT_RANGE_ATTACK_AR1,
	L4D1_ACT_RANGE_ATTACK_AR2,
	L4D1_ACT_RANGE_ATTACK_AR2_LOW,
	L4D1_ACT_RANGE_ATTACK_AR2_GRENADE,
	L4D1_ACT_RANGE_ATTACK_HMG1,
	L4D1_ACT_RANGE_ATTACK_ML,
	L4D1_ACT_RANGE_ATTACK_SMG1,
	L4D1_ACT_RANGE_ATTACK_SMG1_LOW,
	L4D1_ACT_RANGE_ATTACK_SMG2,
	L4D1_ACT_RANGE_ATTACK_SHOTGUN,
	L4D1_ACT_RANGE_ATTACK_SHOTGUN_LOW,
	L4D1_ACT_RANGE_ATTACK_PISTOL,
	L4D1_ACT_RANGE_ATTACK_PISTOL_LOW,
	L4D1_ACT_RANGE_ATTACK_SLAM,
	L4D1_ACT_RANGE_ATTACK_TRIPWIRE,
	L4D1_ACT_RANGE_ATTACK_THROW,
	L4D1_ACT_RANGE_ATTACK_SNIPER_RIFLE,
	L4D1_ACT_RANGE_ATTACK_RPG,
	L4D1_ACT_MELEE_ATTACK_SWING,
	L4D1_ACT_RANGE_AIM_LOW,
	L4D1_ACT_RANGE_AIM_SMG1_LOW,
	L4D1_ACT_RANGE_AIM_PISTOL_LOW,
	L4D1_ACT_RANGE_AIM_AR2_LOW,
	L4D1_ACT_COVER_PISTOL_LOW,
	L4D1_ACT_COVER_SMG1_LOW,
	L4D1_ACT_GESTURE_RANGE_ATTACK_AR1,
	L4D1_ACT_GESTURE_RANGE_ATTACK_AR2,
	L4D1_ACT_GESTURE_RANGE_ATTACK_AR2_GRENADE,
	L4D1_ACT_GESTURE_RANGE_ATTACK_HMG1,
	L4D1_ACT_GESTURE_RANGE_ATTACK_ML,
	L4D1_ACT_GESTURE_RANGE_ATTACK_SMG1,
	L4D1_ACT_GESTURE_RANGE_ATTACK_SMG1_LOW,
	L4D1_ACT_GESTURE_RANGE_ATTACK_SMG2,
	L4D1_ACT_GESTURE_RANGE_ATTACK_SHOTGUN,
	L4D1_ACT_GESTURE_RANGE_ATTACK_PISTOL,
	L4D1_ACT_GESTURE_RANGE_ATTACK_PISTOL_LOW,
	L4D1_ACT_GESTURE_RANGE_ATTACK_SLAM,
	L4D1_ACT_GESTURE_RANGE_ATTACK_TRIPWIRE,
	L4D1_ACT_GESTURE_RANGE_ATTACK_THROW,
	L4D1_ACT_GESTURE_RANGE_ATTACK_SNIPER_RIFLE,
	L4D1_ACT_GESTURE_MELEE_ATTACK_SWING,
	L4D1_ACT_IDLE_RIFLE,
	L4D1_ACT_IDLE_SMG1,
	L4D1_ACT_IDLE_ANGRY_SMG1,
	L4D1_ACT_IDLE_PISTOL,
	L4D1_ACT_IDLE_ANGRY_PISTOL,
	L4D1_ACT_IDLE_ANGRY_SHOTGUN,
	L4D1_ACT_IDLE_STEALTH_PISTOL,
	L4D1_ACT_IDLE_PACKAGE,
	L4D1_ACT_WALK_PACKAGE,
	L4D1_ACT_IDLE_SUITCASE,
	L4D1_ACT_WALK_SUITCASE,
	L4D1_ACT_IDLE_SMG1_RELAXED,
	L4D1_ACT_IDLE_SMG1_STIMULATED,
	L4D1_ACT_WALK_RIFLE_RELAXED,
	L4D1_ACT_RUN_RIFLE_RELAXED,
	L4D1_ACT_WALK_RIFLE_STIMULATED,
	L4D1_ACT_RUN_RIFLE_STIMULATED,
	L4D1_ACT_IDLE_AIM_RIFLE_STIMULATED,
	L4D1_ACT_WALK_AIM_RIFLE_STIMULATED,
	L4D1_ACT_RUN_AIM_RIFLE_STIMULATED,
	L4D1_ACT_IDLE_SHOTGUN_RELAXED,
	L4D1_ACT_IDLE_SHOTGUN_STIMULATED,
	L4D1_ACT_IDLE_SHOTGUN_AGITATED,
	L4D1_ACT_WALK_ANGRY,
	L4D1_ACT_POLICE_HARASS1,
	L4D1_ACT_POLICE_HARASS2,
	L4D1_ACT_IDLE_MANNEDGUN,
	L4D1_ACT_IDLE_MELEE,
	L4D1_ACT_IDLE_ANGRY_MELEE,
	L4D1_ACT_IDLE_RPG_RELAXED,
	L4D1_ACT_IDLE_RPG,
	L4D1_ACT_IDLE_ANGRY_RPG,
	L4D1_ACT_COVER_LOW_RPG,
	L4D1_ACT_WALK_RPG,
	L4D1_ACT_RUN_RPG,
	L4D1_ACT_WALK_CROUCH_RPG,
	L4D1_ACT_RUN_CROUCH_RPG,
	L4D1_ACT_WALK_RPG_RELAXED,
	L4D1_ACT_RUN_RPG_RELAXED,
	L4D1_ACT_WALK_RIFLE,
	L4D1_ACT_WALK_AIM_RIFLE,
	L4D1_ACT_WALK_CROUCH_RIFLE,
	L4D1_ACT_WALK_CROUCH_AIM_RIFLE,
	L4D1_ACT_RUN_RIFLE,
	L4D1_ACT_RUN_AIM_RIFLE,
	L4D1_ACT_RUN_CROUCH_RIFLE,
	L4D1_ACT_RUN_CROUCH_AIM_RIFLE,
	L4D1_ACT_RUN_STEALTH_PISTOL,
	L4D1_ACT_WALK_AIM_SHOTGUN,
	L4D1_ACT_RUN_AIM_SHOTGUN,
	L4D1_ACT_WALK_PISTOL,
	L4D1_ACT_RUN_PISTOL,
	L4D1_ACT_WALK_AIM_PISTOL,
	L4D1_ACT_RUN_AIM_PISTOL,
	L4D1_ACT_WALK_STEALTH_PISTOL,
	L4D1_ACT_WALK_AIM_STEALTH_PISTOL,
	L4D1_ACT_RUN_AIM_STEALTH_PISTOL,
	L4D1_ACT_RELOAD_PISTOL,
	L4D1_ACT_RELOAD_PISTOL_LOW,
	L4D1_ACT_RELOAD_SMG1,
	L4D1_ACT_RELOAD_SMG1_LOW,
	L4D1_ACT_RELOAD_SHOTGUN,
	L4D1_ACT_RELOAD_SHOTGUN_LOW,
	L4D1_ACT_GESTURE_RELOAD,
	L4D1_ACT_GESTURE_RELOAD_PISTOL,
	L4D1_ACT_GESTURE_RELOAD_SMG1,
	L4D1_ACT_GESTURE_RELOAD_SHOTGUN,
	L4D1_ACT_BUSY_LEAN_LEFT,
	L4D1_ACT_BUSY_LEAN_LEFT_ENTRY,
	L4D1_ACT_BUSY_LEAN_LEFT_EXIT,
	L4D1_ACT_BUSY_LEAN_BACK,
	L4D1_ACT_BUSY_LEAN_BACK_ENTRY,
	L4D1_ACT_BUSY_LEAN_BACK_EXIT,
	L4D1_ACT_BUSY_SIT_GROUND,
	L4D1_ACT_BUSY_SIT_GROUND_ENTRY,
	L4D1_ACT_BUSY_SIT_GROUND_EXIT,
	L4D1_ACT_BUSY_SIT_CHAIR,
	L4D1_ACT_BUSY_SIT_CHAIR_ENTRY,
	L4D1_ACT_BUSY_SIT_CHAIR_EXIT,
	L4D1_ACT_BUSY_STAND,
	L4D1_ACT_BUSY_QUEUE,
	L4D1_ACT_DUCK_DODGE,
	L4D1_ACT_DIE_BARNACLE_SWALLOW,
	L4D1_ACT_GESTURE_BARNACLE_STRANGLE,
	L4D1_ACT_PHYSCANNON_DETACH,
	L4D1_ACT_PHYSCANNON_ANIMATE,
	L4D1_ACT_PHYSCANNON_ANIMATE_PRE,
	L4D1_ACT_PHYSCANNON_ANIMATE_POST,
	L4D1_ACT_DIE_FRONTSIDE,
	L4D1_ACT_DIE_RIGHTSIDE,
	L4D1_ACT_DIE_BACKSIDE,
	L4D1_ACT_DIE_LEFTSIDE,
	L4D1_ACT_DIE_CROUCH_FRONTSIDE,
	L4D1_ACT_DIE_CROUCH_RIGHTSIDE,
	L4D1_ACT_DIE_CROUCH_BACKSIDE,
	L4D1_ACT_DIE_CROUCH_LEFTSIDE,
	L4D1_ACT_DIE_INCAP,
	L4D1_ACT_OPEN_DOOR,
	L4D1_ACT_DI_ALYX_ZOMBIE_MELEE,
	L4D1_ACT_DI_ALYX_ZOMBIE_TORSO_MELEE,
	L4D1_ACT_DI_ALYX_HEADCRAB_MELEE,
	L4D1_ACT_DI_ALYX_ANTLION,
	L4D1_ACT_DI_ALYX_ZOMBIE_SHOTGUN64,
	L4D1_ACT_DI_ALYX_ZOMBIE_SHOTGUN26,
	L4D1_ACT_READINESS_RELAXED_TO_STIMULATED,
	L4D1_ACT_READINESS_RELAXED_TO_STIMULATED_WALK,
	L4D1_ACT_READINESS_AGITATED_TO_STIMULATED,
	L4D1_ACT_READINESS_STIMULATED_TO_RELAXED,
	L4D1_ACT_READINESS_PISTOL_RELAXED_TO_STIMULATED,
	L4D1_ACT_READINESS_PISTOL_RELAXED_TO_STIMULATED_WALK,
	L4D1_ACT_READINESS_PISTOL_AGITATED_TO_STIMULATED,
	L4D1_ACT_READINESS_PISTOL_STIMULATED_TO_RELAXED,
	L4D1_ACT_IDLE_CARRY,
	L4D1_ACT_WALK_CARRY,
	L4D1_ACT_STARTDYING,
	L4D1_ACT_DYINGLOOP,
	L4D1_ACT_DYINGTODEAD,
	L4D1_ACT_RIDE_MANNED_GUN,
	L4D1_ACT_VM_SPRINT_ENTER,
	L4D1_ACT_VM_SPRINT_IDLE,
	L4D1_ACT_VM_SPRINT_LEAVE,
	L4D1_ACT_FIRE_START,
	L4D1_ACT_FIRE_LOOP,
	L4D1_ACT_FIRE_END,
	L4D1_ACT_CROUCHING_GRENADEIDLE,
	L4D1_ACT_CROUCHING_GRENADEREADY,
	L4D1_ACT_CROUCHING_PRIMARYATTACK,
	L4D1_ACT_OVERLAY_GRENADEIDLE,
	L4D1_ACT_OVERLAY_GRENADEREADY,
	L4D1_ACT_OVERLAY_PRIMARYATTACK,
	L4D1_ACT_OVERLAY_SHIELD_UP,
	L4D1_ACT_OVERLAY_SHIELD_DOWN,
	L4D1_ACT_OVERLAY_SHIELD_UP_IDLE,
	L4D1_ACT_OVERLAY_SHIELD_ATTACK,
	L4D1_ACT_OVERLAY_SHIELD_KNOCKBACK,
	L4D1_ACT_SHIELD_UP,
	L4D1_ACT_SHIELD_DOWN,
	L4D1_ACT_SHIELD_UP_IDLE,
	L4D1_ACT_SHIELD_ATTACK,
	L4D1_ACT_SHIELD_KNOCKBACK,
	L4D1_ACT_CROUCHING_SHIELD_UP,
	L4D1_ACT_CROUCHING_SHIELD_DOWN,
	L4D1_ACT_CROUCHING_SHIELD_UP_IDLE,
	L4D1_ACT_CROUCHING_SHIELD_ATTACK,
	L4D1_ACT_CROUCHING_SHIELD_KNOCKBACK,
	L4D1_ACT_TURNRIGHT45,
	L4D1_ACT_TURNLEFT45,
	L4D1_ACT_TURN,
	L4D1_ACT_OBJ_ASSEMBLING,
	L4D1_ACT_OBJ_DISMANTLING,
	L4D1_ACT_OBJ_STARTUP,
	L4D1_ACT_OBJ_RUNNING,
	L4D1_ACT_OBJ_IDLE,
	L4D1_ACT_OBJ_PLACING,
	L4D1_ACT_OBJ_DETERIORATING,
	L4D1_ACT_OBJ_UPGRADING,
	L4D1_ACT_DEPLOY,
	L4D1_ACT_DEPLOY_IDLE,
	L4D1_ACT_UNDEPLOY,
	L4D1_ACT_GRENADE_ROLL,
	L4D1_ACT_GRENADE_TOSS,
	L4D1_ACT_HANDGRENADE_THROW1,
	L4D1_ACT_HANDGRENADE_THROW2,
	L4D1_ACT_HANDGRENADE_THROW3,
	L4D1_ACT_SHOTGUN_IDLE_DEEP,
	L4D1_ACT_SHOTGUN_IDLE4,
	L4D1_ACT_GLOCK_SHOOTEMPTY,
	L4D1_ACT_GLOCK_SHOOT_RELOAD,
	L4D1_ACT_RPG_DRAW_UNLOADED,
	L4D1_ACT_RPG_HOLSTER_UNLOADED,
	L4D1_ACT_RPG_IDLE_UNLOADED,
	L4D1_ACT_RPG_FIDGET_UNLOADED,
	L4D1_ACT_CROSSBOW_DRAW_UNLOADED,
	L4D1_ACT_CROSSBOW_IDLE_UNLOADED,
	L4D1_ACT_CROSSBOW_FIDGET_UNLOADED,
	L4D1_ACT_GAUSS_SPINUP,
	L4D1_ACT_GAUSS_SPINCYCLE,
	L4D1_ACT_TRIPMINE_GROUND,
	L4D1_ACT_TRIPMINE_WORLD,
	L4D1_ACT_VM_PRIMARYATTACK_SILENCED,
	L4D1_ACT_VM_RELOAD_SILENCED,
	L4D1_ACT_VM_DRYFIRE_SILENCED,
	L4D1_ACT_VM_IDLE_SILENCED,
	L4D1_ACT_VM_DRAW_SILENCED,
	L4D1_ACT_VM_IDLE_EMPTY_LEFT,
	L4D1_ACT_VM_DRYFIRE_LEFT,
	L4D1_ACT_PLAYER_IDLE_FIRE,
	L4D1_ACT_PLAYER_CROUCH_FIRE,
	L4D1_ACT_PLAYER_CROUCH_WALK_FIRE,
	L4D1_ACT_PLAYER_WALK_FIRE,
	L4D1_ACT_PLAYER_RUN_FIRE,
	L4D1_ACT_IDLETORUN,
	L4D1_ACT_RUNTOIDLE,
	L4D1_ACT_SPRINT,
	L4D1_ACT_GET_DOWN_STAND,
	L4D1_ACT_GET_UP_STAND,
	L4D1_ACT_GET_DOWN_CROUCH,
	L4D1_ACT_GET_UP_CROUCH,
	L4D1_ACT_PRONE_FORWARD,
	L4D1_ACT_PRONE_IDLE,
	L4D1_ACT_DEEPIDLE1,
	L4D1_ACT_DEEPIDLE2,
	L4D1_ACT_DEEPIDLE3,
	L4D1_ACT_DEEPIDLE4,
	L4D1_ACT_VM_RELOAD_DEPLOYED,
	L4D1_ACT_VM_RELOAD_IDLE,
	L4D1_ACT_VM_DRAW_DEPLOYED,
	L4D1_ACT_VM_DRAW_EMPTY,
	L4D1_ACT_VM_PRIMARYATTACK_EMPTY,
	L4D1_ACT_VM_RELOAD_EMPTY,
	L4D1_ACT_VM_IDLE_EMPTY,
	L4D1_ACT_VM_IDLE_DEPLOYED_EMPTY,
	L4D1_ACT_VM_IDLE_8,
	L4D1_ACT_VM_IDLE_7,
	L4D1_ACT_VM_IDLE_6,
	L4D1_ACT_VM_IDLE_5,
	L4D1_ACT_VM_IDLE_4,
	L4D1_ACT_VM_IDLE_3,
	L4D1_ACT_VM_IDLE_2,
	L4D1_ACT_VM_IDLE_1,
	L4D1_ACT_VM_IDLE_DEPLOYED,
	L4D1_ACT_VM_IDLE_DEPLOYED_8,
	L4D1_ACT_VM_IDLE_DEPLOYED_7,
	L4D1_ACT_VM_IDLE_DEPLOYED_6,
	L4D1_ACT_VM_IDLE_DEPLOYED_5,
	L4D1_ACT_VM_IDLE_DEPLOYED_4,
	L4D1_ACT_VM_IDLE_DEPLOYED_3,
	L4D1_ACT_VM_IDLE_DEPLOYED_2,
	L4D1_ACT_VM_IDLE_DEPLOYED_1,
	L4D1_ACT_VM_UNDEPLOY,
	L4D1_ACT_VM_UNDEPLOY_8,
	L4D1_ACT_VM_UNDEPLOY_7,
	L4D1_ACT_VM_UNDEPLOY_6,
	L4D1_ACT_VM_UNDEPLOY_5,
	L4D1_ACT_VM_UNDEPLOY_4,
	L4D1_ACT_VM_UNDEPLOY_3,
	L4D1_ACT_VM_UNDEPLOY_2,
	L4D1_ACT_VM_UNDEPLOY_1,
	L4D1_ACT_VM_UNDEPLOY_EMPTY,
	L4D1_ACT_VM_DEPLOY,
	L4D1_ACT_VM_DEPLOY_8,
	L4D1_ACT_VM_DEPLOY_7,
	L4D1_ACT_VM_DEPLOY_6,
	L4D1_ACT_VM_DEPLOY_5,
	L4D1_ACT_VM_DEPLOY_4,
	L4D1_ACT_VM_DEPLOY_3,
	L4D1_ACT_VM_DEPLOY_2,
	L4D1_ACT_VM_DEPLOY_1,
	L4D1_ACT_VM_DEPLOY_EMPTY,
	L4D1_ACT_VM_PRIMARYATTACK_8,
	L4D1_ACT_VM_PRIMARYATTACK_7,
	L4D1_ACT_VM_PRIMARYATTACK_6,
	L4D1_ACT_VM_PRIMARYATTACK_5,
	L4D1_ACT_VM_PRIMARYATTACK_4,
	L4D1_ACT_VM_PRIMARYATTACK_3,
	L4D1_ACT_VM_PRIMARYATTACK_2,
	L4D1_ACT_VM_PRIMARYATTACK_1,
	L4D1_ACT_VM_PRIMARYATTACK_DEPLOYED,
	L4D1_ACT_VM_PRIMARYATTACK_DEPLOYED_8,
	L4D1_ACT_VM_PRIMARYATTACK_DEPLOYED_7,
	L4D1_ACT_VM_PRIMARYATTACK_DEPLOYED_6,
	L4D1_ACT_VM_PRIMARYATTACK_DEPLOYED_5,
	L4D1_ACT_VM_PRIMARYATTACK_DEPLOYED_4,
	L4D1_ACT_VM_PRIMARYATTACK_DEPLOYED_3,
	L4D1_ACT_VM_PRIMARYATTACK_DEPLOYED_2,
	L4D1_ACT_VM_PRIMARYATTACK_DEPLOYED_1,
	L4D1_ACT_VM_PRIMARYATTACK_DEPLOYED_EMPTY,
	L4D1_ACT_DOD_DEPLOYED,
	L4D1_ACT_DOD_PRONE_DEPLOYED,
	L4D1_ACT_DOD_IDLE_ZOOMED,
	L4D1_ACT_DOD_WALK_ZOOMED,
	L4D1_ACT_DOD_CROUCH_ZOOMED,
	L4D1_ACT_DOD_CROUCHWALK_ZOOMED,
	L4D1_ACT_DOD_PRONE_ZOOMED,
	L4D1_ACT_DOD_PRONE_FORWARD_ZOOMED,
	L4D1_ACT_DOD_PRIMARYATTACK_DEPLOYED,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_DEPLOYED,
	L4D1_ACT_DOD_RELOAD_DEPLOYED,
	L4D1_ACT_DOD_RELOAD_PRONE_DEPLOYED,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE,
	L4D1_ACT_DOD_SECONDARYATTACK_PRONE,
	L4D1_ACT_DOD_RELOAD_CROUCH,
	L4D1_ACT_DOD_RELOAD_PRONE,
	L4D1_ACT_DOD_STAND_IDLE,
	L4D1_ACT_DOD_STAND_AIM,
	L4D1_ACT_DOD_CROUCH_IDLE,
	L4D1_ACT_DOD_CROUCH_AIM,
	L4D1_ACT_DOD_CROUCHWALK_IDLE,
	L4D1_ACT_DOD_CROUCHWALK_AIM,
	L4D1_ACT_DOD_WALK_IDLE,
	L4D1_ACT_DOD_WALK_AIM,
	L4D1_ACT_DOD_RUN_IDLE,
	L4D1_ACT_DOD_RUN_AIM,
	L4D1_ACT_DOD_STAND_AIM_PISTOL,
	L4D1_ACT_DOD_CROUCH_AIM_PISTOL,
	L4D1_ACT_DOD_CROUCHWALK_AIM_PISTOL,
	L4D1_ACT_DOD_WALK_AIM_PISTOL,
	L4D1_ACT_DOD_RUN_AIM_PISTOL,
	L4D1_ACT_DOD_PRONE_AIM_PISTOL,
	L4D1_ACT_DOD_STAND_IDLE_PISTOL,
	L4D1_ACT_DOD_CROUCH_IDLE_PISTOL,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_PISTOL,
	L4D1_ACT_DOD_WALK_IDLE_PISTOL,
	L4D1_ACT_DOD_RUN_IDLE_PISTOL,
	L4D1_ACT_DOD_SPRINT_IDLE_PISTOL,
	L4D1_ACT_DOD_PRONEWALK_IDLE_PISTOL,
	L4D1_ACT_DOD_STAND_AIM_C96,
	L4D1_ACT_DOD_CROUCH_AIM_C96,
	L4D1_ACT_DOD_CROUCHWALK_AIM_C96,
	L4D1_ACT_DOD_WALK_AIM_C96,
	L4D1_ACT_DOD_RUN_AIM_C96,
	L4D1_ACT_DOD_PRONE_AIM_C96,
	L4D1_ACT_DOD_STAND_IDLE_C96,
	L4D1_ACT_DOD_CROUCH_IDLE_C96,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_C96,
	L4D1_ACT_DOD_WALK_IDLE_C96,
	L4D1_ACT_DOD_RUN_IDLE_C96,
	L4D1_ACT_DOD_SPRINT_IDLE_C96,
	L4D1_ACT_DOD_PRONEWALK_IDLE_C96,
	L4D1_ACT_DOD_STAND_AIM_RIFLE,
	L4D1_ACT_DOD_CROUCH_AIM_RIFLE,
	L4D1_ACT_DOD_CROUCHWALK_AIM_RIFLE,
	L4D1_ACT_DOD_WALK_AIM_RIFLE,
	L4D1_ACT_DOD_RUN_AIM_RIFLE,
	L4D1_ACT_DOD_PRONE_AIM_RIFLE,
	L4D1_ACT_DOD_STAND_IDLE_RIFLE,
	L4D1_ACT_DOD_CROUCH_IDLE_RIFLE,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_RIFLE,
	L4D1_ACT_DOD_WALK_IDLE_RIFLE,
	L4D1_ACT_DOD_RUN_IDLE_RIFLE,
	L4D1_ACT_DOD_SPRINT_IDLE_RIFLE,
	L4D1_ACT_DOD_PRONEWALK_IDLE_RIFLE,
	L4D1_ACT_DOD_STAND_AIM_BOLT,
	L4D1_ACT_DOD_CROUCH_AIM_BOLT,
	L4D1_ACT_DOD_CROUCHWALK_AIM_BOLT,
	L4D1_ACT_DOD_WALK_AIM_BOLT,
	L4D1_ACT_DOD_RUN_AIM_BOLT,
	L4D1_ACT_DOD_PRONE_AIM_BOLT,
	L4D1_ACT_DOD_STAND_IDLE_BOLT,
	L4D1_ACT_DOD_CROUCH_IDLE_BOLT,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_BOLT,
	L4D1_ACT_DOD_WALK_IDLE_BOLT,
	L4D1_ACT_DOD_RUN_IDLE_BOLT,
	L4D1_ACT_DOD_SPRINT_IDLE_BOLT,
	L4D1_ACT_DOD_PRONEWALK_IDLE_BOLT,
	L4D1_ACT_DOD_STAND_AIM_TOMMY,
	L4D1_ACT_DOD_CROUCH_AIM_TOMMY,
	L4D1_ACT_DOD_CROUCHWALK_AIM_TOMMY,
	L4D1_ACT_DOD_WALK_AIM_TOMMY,
	L4D1_ACT_DOD_RUN_AIM_TOMMY,
	L4D1_ACT_DOD_PRONE_AIM_TOMMY,
	L4D1_ACT_DOD_STAND_IDLE_TOMMY,
	L4D1_ACT_DOD_CROUCH_IDLE_TOMMY,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_TOMMY,
	L4D1_ACT_DOD_WALK_IDLE_TOMMY,
	L4D1_ACT_DOD_RUN_IDLE_TOMMY,
	L4D1_ACT_DOD_SPRINT_IDLE_TOMMY,
	L4D1_ACT_DOD_PRONEWALK_IDLE_TOMMY,
	L4D1_ACT_DOD_STAND_AIM_MP40,
	L4D1_ACT_DOD_CROUCH_AIM_MP40,
	L4D1_ACT_DOD_CROUCHWALK_AIM_MP40,
	L4D1_ACT_DOD_WALK_AIM_MP40,
	L4D1_ACT_DOD_RUN_AIM_MP40,
	L4D1_ACT_DOD_PRONE_AIM_MP40,
	L4D1_ACT_DOD_STAND_IDLE_MP40,
	L4D1_ACT_DOD_CROUCH_IDLE_MP40,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_MP40,
	L4D1_ACT_DOD_WALK_IDLE_MP40,
	L4D1_ACT_DOD_RUN_IDLE_MP40,
	L4D1_ACT_DOD_SPRINT_IDLE_MP40,
	L4D1_ACT_DOD_PRONEWALK_IDLE_MP40,
	L4D1_ACT_DOD_STAND_AIM_MP44,
	L4D1_ACT_DOD_CROUCH_AIM_MP44,
	L4D1_ACT_DOD_CROUCHWALK_AIM_MP44,
	L4D1_ACT_DOD_WALK_AIM_MP44,
	L4D1_ACT_DOD_RUN_AIM_MP44,
	L4D1_ACT_DOD_PRONE_AIM_MP44,
	L4D1_ACT_DOD_STAND_IDLE_MP44,
	L4D1_ACT_DOD_CROUCH_IDLE_MP44,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_MP44,
	L4D1_ACT_DOD_WALK_IDLE_MP44,
	L4D1_ACT_DOD_RUN_IDLE_MP44,
	L4D1_ACT_DOD_SPRINT_IDLE_MP44,
	L4D1_ACT_DOD_PRONEWALK_IDLE_MP44,
	L4D1_ACT_DOD_STAND_AIM_GREASE,
	L4D1_ACT_DOD_CROUCH_AIM_GREASE,
	L4D1_ACT_DOD_CROUCHWALK_AIM_GREASE,
	L4D1_ACT_DOD_WALK_AIM_GREASE,
	L4D1_ACT_DOD_RUN_AIM_GREASE,
	L4D1_ACT_DOD_PRONE_AIM_GREASE,
	L4D1_ACT_DOD_STAND_IDLE_GREASE,
	L4D1_ACT_DOD_CROUCH_IDLE_GREASE,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_GREASE,
	L4D1_ACT_DOD_WALK_IDLE_GREASE,
	L4D1_ACT_DOD_RUN_IDLE_GREASE,
	L4D1_ACT_DOD_SPRINT_IDLE_GREASE,
	L4D1_ACT_DOD_PRONEWALK_IDLE_GREASE,
	L4D1_ACT_DOD_STAND_AIM_MG,
	L4D1_ACT_DOD_CROUCH_AIM_MG,
	L4D1_ACT_DOD_CROUCHWALK_AIM_MG,
	L4D1_ACT_DOD_WALK_AIM_MG,
	L4D1_ACT_DOD_RUN_AIM_MG,
	L4D1_ACT_DOD_PRONE_AIM_MG,
	L4D1_ACT_DOD_STAND_IDLE_MG,
	L4D1_ACT_DOD_CROUCH_IDLE_MG,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_MG,
	L4D1_ACT_DOD_WALK_IDLE_MG,
	L4D1_ACT_DOD_RUN_IDLE_MG,
	L4D1_ACT_DOD_SPRINT_IDLE_MG,
	L4D1_ACT_DOD_PRONEWALK_IDLE_MG,
	L4D1_ACT_DOD_STAND_AIM_30CAL,
	L4D1_ACT_DOD_CROUCH_AIM_30CAL,
	L4D1_ACT_DOD_CROUCHWALK_AIM_30CAL,
	L4D1_ACT_DOD_WALK_AIM_30CAL,
	L4D1_ACT_DOD_RUN_AIM_30CAL,
	L4D1_ACT_DOD_PRONE_AIM_30CAL,
	L4D1_ACT_DOD_STAND_IDLE_30CAL,
	L4D1_ACT_DOD_CROUCH_IDLE_30CAL,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_30CAL,
	L4D1_ACT_DOD_WALK_IDLE_30CAL,
	L4D1_ACT_DOD_RUN_IDLE_30CAL,
	L4D1_ACT_DOD_SPRINT_IDLE_30CAL,
	L4D1_ACT_DOD_PRONEWALK_IDLE_30CAL,
	L4D1_ACT_DOD_STAND_AIM_GREN_FRAG,
	L4D1_ACT_DOD_CROUCH_AIM_GREN_FRAG,
	L4D1_ACT_DOD_CROUCHWALK_AIM_GREN_FRAG,
	L4D1_ACT_DOD_WALK_AIM_GREN_FRAG,
	L4D1_ACT_DOD_RUN_AIM_GREN_FRAG,
	L4D1_ACT_DOD_PRONE_AIM_GREN_FRAG,
	L4D1_ACT_DOD_SPRINT_AIM_GREN_FRAG,
	L4D1_ACT_DOD_PRONEWALK_AIM_GREN_FRAG,
	L4D1_ACT_DOD_STAND_AIM_GREN_STICK,
	L4D1_ACT_DOD_CROUCH_AIM_GREN_STICK,
	L4D1_ACT_DOD_CROUCHWALK_AIM_GREN_STICK,
	L4D1_ACT_DOD_WALK_AIM_GREN_STICK,
	L4D1_ACT_DOD_RUN_AIM_GREN_STICK,
	L4D1_ACT_DOD_PRONE_AIM_GREN_STICK,
	L4D1_ACT_DOD_SPRINT_AIM_GREN_STICK,
	L4D1_ACT_DOD_PRONEWALK_AIM_GREN_STICK,
	L4D1_ACT_DOD_STAND_AIM_KNIFE,
	L4D1_ACT_DOD_CROUCH_AIM_KNIFE,
	L4D1_ACT_DOD_CROUCHWALK_AIM_KNIFE,
	L4D1_ACT_DOD_WALK_AIM_KNIFE,
	L4D1_ACT_DOD_RUN_AIM_KNIFE,
	L4D1_ACT_DOD_PRONE_AIM_KNIFE,
	L4D1_ACT_DOD_SPRINT_AIM_KNIFE,
	L4D1_ACT_DOD_PRONEWALK_AIM_KNIFE,
	L4D1_ACT_DOD_STAND_AIM_SPADE,
	L4D1_ACT_DOD_CROUCH_AIM_SPADE,
	L4D1_ACT_DOD_CROUCHWALK_AIM_SPADE,
	L4D1_ACT_DOD_WALK_AIM_SPADE,
	L4D1_ACT_DOD_RUN_AIM_SPADE,
	L4D1_ACT_DOD_PRONE_AIM_SPADE,
	L4D1_ACT_DOD_SPRINT_AIM_SPADE,
	L4D1_ACT_DOD_PRONEWALK_AIM_SPADE,
	L4D1_ACT_DOD_STAND_AIM_BAZOOKA,
	L4D1_ACT_DOD_CROUCH_AIM_BAZOOKA,
	L4D1_ACT_DOD_CROUCHWALK_AIM_BAZOOKA,
	L4D1_ACT_DOD_WALK_AIM_BAZOOKA,
	L4D1_ACT_DOD_RUN_AIM_BAZOOKA,
	L4D1_ACT_DOD_PRONE_AIM_BAZOOKA,
	L4D1_ACT_DOD_STAND_IDLE_BAZOOKA,
	L4D1_ACT_DOD_CROUCH_IDLE_BAZOOKA,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_BAZOOKA,
	L4D1_ACT_DOD_WALK_IDLE_BAZOOKA,
	L4D1_ACT_DOD_RUN_IDLE_BAZOOKA,
	L4D1_ACT_DOD_SPRINT_IDLE_BAZOOKA,
	L4D1_ACT_DOD_PRONEWALK_IDLE_BAZOOKA,
	L4D1_ACT_DOD_STAND_AIM_PSCHRECK,
	L4D1_ACT_DOD_CROUCH_AIM_PSCHRECK,
	L4D1_ACT_DOD_CROUCHWALK_AIM_PSCHRECK,
	L4D1_ACT_DOD_WALK_AIM_PSCHRECK,
	L4D1_ACT_DOD_RUN_AIM_PSCHRECK,
	L4D1_ACT_DOD_PRONE_AIM_PSCHRECK,
	L4D1_ACT_DOD_STAND_IDLE_PSCHRECK,
	L4D1_ACT_DOD_CROUCH_IDLE_PSCHRECK,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_PSCHRECK,
	L4D1_ACT_DOD_WALK_IDLE_PSCHRECK,
	L4D1_ACT_DOD_RUN_IDLE_PSCHRECK,
	L4D1_ACT_DOD_SPRINT_IDLE_PSCHRECK,
	L4D1_ACT_DOD_PRONEWALK_IDLE_PSCHRECK,
	L4D1_ACT_DOD_STAND_AIM_BAR,
	L4D1_ACT_DOD_CROUCH_AIM_BAR,
	L4D1_ACT_DOD_CROUCHWALK_AIM_BAR,
	L4D1_ACT_DOD_WALK_AIM_BAR,
	L4D1_ACT_DOD_RUN_AIM_BAR,
	L4D1_ACT_DOD_PRONE_AIM_BAR,
	L4D1_ACT_DOD_STAND_IDLE_BAR,
	L4D1_ACT_DOD_CROUCH_IDLE_BAR,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_BAR,
	L4D1_ACT_DOD_WALK_IDLE_BAR,
	L4D1_ACT_DOD_RUN_IDLE_BAR,
	L4D1_ACT_DOD_SPRINT_IDLE_BAR,
	L4D1_ACT_DOD_PRONEWALK_IDLE_BAR,
	L4D1_ACT_DOD_STAND_ZOOM_RIFLE,
	L4D1_ACT_DOD_CROUCH_ZOOM_RIFLE,
	L4D1_ACT_DOD_CROUCHWALK_ZOOM_RIFLE,
	L4D1_ACT_DOD_WALK_ZOOM_RIFLE,
	L4D1_ACT_DOD_RUN_ZOOM_RIFLE,
	L4D1_ACT_DOD_PRONE_ZOOM_RIFLE,
	L4D1_ACT_DOD_STAND_ZOOM_BOLT,
	L4D1_ACT_DOD_CROUCH_ZOOM_BOLT,
	L4D1_ACT_DOD_CROUCHWALK_ZOOM_BOLT,
	L4D1_ACT_DOD_WALK_ZOOM_BOLT,
	L4D1_ACT_DOD_RUN_ZOOM_BOLT,
	L4D1_ACT_DOD_PRONE_ZOOM_BOLT,
	L4D1_ACT_DOD_STAND_ZOOM_BAZOOKA,
	L4D1_ACT_DOD_CROUCH_ZOOM_BAZOOKA,
	L4D1_ACT_DOD_CROUCHWALK_ZOOM_BAZOOKA,
	L4D1_ACT_DOD_WALK_ZOOM_BAZOOKA,
	L4D1_ACT_DOD_RUN_ZOOM_BAZOOKA,
	L4D1_ACT_DOD_PRONE_ZOOM_BAZOOKA,
	L4D1_ACT_DOD_STAND_ZOOM_PSCHRECK,
	L4D1_ACT_DOD_CROUCH_ZOOM_PSCHRECK,
	L4D1_ACT_DOD_CROUCHWALK_ZOOM_PSCHRECK,
	L4D1_ACT_DOD_WALK_ZOOM_PSCHRECK,
	L4D1_ACT_DOD_RUN_ZOOM_PSCHRECK,
	L4D1_ACT_DOD_PRONE_ZOOM_PSCHRECK,
	L4D1_ACT_DOD_DEPLOY_RIFLE,
	L4D1_ACT_DOD_DEPLOY_TOMMY,
	L4D1_ACT_DOD_DEPLOY_MG,
	L4D1_ACT_DOD_DEPLOY_30CAL,
	L4D1_ACT_DOD_PRONE_DEPLOY_RIFLE,
	L4D1_ACT_DOD_PRONE_DEPLOY_TOMMY,
	L4D1_ACT_DOD_PRONE_DEPLOY_MG,
	L4D1_ACT_DOD_PRONE_DEPLOY_30CAL,
	L4D1_ACT_DOD_PRIMARYATTACK_RIFLE,
	L4D1_ACT_DOD_SECONDARYATTACK_RIFLE,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_RIFLE,
	L4D1_ACT_DOD_SECONDARYATTACK_PRONE_RIFLE,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_DEPLOYED_RIFLE,
	L4D1_ACT_DOD_PRIMARYATTACK_DEPLOYED_RIFLE,
	L4D1_ACT_DOD_PRIMARYATTACK_BOLT,
	L4D1_ACT_DOD_SECONDARYATTACK_BOLT,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_BOLT,
	L4D1_ACT_DOD_SECONDARYATTACK_PRONE_BOLT,
	L4D1_ACT_DOD_PRIMARYATTACK_TOMMY,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_TOMMY,
	L4D1_ACT_DOD_SECONDARYATTACK_TOMMY,
	L4D1_ACT_DOD_SECONDARYATTACK_PRONE_TOMMY,
	L4D1_ACT_DOD_PRIMARYATTACK_MP40,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_MP40,
	L4D1_ACT_DOD_SECONDARYATTACK_MP40,
	L4D1_ACT_DOD_SECONDARYATTACK_PRONE_MP40,
	L4D1_ACT_DOD_PRIMARYATTACK_MP44,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_MP44,
	L4D1_ACT_DOD_PRIMARYATTACK_GREASE,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_GREASE,
	L4D1_ACT_DOD_PRIMARYATTACK_PISTOL,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_PISTOL,
	L4D1_ACT_DOD_PRIMARYATTACK_C96,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_C96,
	L4D1_ACT_DOD_PRIMARYATTACK_MG,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_MG,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_DEPLOYED_MG,
	L4D1_ACT_DOD_PRIMARYATTACK_DEPLOYED_MG,
	L4D1_ACT_DOD_PRIMARYATTACK_30CAL,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_30CAL,
	L4D1_ACT_DOD_PRIMARYATTACK_DEPLOYED_30CAL,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_DEPLOYED_30CAL,
	L4D1_ACT_DOD_PRIMARYATTACK_GREN_FRAG,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_GREN_FRAG,
	L4D1_ACT_DOD_PRIMARYATTACK_GREN_STICK,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_GREN_STICK,
	L4D1_ACT_DOD_PRIMARYATTACK_KNIFE,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_KNIFE,
	L4D1_ACT_DOD_PRIMARYATTACK_SPADE,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_SPADE,
	L4D1_ACT_DOD_PRIMARYATTACK_BAZOOKA,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_BAZOOKA,
	L4D1_ACT_DOD_PRIMARYATTACK_PSCHRECK,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_PSCHRECK,
	L4D1_ACT_DOD_PRIMARYATTACK_BAR,
	L4D1_ACT_DOD_PRIMARYATTACK_PRONE_BAR,
	L4D1_ACT_DOD_RELOAD_GARAND,
	L4D1_ACT_DOD_RELOAD_K43,
	L4D1_ACT_DOD_RELOAD_BAR,
	L4D1_ACT_DOD_RELOAD_MP40,
	L4D1_ACT_DOD_RELOAD_MP44,
	L4D1_ACT_DOD_RELOAD_BOLT,
	L4D1_ACT_DOD_RELOAD_M1CARBINE,
	L4D1_ACT_DOD_RELOAD_TOMMY,
	L4D1_ACT_DOD_RELOAD_GREASEGUN,
	L4D1_ACT_DOD_RELOAD_PISTOL,
	L4D1_ACT_DOD_RELOAD_FG42,
	L4D1_ACT_DOD_RELOAD_RIFLE,
	L4D1_ACT_DOD_RELOAD_RIFLEGRENADE,
	L4D1_ACT_DOD_RELOAD_C96,
	L4D1_ACT_DOD_RELOAD_CROUCH_BAR,
	L4D1_ACT_DOD_RELOAD_CROUCH_RIFLE,
	L4D1_ACT_DOD_RELOAD_CROUCH_RIFLEGRENADE,
	L4D1_ACT_DOD_RELOAD_CROUCH_BOLT,
	L4D1_ACT_DOD_RELOAD_CROUCH_MP44,
	L4D1_ACT_DOD_RELOAD_CROUCH_MP40,
	L4D1_ACT_DOD_RELOAD_CROUCH_TOMMY,
	L4D1_ACT_DOD_RELOAD_CROUCH_BAZOOKA,
	L4D1_ACT_DOD_RELOAD_CROUCH_PSCHRECK,
	L4D1_ACT_DOD_RELOAD_CROUCH_PISTOL,
	L4D1_ACT_DOD_RELOAD_CROUCH_M1CARBINE,
	L4D1_ACT_DOD_RELOAD_CROUCH_C96,
	L4D1_ACT_DOD_RELOAD_BAZOOKA,
	L4D1_ACT_DOD_ZOOMLOAD_BAZOOKA,
	L4D1_ACT_DOD_RELOAD_PSCHRECK,
	L4D1_ACT_DOD_ZOOMLOAD_PSCHRECK,
	L4D1_ACT_DOD_RELOAD_DEPLOYED_FG42,
	L4D1_ACT_DOD_RELOAD_DEPLOYED_30CAL,
	L4D1_ACT_DOD_RELOAD_DEPLOYED_MG,
	L4D1_ACT_DOD_RELOAD_DEPLOYED_MG34,
	L4D1_ACT_DOD_RELOAD_DEPLOYED_BAR,
	L4D1_ACT_DOD_RELOAD_PRONE_PISTOL,
	L4D1_ACT_DOD_RELOAD_PRONE_GARAND,
	L4D1_ACT_DOD_RELOAD_PRONE_M1CARBINE,
	L4D1_ACT_DOD_RELOAD_PRONE_BOLT,
	L4D1_ACT_DOD_RELOAD_PRONE_K43,
	L4D1_ACT_DOD_RELOAD_PRONE_MP40,
	L4D1_ACT_DOD_RELOAD_PRONE_MP44,
	L4D1_ACT_DOD_RELOAD_PRONE_BAR,
	L4D1_ACT_DOD_RELOAD_PRONE_GREASEGUN,
	L4D1_ACT_DOD_RELOAD_PRONE_TOMMY,
	L4D1_ACT_DOD_RELOAD_PRONE_FG42,
	L4D1_ACT_DOD_RELOAD_PRONE_RIFLE,
	L4D1_ACT_DOD_RELOAD_PRONE_RIFLEGRENADE,
	L4D1_ACT_DOD_RELOAD_PRONE_C96,
	L4D1_ACT_DOD_RELOAD_PRONE_BAZOOKA,
	L4D1_ACT_DOD_ZOOMLOAD_PRONE_BAZOOKA,
	L4D1_ACT_DOD_RELOAD_PRONE_PSCHRECK,
	L4D1_ACT_DOD_ZOOMLOAD_PRONE_PSCHRECK,
	L4D1_ACT_DOD_RELOAD_PRONE_DEPLOYED_BAR,
	L4D1_ACT_DOD_RELOAD_PRONE_DEPLOYED_FG42,
	L4D1_ACT_DOD_RELOAD_PRONE_DEPLOYED_30CAL,
	L4D1_ACT_DOD_RELOAD_PRONE_DEPLOYED_MG,
	L4D1_ACT_DOD_RELOAD_PRONE_DEPLOYED_MG34,
	L4D1_ACT_DOD_PRONE_ZOOM_FORWARD_RIFLE,
	L4D1_ACT_DOD_PRONE_ZOOM_FORWARD_BOLT,
	L4D1_ACT_DOD_PRONE_ZOOM_FORWARD_BAZOOKA,
	L4D1_ACT_DOD_PRONE_ZOOM_FORWARD_PSCHRECK,
	L4D1_ACT_DOD_PRIMARYATTACK_CROUCH,
	L4D1_ACT_DOD_PRIMARYATTACK_CROUCH_SPADE,
	L4D1_ACT_DOD_PRIMARYATTACK_CROUCH_KNIFE,
	L4D1_ACT_DOD_PRIMARYATTACK_CROUCH_GREN_FRAG,
	L4D1_ACT_DOD_PRIMARYATTACK_CROUCH_GREN_STICK,
	L4D1_ACT_DOD_SECONDARYATTACK_CROUCH,
	L4D1_ACT_DOD_SECONDARYATTACK_CROUCH_TOMMY,
	L4D1_ACT_DOD_SECONDARYATTACK_CROUCH_MP40,
	L4D1_ACT_DOD_HS_IDLE,
	L4D1_ACT_DOD_HS_CROUCH,
	L4D1_ACT_DOD_HS_IDLE_30CAL,
	L4D1_ACT_DOD_HS_IDLE_BAZOOKA,
	L4D1_ACT_DOD_HS_IDLE_PSCHRECK,
	L4D1_ACT_DOD_HS_IDLE_KNIFE,
	L4D1_ACT_DOD_HS_IDLE_MG42,
	L4D1_ACT_DOD_HS_IDLE_PISTOL,
	L4D1_ACT_DOD_HS_IDLE_STICKGRENADE,
	L4D1_ACT_DOD_HS_IDLE_TOMMY,
	L4D1_ACT_DOD_HS_IDLE_MP44,
	L4D1_ACT_DOD_HS_IDLE_K98,
	L4D1_ACT_DOD_HS_CROUCH_30CAL,
	L4D1_ACT_DOD_HS_CROUCH_BAZOOKA,
	L4D1_ACT_DOD_HS_CROUCH_PSCHRECK,
	L4D1_ACT_DOD_HS_CROUCH_KNIFE,
	L4D1_ACT_DOD_HS_CROUCH_MG42,
	L4D1_ACT_DOD_HS_CROUCH_PISTOL,
	L4D1_ACT_DOD_HS_CROUCH_STICKGRENADE,
	L4D1_ACT_DOD_HS_CROUCH_TOMMY,
	L4D1_ACT_DOD_HS_CROUCH_MP44,
	L4D1_ACT_DOD_HS_CROUCH_K98,
	L4D1_ACT_DOD_STAND_IDLE_TNT,
	L4D1_ACT_DOD_CROUCH_IDLE_TNT,
	L4D1_ACT_DOD_CROUCHWALK_IDLE_TNT,
	L4D1_ACT_DOD_WALK_IDLE_TNT,
	L4D1_ACT_DOD_RUN_IDLE_TNT,
	L4D1_ACT_DOD_SPRINT_IDLE_TNT,
	L4D1_ACT_DOD_PRONEWALK_IDLE_TNT,
	L4D1_ACT_DOD_PLANT_TNT,
	L4D1_ACT_DOD_DEFUSE_TNT,
	L4D1_ACT_HL2MP_IDLE,
	L4D1_ACT_HL2MP_RUN,
	L4D1_ACT_HL2MP_IDLE_CROUCH,
	L4D1_ACT_HL2MP_WALK_CROUCH,
	L4D1_ACT_HL2MP_GESTURE_RANGE_ATTACK,
	L4D1_ACT_HL2MP_GESTURE_RELOAD,
	L4D1_ACT_HL2MP_JUMP,
	L4D1_ACT_HL2MP_IDLE_PISTOL,
	L4D1_ACT_HL2MP_RUN_PISTOL,
	L4D1_ACT_HL2MP_IDLE_CROUCH_PISTOL,
	L4D1_ACT_HL2MP_WALK_CROUCH_PISTOL,
	L4D1_ACT_HL2MP_GESTURE_RANGE_ATTACK_PISTOL,
	L4D1_ACT_HL2MP_GESTURE_RELOAD_PISTOL,
	L4D1_ACT_HL2MP_JUMP_PISTOL,
	L4D1_ACT_HL2MP_IDLE_SMG1,
	L4D1_ACT_HL2MP_RUN_SMG1,
	L4D1_ACT_HL2MP_IDLE_CROUCH_SMG1,
	L4D1_ACT_HL2MP_WALK_CROUCH_SMG1,
	L4D1_ACT_HL2MP_GESTURE_RANGE_ATTACK_SMG1,
	L4D1_ACT_HL2MP_GESTURE_RELOAD_SMG1,
	L4D1_ACT_HL2MP_JUMP_SMG1,
	L4D1_ACT_HL2MP_IDLE_AR2,
	L4D1_ACT_HL2MP_RUN_AR2,
	L4D1_ACT_HL2MP_IDLE_CROUCH_AR2,
	L4D1_ACT_HL2MP_WALK_CROUCH_AR2,
	L4D1_ACT_HL2MP_GESTURE_RANGE_ATTACK_AR2,
	L4D1_ACT_HL2MP_GESTURE_RELOAD_AR2,
	L4D1_ACT_HL2MP_JUMP_AR2,
	L4D1_ACT_HL2MP_IDLE_SHOTGUN,
	L4D1_ACT_HL2MP_RUN_SHOTGUN,
	L4D1_ACT_HL2MP_IDLE_CROUCH_SHOTGUN,
	L4D1_ACT_HL2MP_WALK_CROUCH_SHOTGUN,
	L4D1_ACT_HL2MP_GESTURE_RANGE_ATTACK_SHOTGUN,
	L4D1_ACT_HL2MP_GESTURE_RELOAD_SHOTGUN,
	L4D1_ACT_HL2MP_JUMP_SHOTGUN,
	L4D1_ACT_HL2MP_IDLE_RPG,
	L4D1_ACT_HL2MP_RUN_RPG,
	L4D1_ACT_HL2MP_IDLE_CROUCH_RPG,
	L4D1_ACT_HL2MP_WALK_CROUCH_RPG,
	L4D1_ACT_HL2MP_GESTURE_RANGE_ATTACK_RPG,
	L4D1_ACT_HL2MP_GESTURE_RELOAD_RPG,
	L4D1_ACT_HL2MP_JUMP_RPG,
	L4D1_ACT_HL2MP_IDLE_GRENADE,
	L4D1_ACT_HL2MP_RUN_GRENADE,
	L4D1_ACT_HL2MP_IDLE_CROUCH_GRENADE,
	L4D1_ACT_HL2MP_WALK_CROUCH_GRENADE,
	L4D1_ACT_HL2MP_GESTURE_RANGE_ATTACK_GRENADE,
	L4D1_ACT_HL2MP_GESTURE_RELOAD_GRENADE,
	L4D1_ACT_HL2MP_JUMP_GRENADE,
	L4D1_ACT_HL2MP_IDLE_PHYSGUN,
	L4D1_ACT_HL2MP_RUN_PHYSGUN,
	L4D1_ACT_HL2MP_IDLE_CROUCH_PHYSGUN,
	L4D1_ACT_HL2MP_WALK_CROUCH_PHYSGUN,
	L4D1_ACT_HL2MP_GESTURE_RANGE_ATTACK_PHYSGUN,
	L4D1_ACT_HL2MP_GESTURE_RELOAD_PHYSGUN,
	L4D1_ACT_HL2MP_JUMP_PHYSGUN,
	L4D1_ACT_HL2MP_IDLE_CROSSBOW,
	L4D1_ACT_HL2MP_RUN_CROSSBOW,
	L4D1_ACT_HL2MP_IDLE_CROUCH_CROSSBOW,
	L4D1_ACT_HL2MP_WALK_CROUCH_CROSSBOW,
	L4D1_ACT_HL2MP_GESTURE_RANGE_ATTACK_CROSSBOW,
	L4D1_ACT_HL2MP_GESTURE_RELOAD_CROSSBOW,
	L4D1_ACT_HL2MP_JUMP_CROSSBOW,
	L4D1_ACT_HL2MP_IDLE_MELEE,
	L4D1_ACT_HL2MP_RUN_MELEE,
	L4D1_ACT_HL2MP_IDLE_CROUCH_MELEE,
	L4D1_ACT_HL2MP_WALK_CROUCH_MELEE,
	L4D1_ACT_HL2MP_GESTURE_RANGE_ATTACK_MELEE,
	L4D1_ACT_HL2MP_GESTURE_RELOAD_MELEE,
	L4D1_ACT_HL2MP_JUMP_MELEE,
	L4D1_ACT_HL2MP_IDLE_SLAM,
	L4D1_ACT_HL2MP_RUN_SLAM,
	L4D1_ACT_HL2MP_IDLE_CROUCH_SLAM,
	L4D1_ACT_HL2MP_WALK_CROUCH_SLAM,
	L4D1_ACT_HL2MP_GESTURE_RANGE_ATTACK_SLAM,
	L4D1_ACT_HL2MP_GESTURE_RELOAD_SLAM,
	L4D1_ACT_HL2MP_JUMP_SLAM,
	L4D1_ACT_VM_SHRIEK,
	L4D1_ACT_VM_VOMIT,
	L4D1_ACT_VM_COUGH,
	L4D1_ACT_VM_LUNGE,
	L4D1_ACT_IDLE_INJURED,
	L4D1_ACT_IDLE_INCAP,
	L4D1_ACT_WALK_INJURED,
	L4D1_ACT_RUN_INJURED,
	L4D1_ACT_RUN_PULLED,
	L4D1_ACT_IDLE_CALM,
	L4D1_ACT_WALK_CALM,
	L4D1_ACT_RUN_CALM,
	L4D1_ACT_JUMP_SHOTGUN,
	L4D1_ACT_JUMP_RIFLE,
	L4D1_ACT_JUMP_SMG,
	L4D1_ACT_JUMP_PISTOL,
	L4D1_ACT_JUMP_DUAL_PISTOL,
	L4D1_ACT_JUMP_ITEM,
	L4D1_ACT_TERROR_HIT_BY_TANKPUNCH,
	L4D1_ACT_TERROR_IDLE_FALL_FROM_TANKPUNCH,
	L4D1_ACT_TERROR_TANKPUNCH_LAND,
	L4D1_ACT_TERROR_HEAL_SELF,
	L4D1_ACT_TERROR_HEAL_FRIEND,
	L4D1_ACT_TERROR_HEAL_INCAPACITATED,
	L4D1_ACT_TERROR_CROUCH_HEAL_SELF,
	L4D1_ACT_TERROR_CROUCH_HEAL_FRIEND,
	L4D1_ACT_TERROR_CROUCH_HEAL_INCAPACITATED,
	L4D1_ACT_TERROR_HEAL_INCAPACITATED_ABOVE,
	L4D1_ACT_TERROR_CROUCH_HEAL_INCAPACITATED_ABOVE,
	L4D1_ACT_TERROR_USE_PILLS,
	L4D1_ACT_TERROR_IDLE_NEUTRAL,
	L4D1_ACT_TERROR_IDLE_ALERT,
	L4D1_ACT_TERROR_IDLE_INTENSE,
	L4D1_ACT_TERROR_IDLE_ALERT_INJURED_AHEAD,
	L4D1_ACT_TERROR_IDLE_ALERT_AHEAD,
	L4D1_ACT_TERROR_IDLE_ALERT_INJURED_BEHIND,
	L4D1_ACT_TERROR_IDLE_ALERT_BEHIND,
	L4D1_ACT_TERROR_IDLE_ALERT_INJURED_LEFT,
	L4D1_ACT_TERROR_IDLE_ALERT_LEFT,
	L4D1_ACT_TERROR_IDLE_ALERT_INJURED_RIGHT,
	L4D1_ACT_TERROR_IDLE_ALERT_RIGHT,
	L4D1_ACT_TERROR_IDLE_ACQUIRE,
	L4D1_ACT_TERROR_LEAN_FORWARD_IDLE,
	L4D1_ACT_TERROR_LEAN_BACKWARD_IDLE,
	L4D1_ACT_TERROR_LEAN_LEFTWARD_IDLE,
	L4D1_ACT_TERROR_LEAN_RIGHTWARD_IDLE,
	L4D1_ACT_TERROR_FIDGET,
	L4D1_ACT_TERROR_NEUTRAL_TO_ALERT,
	L4D1_ACT_TERROR_ALERT_TO_NEUTRAL,
	L4D1_ACT_TERROR_FACE_LEFT_NEUTRAL,
	L4D1_ACT_TERROR_FACE_LEFT_ALERT,
	L4D1_ACT_TERROR_FACE_LEFT_INTENSE,
	L4D1_ACT_TERROR_FACE_RIGHT_NEUTRAL,
	L4D1_ACT_TERROR_FACE_RIGHT_ALERT,
	L4D1_ACT_TERROR_FACE_RIGHT_INTENSE,
	L4D1_ACT_TERROR_ABOUT_FACE_NEUTRAL,
	L4D1_ACT_TERROR_ABOUT_FACE_ALERT,
	L4D1_ACT_TERROR_ABOUT_FACE_INTENSE,
	L4D1_ACT_TERROR_WALK_NEUTRAL,
	L4D1_ACT_TERROR_WALK_ALERT,
	L4D1_ACT_TERROR_WALK_INTENSE,
	L4D1_ACT_TERROR_RUN_NEUTRAL,
	L4D1_ACT_TERROR_RUN_ALERT,
	L4D1_ACT_TERROR_RUN_INTENSE,
	L4D1_ACT_TERROR_RUN_ON_FIRE_INTENSE,
	L4D1_ACT_TERROR_RUN_INTENSE_TO_STAND_ALERT,
	L4D1_ACT_TERROR_CROUCH_IDLE_NEUTRAL,
	L4D1_ACT_TERROR_CROUCH_IDLE_ALERT,
	L4D1_ACT_TERROR_CROUCH_IDLE_INTENSE,
	L4D1_ACT_TERROR_CROUCH_WALK_NEUTRAL,
	L4D1_ACT_TERROR_CROUCH_WALK_ALERT,
	L4D1_ACT_TERROR_CROUCH_WALK_INTENSE,
	L4D1_ACT_TERROR_CROUCH_RUN_NEUTRAL,
	L4D1_ACT_TERROR_CROUCH_RUN_ALERT,
	L4D1_ACT_TERROR_CROUCH_RUN_INTENSE,
	L4D1_ACT_TERROR_IDLE_ON_FIRE,
	L4D1_ACT_TERROR_WALK_ON_FIRE,
	L4D1_ACT_TERROR_RUN_ON_FIRE,
	L4D1_ACT_TERROR_ATTACK,
	L4D1_ACT_TERROR_ATTACK_CONTINUOUSLY,
	L4D1_ACT_TERROR_ATTACK_LOW,
	L4D1_ACT_TERROR_ATTACK_LOW_CONTINUOUSLY,
	L4D1_ACT_TERROR_ATTACK_DOOR,
	L4D1_ACT_TERROR_ATTACK_DOOR_CONTINUOUSLY,
	L4D1_ACT_TERROR_UNABLE_TO_REACH_TARGET,
	L4D1_ACT_TERROR_REACH_THROUGH_DOOR,
	L4D1_ACT_TERROR_SHOVED_FORWARD,
	L4D1_ACT_TERROR_SHOVED_BACKWARD,
	L4D1_ACT_TERROR_SHOVED_LEFTWARD,
	L4D1_ACT_TERROR_SHOVED_RIGHTWARD,
	L4D1_ACT_TERROR_SHOVED_BACKWARD_INTO_WALL,
	L4D1_ACT_TERROR_SHOVED_FORWARD_INTO_WALL,
	L4D1_ACT_TERROR_SHOVED_LEFTWARD_INTO_WALL,
	L4D1_ACT_TERROR_SHOVED_RIGHTWARD_INTO_WALL,
	L4D1_ACT_TERROR_SHOVED_BACKWARD_FROM_SIT,
	L4D1_ACT_TERROR_SHOVED_RIGHTWARD_FROM_SIT,
	L4D1_ACT_TERROR_SHOVED_LEFTWARD_FROM_SIT,
	L4D1_ACT_TERROR_TUGGED_FORWARD,
	L4D1_ACT_TERROR_TUGGED_BACKWARD,
	L4D1_ACT_TERROR_TUGGED_LEFTWARD,
	L4D1_ACT_TERROR_TUGGED_RIGHTWARD,
	L4D1_ACT_TERROR_SIT_IDLE,
	L4D1_ACT_TERROR_SIT_FROM_STAND,
	L4D1_ACT_TERROR_SIT_TO_STAND,
	L4D1_ACT_TERROR_SIT_TO_STAND_ALERT,
	L4D1_ACT_TERROR_SIT_TO_LIE,
	L4D1_ACT_TERROR_LIE_IDLE,
	L4D1_ACT_TERROR_LIE_FROM_STAND,
	L4D1_ACT_TERROR_LIE_TO_STAND,
	L4D1_ACT_TERROR_LIE_TO_STAND_ALERT,
	L4D1_ACT_TERROR_LIE_TO_SIT,
	L4D1_ACT_TERROR_JUMP,
	L4D1_ACT_TERROR_JUMP_UP_TO_LEDGE,
	L4D1_ACT_TERROR_JUMP_DOWN_FROM_LEDGE,
	L4D1_ACT_TERROR_JUMP_OVER_GAP,
	L4D1_ACT_TERROR_JUMP_LANDING,
	L4D1_ACT_TERROR_JUMP_LANDING_HARD,
	L4D1_ACT_TERROR_JUMP_LANDING_NEUTRAL,
	L4D1_ACT_TERROR_JUMP_LANDING_HARD_NEUTRAL,
	L4D1_ACT_TERROR_FALL,
	L4D1_ACT_TERROR_DIE_FROM_STAND,
	L4D1_ACT_TERROR_DIE_FROM_CROUCH,
	L4D1_ACT_TERROR_DIE_WHILE_RUNNING,
	L4D1_ACT_TERROR_DIE_BACKWARD_FROM_SHOTGUN,
	L4D1_ACT_TERROR_DIE_FORWARD_FROM_SHOTGUN,
	L4D1_ACT_TERROR_DIE_LEFTWARD_FROM_SHOTGUN,
	L4D1_ACT_TERROR_DIE_RIGHTWARD_FROM_SHOTGUN,
	L4D1_ACT_TERROR_WITCH_ANGRY,
	L4D1_ACT_TERROR_WITCH_ANGRY_HIGH,
	L4D1_ACT_TERROR_WITCH_IDLE,
	L4D1_ACT_TERROR_WITCH_IDLE_PRE_RETREAT,
	L4D1_ACT_TERROR_WITCH_RETREAT,
	L4D1_ACT_TERROR_WITCH_KILL_DISPLAY,
	L4D1_ACT_TERROR_WITCH_KILL_LOOP,
	L4D1_ACT_TERROR_TUG,
	L4D1_ACT_TERROR_FLINCH,
	L4D1_ACT_TERROR_FLINCH_LEDGE,
	L4D1_ACT_SECONDARYATTACK,
	L4D1_ACT_TERROR_INCAP_CRAWL,
	L4D1_ACT_TERROR_INCAP_TO_STAND,
	L4D1_ACT_TERROR_INCAP_TO_CROUCH,
	L4D1_ACT_TERROR_ITEM_PICKUP,
	L4D1_ACT_IDLE_INCAP_PISTOL,
	L4D1_ACT_IDLE_INCAP_ELITES,
	L4D1_ACT_FALL,
	L4D1_ACT_CALL_FOR_RESCUE,
	L4D1_ACT_PUSH,
	L4D1_ACT_IDLE_MINIGUN,
	L4D1_ACT_PRIMARYATTACK_MINIGUN,
	L4D1_ACT_TERROR_LEDGE_CLIMB,
	L4D1_ACT_TERROR_LEDGE_CLIMB_TO_CROUCH,
	L4D1_ACT_TERROR_FALL_GRAB_LEDGE,
	L4D1_ACT_TERROR_LEDGE_HANG_FIRM,
	L4D1_ACT_TERROR_LEDGE_HANG_WEAK,
	L4D1_ACT_TERROR_LEDGE_HANG_DANGLE,
	L4D1_ACT_TERROR_IDLE_LADDER,
	L4D1_ACT_TERROR_LADDER_DISMOUNT,
	L4D1_ACT_TERROR_CLIMB_24_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_36_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_48_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_60_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_70_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_72_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_84_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_96_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_108_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_115_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_120_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_130_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_132_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_144_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_150_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_156_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_166_FROM_STAND,
	L4D1_ACT_TERROR_CLIMB_168_FROM_STAND,
	L4D1_ACT_TERROR_HUNTER_LUNGE_INTO_SURVIVOR,
	L4D1_ACT_TERROR_HUNTER_LUNGE,
	L4D1_ACT_TERROR_HUNTER_LUNGE_WHILE_RUNNING,
	L4D1_ACT_TERROR_HUNTER_LANDING_HARD,
	L4D1_ACT_TERROR_HUNTER_LUNGE_OFF_WALL_SPIN_RIGHT,
	L4D1_ACT_TERROR_HUNTER_LUNGE_OFF_WALL_SPIN_LEFT,
	L4D1_ACT_TERROR_HUNTER_LUNGE_OFF_WALL_BACK,
	L4D1_ACT_TERROR_HUNTER_LUNGE_IDLE,
	L4D1_ACT_TERROR_HUNTER_LUNGE_ONTO_WALL,
	L4D1_ACT_TERROR_HUNTER_POUNCE,
	L4D1_ACT_TERROR_HUNTER_POUNCE_IDLE,
	L4D1_ACT_TERROR_SMOKER_PREPARE_TONGUE_LAUNCH,
	L4D1_ACT_TERROR_SMOKER_SENDING_OUT_TONGUE,
	L4D1_ACT_TERROR_SMOKER_SENDING_OUT_TONGUE_IDLE,
	L4D1_ACT_TERROR_SMOKER_REELING_IN_TONGUE,
	L4D1_ACT_TERROR_SMOKER_REELING_IN_TONGUE_IDLE,
	L4D1_ACT_TERROR_SMOKER_CRITICAL_ATTACK,
	L4D1_ACT_TERROR_SMOKER_CRITICAL_ATTACK_IDLE,
	L4D1_ACT_TERROR_SMOKER_END_TONGUE_ATTACK,
	L4D1_ACT_TERROR_GUARD,
	L4D1_ACT_HULK_THROW,
	L4D1_ACT_TANK_OVERHEAD_THROW,
	L4D1_ACT_HULK_ATTACK_LOW,
	L4D1_ACT_TERROR_HUNTER_POUNCE_MELEE,
	L4D1_ACT_TERROR_HUNTER_POUNCE_KNOCKOFF_L,
	L4D1_ACT_TERROR_HUNTER_POUNCE_KNOCKOFF_R,
	L4D1_ACT_TERROR_HUNTER_POUNCE_KNOCKOFF_BACKWARD,
	L4D1_ACT_TERROR_HUNTER_POUNCE_KNOCKOFF_FORWARD,
	L4D1_ACT_IDLE_POUNCED,
	L4D1_ACT_TERROR_POUNCED_TO_STAND,
	L4D1_ACT_TERROR_INCAP_FROM_POUNCE,
	L4D1_ACT_TERROR_INCAP_FROM_TONGUE,
	L4D1_ACT_TERROR_IDLE_FALL_FROM_TONGUE,
	L4D1_ACT_TERROR_HANGING_FROM_TONGUE,
	L4D1_ACT_TERROR_HANGING_FROM_TONGUE_MALE,
	L4D1_ACT_TERROR_HANGING_FROM_TONGUE_FEMALE,
	L4D1_ACT_TERROR_STANDING_CHOKE_FROM_TONGUE,
	L4D1_ACT_TERROR_DRAGGING_FROM_TONGUE,
	L4D1_ACT_TERROR_DRAGGING_FROM_TONGUE_MALE,
	L4D1_ACT_TERROR_DRAGGING_FROM_TONGUE_FEMALE,
	L4D1_ACT_TERROR_DRAGGING_FROM_TONGUE_DEPLOY,
	L4D1_ACT_TERROR_CHOKING_TONGUE_GROUND,
	L4D1_ACT_TERROR_CHOKING_TONGUE_GROUND_MALE,
	L4D1_ACT_TERROR_CHOKING_TONGUE_GROUND_FEMALE,
	L4D1_ACT_TERROR_INCAP_FROM_TONGUE_GERMANY,
	L4D1_ACT_TERROR_HANGING_FROM_TONGUE_GERMANY,
	L4D1_ACT_TERROR_IDLE_FALL_FROM_TONGUE_GERMANY,
	L4D1_ACT_TERROR_STANDING_CHOKE_FROM_TONGUE_GERMANY,
	L4D1_ACT_TERROR_ATTACK_MOVING,
	L4D1_ACT_TERROR_TANKROCK_TO_STAND,
	L4D1_ACT_TERROR_HULK_VICTORY,
	L4D1_ACT_TERROR_HULK_VICTORY_B,
	L4D1_ACT_TERROR_RAGE_AT_ENEMY,
	L4D1_ACT_TERROR_RAGE_AT_KNOCKDOWN,
	L4D1_ACT_TERROR_SMASH_LEFT,
	L4D1_ACT_TERROR_SMASH_RIGHT,
	L4D1_ACT_VM_LUNGE_LAYER,
	L4D1_ACT_VM_LUNGE_PUSH,
	L4D1_ACT_VM_LUNGE_PUSH_LAYER,
	L4D1_ACT_VM_LUNGE_OFFWALL,
	L4D1_ACT_VM_LUNGE_OFFWALL_LAYER,
	L4D1_ACT_VM_LUNGE_POUNCE,
	L4D1_ACT_VM_LUNGE_POUNCE_LAYER,
	L4D1_ACT_VM_POUNCE,
	L4D1_ACT_VM_POUNCE_LAYER,
	L4D1_ACT_VM_VOMIT_LAYER,
	L4D1_ACT_VM_TONGUE,
	L4D1_ACT_VM_TONGUE_LAYER,
	L4D1_ACT_VM_BITE,
	L4D1_ACT_VM_BITE_LAYER,
	L4D1_ACT_VM_COUGH_LAYER,
	L4D1_ACT_VM_FIDGET_LAYER,
	L4D1_ACT_DOOR_OPEN,
	L4D1_ACT_DOOR_OPENFAIL,
	L4D1_ACT_DOOR_ANIMTOLOCK,
	L4D1_ACT_DOOR_ANIMTOUNLOCK,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP,
	L4D1_ACT_VM_HELPINGHAND_LOOP_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_PISTOL,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_PISTOL_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_PISTOL,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_PISTOL_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_PISTOL,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_PISTOL_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_PISTOL,
	L4D1_ACT_VM_HELPINGHAND_LOOP_PISTOL_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_PISTOL,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_PISTOL_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_PISTOL,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_PISTOL_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_DUAL_PISTOL,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_DUAL_PISTOL,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_DUAL_PISTOL,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_DUAL_PISTOL,
	L4D1_ACT_VM_HELPINGHAND_LOOP_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_DUAL_PISTOL,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_DUAL_PISTOL,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_RIFLE,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_RIFLE_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_RIFLE,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_RIFLE_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_RIFLE,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_RIFLE_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_RIFLE,
	L4D1_ACT_VM_HELPINGHAND_LOOP_RIFLE_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_RIFLE,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_RIFLE_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_RIFLE,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_RIFLE_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_SMG,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_SMG_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_SMG,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_SMG_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_SMG,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_SMG_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_SMG,
	L4D1_ACT_VM_HELPINGHAND_LOOP_SMG_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_SMG,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_SMG_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_SMG,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_SMG_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_SHOTGUN,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_SHOTGUN_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_SHOTGUN,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_SHOTGUN_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_SHOTGUN,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_SHOTGUN_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_SHOTGUN,
	L4D1_ACT_VM_HELPINGHAND_LOOP_SHOTGUN_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_SHOTGUN,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_SHOTGUN_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_SHOTGUN,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_SHOTGUN_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_AUTOSHOTGUN,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_AUTOSHOTGUN,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_AUTOSHOTGUN,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_AUTOSHOTGUN,
	L4D1_ACT_VM_HELPINGHAND_LOOP_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_AUTOSHOTGUN,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_AUTOSHOTGUN,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_SNIPER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_SNIPER_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_SNIPER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_SNIPER_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_SNIPER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_SNIPER_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_SNIPER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_SNIPER_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_SNIPER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_SNIPER_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_SNIPER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_SNIPER_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_PIPEBOMB,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_PIPEBOMB_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_PIPEBOMB,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_PIPEBOMB_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_PIPEBOMB,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_PIPEBOMB_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_PIPEBOMB,
	L4D1_ACT_VM_HELPINGHAND_LOOP_PIPEBOMB_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_PIPEBOMB,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_PIPEBOMB_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_PIPEBOMB,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_PIPEBOMB_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_MOLOTOV,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_MOLOTOV_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_MOLOTOV,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_MOLOTOV_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_MOLOTOV,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_MOLOTOV_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_MOLOTOV,
	L4D1_ACT_VM_HELPINGHAND_LOOP_MOLOTOV_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_MOLOTOV,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_MOLOTOV_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_MOLOTOV,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_MOLOTOV_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_MEDKIT,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_MEDKIT_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_MEDKIT,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_MEDKIT_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_MEDKIT,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_MEDKIT_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_MEDKIT,
	L4D1_ACT_VM_HELPINGHAND_LOOP_MEDKIT_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_MEDKIT,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_MEDKIT_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_MEDKIT,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_MEDKIT_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_PAINPILLS,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_PAINPILLS_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_PAINPILLS,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_PAINPILLS_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_PAINPILLS,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_PAINPILLS_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_PAINPILLS,
	L4D1_ACT_VM_HELPINGHAND_LOOP_PAINPILLS_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_PAINPILLS,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_PAINPILLS_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_PAINPILLS,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_PAINPILLS_LAYER,
	L4D1_ACT_VM_CANCEL,
	L4D1_ACT_VM_CANCEL_LAYER,
	L4D1_ACT_CROUCHIDLE_RIFLE,
	L4D1_ACT_IDLE_INJURED_RIFLE,
	L4D1_ACT_WALK_INJURED_RIFLE,
	L4D1_ACT_RUN_INJURED_RIFLE,
	L4D1_ACT_DEPLOY_RIFLE,
	L4D1_ACT_PRIMARYATTACK_RIFLE,
	L4D1_ACT_RELOAD_RIFLE,
	L4D1_ACT_IDLE_CALM_RIFLE,
	L4D1_ACT_WALK_CALM_RIFLE,
	L4D1_ACT_RUN_CALM_RIFLE,
	L4D1_ACT_MELEE_SWEEP_RIFLE_IDLE,
	L4D1_ACT_MELEE_SHOVE_RIFLE_IDLE,
	L4D1_ACT_MELEE_STRAIGHT_RIFLE_IDLE,
	L4D1_ACT_MELEE_SWEEP_RIFLE_RUN,
	L4D1_ACT_MELEE_SHOVE_RIFLE_RUN,
	L4D1_ACT_MELEE_STRAIGHT_RIFLE_RUN,
	L4D1_ACT_TERROR_PULLED_RUN_RIFLE,
	L4D1_ACT_MELEE_STOMP_RIFLE_IDLE,
	L4D1_ACT_MELEE_STOMP_RIFLE_WALK,
	L4D1_ACT_IDLE_SHOTGUN,
	L4D1_ACT_WALK_SHOTGUN,
	L4D1_ACT_RUN_SHOTGUN,
	L4D1_ACT_IDLE_CALM_SHOTGUN,
	L4D1_ACT_CROUCHIDLE_SHOTGUN,
	L4D1_ACT_RUN_CROUCH_SHOTGUN,
	L4D1_ACT_DEPLOY_SHOTGUN,
	L4D1_ACT_PRIMARYATTACK_SHOTGUN,
	L4D1_ACT_RELOAD_SHOTGUN_START,
	L4D1_ACT_RELOAD_SHOTGUN_LOOP,
	L4D1_ACT_RELOAD_SHOTGUN_END,
	L4D1_ACT_IDLE_PUMPSHOTGUN,
	L4D1_ACT_CROUCHIDLE_PUMPSHOTGUN,
	L4D1_ACT_IDLE_CALM_PUMPSHOTGUN,
	L4D1_ACT_IDLE_INJURED_PUMPSHOTGUN,
	L4D1_ACT_WALK_PUMPSHOTGUN,
	L4D1_ACT_RUN_CROUCH_PUMPSHOTGUN,
	L4D1_ACT_RUN_PUMPSHOTGUN,
	L4D1_ACT_WALK_CALM_PUMPSHOTGUN,
	L4D1_ACT_RUN_CALM_PUMPSHOTGUN,
	L4D1_ACT_WALK_INJURED_PUMPSHOTGUN,
	L4D1_ACT_RUN_INJURED_PUMPSHOTGUN,
	L4D1_ACT_PRIMARYATTACK_PUMPSHOTGUN,
	L4D1_ACT_RELOAD_PUMPSHOTGUN_START,
	L4D1_ACT_RELOAD_PUMPSHOTGUN_LOOP,
	L4D1_ACT_RELOAD_PUMPSHOTGUN_END,
	L4D1_ACT_IDLE_GREN,
	L4D1_ACT_CROUCHIDLE_GREN,
	L4D1_ACT_IDLE_INJURED_GREN,
	L4D1_ACT_RUN_GREN,
	L4D1_ACT_WALK_GREN,
	L4D1_ACT_RUN_CROUCH_GREN,
	L4D1_ACT_DEPLOY_GREN,
	L4D1_ACT_WALK_INJURED_GREN,
	L4D1_ACT_RUN_INJURED_GREN,
	L4D1_ACT_PRIMARYATTACK_GREN1_IDLE,
	L4D1_ACT_PRIMARYATTACK_GREN2_IDLE,
	L4D1_ACT_PRIMARYATTACK_GREN1_RUN,
	L4D1_ACT_PRIMARYATTACK_GREN2_RUN,
	L4D1_ACT_IDLE_GREN_PULL_BACK,
	L4D1_ACT_CROUCHIDLE_GREN_PULL_BACK,
	L4D1_ACT_IDLE_INJURED_GREN_PULL_BACK,
	L4D1_ACT_RUN_GREN_PULL_BACK,
	L4D1_ACT_WALK_GREN_PULL_BACK,
	L4D1_ACT_RUN_CROUCH_GREN_PULL_BACK,
	L4D1_ACT_WALK_INJURED_GREN_PULL_BACK,
	L4D1_ACT_RUN_INJURED_GREN_PULL_BACK,
	L4D1_ACT_IDLE_SNIPER,
	L4D1_ACT_CROUCHIDLE_SNIPER,
	L4D1_ACT_IDLE_CALM_SNIPER,
	L4D1_ACT_IDLE_INJURED_SNIPER,
	L4D1_ACT_WALK_SNIPER,
	L4D1_ACT_RUN_CROUCH_SNIPER,
	L4D1_ACT_RUN_SNIPER,
	L4D1_ACT_WALK_CALM_SNIPER,
	L4D1_ACT_RUN_CALM_SNIPER,
	L4D1_ACT_WALK_INJURED_SNIPER,
	L4D1_ACT_RUN_INJURED_SNIPER,
	L4D1_ACT_IDLE_SNIPER_ZOOMED,
	L4D1_ACT_CROUCHIDLE_SNIPER_ZOOMED,
	L4D1_ACT_IDLE_INJURED_SNIPER_ZOOMED,
	L4D1_ACT_IDLE_SMG,
	L4D1_ACT_WALK_SMG,
	L4D1_ACT_RUN_SMG,
	L4D1_ACT_CROUCHIDLE_SMG,
	L4D1_ACT_RUN_CROUCH_SMG,
	L4D1_ACT_IDLE_INJURED_SMG,
	L4D1_ACT_WALK_INJURED_SMG,
	L4D1_ACT_RUN_INJURED_SMG,
	L4D1_ACT_IDLE_CALM_SMG,
	L4D1_ACT_WALK_CALM_SMG,
	L4D1_ACT_RUN_CALM_SMG,
	L4D1_ACT_PRIMARYATTACK_SMG,
	L4D1_ACT_RELOAD_SMG,
	L4D1_ACT_IDLE_FIRSTAIDKIT,
	L4D1_ACT_CROUCHIDLE_FIRSTAIDKIT,
	L4D1_ACT_IDLE_INJURED_FIRSTAIDKIT,
	L4D1_ACT_RUN_FIRSTAIDKIT,
	L4D1_ACT_WALK_FIRSTAIDKIT,
	L4D1_ACT_RUN_CROUCH_FIRSTAIDKIT,
	L4D1_ACT_WALK_INJURED_FIRSTAIDKIT,
	L4D1_ACT_RUN_INJURED_FIRSTAIDKIT,
	L4D1_ACT_CROUCHIDLE_PISTOL,
	L4D1_ACT_RUN_CROUCH_PISTOL,
	L4D1_ACT_IDLE_INJURED_PISTOL,
	L4D1_ACT_WALK_INJURED_PISTOL,
	L4D1_ACT_RUN_INJURED_PISTOL,
	L4D1_ACT_DEPLOY_PISTOL,
	L4D1_ACT_PRIMARYATTACK_PISTOL,
	L4D1_ACT_IDLE_CALM_PISTOL,
	L4D1_ACT_WALK_CALM_PISTOL,
	L4D1_ACT_RUN_CALM_PISTOL,
	L4D1_ACT_IDLE_ELITES,
	L4D1_ACT_WALK_ELITES,
	L4D1_ACT_RUN_ELITES,
	L4D1_ACT_CROUCHIDLE_ELITES,
	L4D1_ACT_RUN_CROUCH_ELITES,
	L4D1_ACT_IDLE_INJURED_ELITES,
	L4D1_ACT_WALK_INJURED_ELITES,
	L4D1_ACT_RUN_INJURED_ELITES,
	L4D1_ACT_DEPLOY_ELITES,
	L4D1_ACT_PRIMARYATTACK_ELITES_R,
	L4D1_ACT_PRIMARYATTACK_ELITES_L,
	L4D1_ACT_IDLE_CALM_ELITES,
	L4D1_ACT_WALK_CALM_ELITES,
	L4D1_ACT_RUN_CALM_ELITES,
	L4D1_ACT_RELOAD_ELITES,
	L4D1_ACT_RELOAD_M4,
	L4D1_ACT_PRIMARYATTACK_XM1014,
	L4D1_ACT_PRIMARYATTACK_M3S90,
	L4D1_ACT_IDLE_GASCAN,
	L4D1_ACT_CROUCHIDLE_GASCAN,
	L4D1_ACT_IDLE_CALM_GASCAN,
	L4D1_ACT_IDLE_INJURED_GASCAN,
	L4D1_ACT_RUN_GASCAN,
	L4D1_ACT_WALK_GASCAN,
	L4D1_ACT_RUN_CROUCH_GASCAN,
	L4D1_ACT_RUN_INJURED_GASCAN,
	L4D1_ACT_JUMP_GASCAN,
	L4D1_ACT_MELEE_SWEEP_GASCAN,
	L4D1_ACT_IDLE_O2,
	L4D1_ACT_CROUCHIDLE_O2,
	L4D1_ACT_IDLE_CALM_O2,
	L4D1_ACT_IDLE_INJURED_O2,
	L4D1_ACT_RUN_O2,
	L4D1_ACT_WALK_O2,
	L4D1_ACT_RUN_CROUCH_O2,
	L4D1_ACT_RUN_INJURED_O2,
	L4D1_ACT_JUMP_O2,
	L4D1_ACT_MELEE_SWEEP_O2,
	L4D1_ACT_VM_MELEE,
	L4D1_ACT_VM_SHOOT_LAYER,
	L4D1_ACT_VM_SECONDARYATTACK_LAYER,
	L4D1_ACT_VM_MELEE_LAYER,
	L4D1_ACT_VM_PICKUP_LAYER,
	L4D1_ACT_VM_PICKUP_CLIPIN,
	L4D1_ACT_VM_PICKUP_CLIPIN_LAYER,
	L4D1_ACT_VM_PICKUP_CHARGING,
	L4D1_ACT_VM_PICKUP_CHARGING_LAYER,
	L4D1_ACT_VM_PICKUP_FASSIST,
	L4D1_ACT_VM_PICKUP_FASSIST_LAYER,
	L4D1_ACT_VM_RELOAD_PUMP,
	L4D1_ACT_VM_RELOAD_LAYER,
	L4D1_ACT_VM_RELOAD_CLIPOUT,
	L4D1_ACT_VM_RELOAD_CLIPOUT_LAYER,
	L4D1_ACT_VM_RELOAD_CLIPIN,
	L4D1_ACT_VM_RELOAD_CLIPIN_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPOUT,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPOUT_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN2,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN2_LAYER,
	L4D1_ACT_VM_DEPLOY_LAYER,
	L4D1_ACT_VM_THROW_LAYER,
	L4D1_ACT_VM_PULLPIN_LAYER,
	L4D1_ACT_VM_IDLE_BAREHAND,
	L4D1_ACT_VM_DEPLOY_BAREHAND,
	L4D1_ACT_VM_DEPLOY_BAREHAND_LAYER,
	L4D1_ACT_VM_MELEE_BAREHAND,
	L4D1_ACT_VM_MELEE_BAREHAND_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_BAREHAND,
	L4D1_ACT_VM_ITEMPICKUP_EXTEND_BAREHAND_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_BAREHAND,
	L4D1_ACT_VM_ITEMPICKUP_LOOP_BAREHAND_LAYER,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_BAREHAND,
	L4D1_ACT_VM_ITEMPICKUP_RETRACT_BAREHAND_LAYER,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_BAREHAND,
	L4D1_ACT_VM_HELPINGHAND_EXTEND_BAREHAND_LAYER,
	L4D1_ACT_VM_HELPINGHAND_LOOP_BAREHAND,
	L4D1_ACT_VM_HELPINGHAND_LOOP_BAREHAND_LAYER,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_BAREHAND,
	L4D1_ACT_VM_HELPINGHAND_RETRACT_BAREHAND_LAYER,
	L4D1_ACT_VM_IDLE_DUAL_PISTOL,
	L4D1_ACT_VM_DEPLOY_DUAL_PISTOL,
	L4D1_ACT_VM_DEPLOY_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_PRIMARYATTACK_DUAL_PISTOL,
	L4D1_ACT_VM_SHOOT_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_SECONDARYATTACK_DUAL_PISTOL,
	L4D1_ACT_VM_SECONDARYATTACK_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_RELOAD_DUAL_PISTOL,
	L4D1_ACT_VM_RELOAD_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_RELOAD_CLIPOUT_DUAL_PISTOL,
	L4D1_ACT_VM_RELOAD_CLIPOUT_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_RELOAD_CLIPIN_DUAL_PISTOL,
	L4D1_ACT_VM_RELOAD_CLIPIN_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_DUAL_PISTOL,
	L4D1_ACT_VM_RELOAD_EMPTY_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPOUT_DUAL_PISTOL,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPOUT_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN_DUAL_PISTOL,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN2_DUAL_PISTOL,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN2_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_PICKUP_DUAL_PISTOL,
	L4D1_ACT_VM_PICKUP_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_PICKUP_CLIPIN_DUAL_PISTOL,
	L4D1_ACT_VM_PICKUP_CLIPIN_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_PICKUP_CHARGING_DUAL_PISTOL,
	L4D1_ACT_VM_PICKUP_CHARGING_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_MELEE_DUAL_PISTOL,
	L4D1_ACT_VM_MELEE_DUAL_PISTOL_LAYER,
	L4D1_ACT_VM_IDLE_PISTOL,
	L4D1_ACT_VM_DEPLOY_PISTOL,
	L4D1_ACT_VM_DEPLOY_PISTOL_LAYER,
	L4D1_ACT_VM_PRIMARYATTACK_PISTOL,
	L4D1_ACT_VM_SHOOT_PISTOL_LAYER,
	L4D1_ACT_VM_RELOAD_PISTOL,
	L4D1_ACT_VM_RELOAD_PISTOL_LAYER,
	L4D1_ACT_VM_RELOAD_CLIPOUT_PISTOL,
	L4D1_ACT_VM_RELOAD_CLIPOUT_PISTOL_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_PISTOL,
	L4D1_ACT_VM_RELOAD_EMPTY_PISTOL_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPOUT_PISTOL,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPOUT_PISTOL_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN_PISTOL,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN_PISTOL_LAYER,
	L4D1_ACT_VM_MELEE_PISTOL,
	L4D1_ACT_VM_MELEE_PISTOL_LAYER,
	L4D1_ACT_VM_IDLE_RIFLE,
	L4D1_ACT_VM_PICKUP_RIFLE,
	L4D1_ACT_VM_PICKUP_RIFLE_LAYER,
	L4D1_ACT_VM_PICKUP_CLIPIN_RIFLE,
	L4D1_ACT_VM_PICKUP_CLIPIN_RIFLE_LAYER,
	L4D1_ACT_VM_PICKUP_CHARGING_RIFLE,
	L4D1_ACT_VM_PICKUP_CHARGING_RIFLE_LAYER,
	L4D1_ACT_VM_PICKUP_FASSIST_RIFLE,
	L4D1_ACT_VM_PICKUP_FASSIST_RIFLE_LAYER,
	L4D1_ACT_VM_DEPLOY_RIFLE,
	L4D1_ACT_VM_DEPLOY_RIFLE_LAYER,
	L4D1_ACT_VM_PRIMARYATTACK_RIFLE,
	L4D1_ACT_VM_SHOOT_RIFLE_LAYER,
	L4D1_ACT_VM_RELOAD_RIFLE,
	L4D1_ACT_VM_RELOAD_RIFLE_LAYER,
	L4D1_ACT_VM_RELOAD_CLIPOUT_RIFLE,
	L4D1_ACT_VM_RELOAD_CLIPOUT_RIFLE_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_RIFLE,
	L4D1_ACT_VM_RELOAD_EMPTY_RIFLE_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPOUT_RIFLE,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPOUT_RIFLE_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN_RIFLE,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN_RIFLE_LAYER,
	L4D1_ACT_VM_MELEE_RIFLE,
	L4D1_ACT_VM_MELEE_RIFLE_LAYER,
	L4D1_ACT_VM_FIDGET_RIFLE_LAYER,
	L4D1_ACT_VM_IDLE_SMG,
	L4D1_ACT_VM_PICKUP_SMG,
	L4D1_ACT_VM_PICKUP_SMG_LAYER,
	L4D1_ACT_VM_PICKUP_CLIPIN_SMG,
	L4D1_ACT_VM_PICKUP_CLIPIN_SMG_LAYER,
	L4D1_ACT_VM_PICKUP_CHARGING_SMG,
	L4D1_ACT_VM_PICKUP_CHARGING_SMG_LAYER,
	L4D1_ACT_VM_DEPLOY_SMG,
	L4D1_ACT_VM_DEPLOY_SMG_LAYER,
	L4D1_ACT_VM_PRIMARYATTACK_SMG,
	L4D1_ACT_VM_SHOOT_SMG_LAYER,
	L4D1_ACT_VM_RELOAD_SMG,
	L4D1_ACT_VM_RELOAD_SMG_LAYER,
	L4D1_ACT_VM_RELOAD_CLIPOUT_SMG,
	L4D1_ACT_VM_RELOAD_CLIPOUT_SMG_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_SMG,
	L4D1_ACT_VM_RELOAD_EMPTY_SMG_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPOUT_SMG,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPOUT_SMG_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN_SMG,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN_SMG_LAYER,
	L4D1_ACT_VM_MELEE_SMG,
	L4D1_ACT_VM_MELEE_SMG_LAYER,
	L4D1_ACT_VM_FIDGET_SMG_LAYER,
	L4D1_ACT_VM_IDLE_SNIPER,
	L4D1_ACT_VM_PICKUP_SNIPER,
	L4D1_ACT_VM_PICKUP_SNIPER_LAYER,
	L4D1_ACT_VM_PICKUP_CLIPIN_SNIPER,
	L4D1_ACT_VM_PICKUP_CLIPIN_SNIPER_LAYER,
	L4D1_ACT_VM_PICKUP_CHARGING_SNIPER,
	L4D1_ACT_VM_PICKUP_CHARGING_SNIPER_LAYER,
	L4D1_ACT_VM_DEPLOY_SNIPER,
	L4D1_ACT_VM_DEPLOY_SNIPER_LAYER,
	L4D1_ACT_VM_PRIMARYATTACK_SNIPER,
	L4D1_ACT_VM_SHOOT_SNIPER_LAYER,
	L4D1_ACT_VM_RELOAD_SNIPER,
	L4D1_ACT_VM_RELOAD_SNIPER_LAYER,
	L4D1_ACT_VM_RELOAD_CLIPOUT_SNIPER,
	L4D1_ACT_VM_RELOAD_CLIPOUT_SNIPER_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_SNIPER,
	L4D1_ACT_VM_RELOAD_EMPTY_SNIPER_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPOUT_SNIPER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPOUT_SNIPER_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN_SNIPER,
	L4D1_ACT_VM_RELOAD_EMPTY_CLIPIN_SNIPER_LAYER,
	L4D1_ACT_VM_MELEE_SNIPER,
	L4D1_ACT_VM_MELEE_SNIPER_LAYER,
	L4D1_ACT_VM_FIDGET_SNIPER_LAYER,
	L4D1_ACT_VM_IDLE_SHOTGUN,
	L4D1_ACT_VM_DEPLOY_SHOTGUN,
	L4D1_ACT_VM_DEPLOY_SHOTGUN_LAYER,
	L4D1_ACT_VM_PRIMARYATTACK_SHOTGUN,
	L4D1_ACT_VM_SHOOT_SHOTGUN_LAYER,
	L4D1_ACT_VM_MELEE_SHOTGUN,
	L4D1_ACT_VM_MELEE_SHOTGUN_LAYER,
	L4D1_ACT_VM_PICKUP_SHOTGUN,
	L4D1_ACT_VM_PICKUP_SHOTGUN_LAYER,
	L4D1_ACT_VM_PUMP_SHOTGUN,
	L4D1_ACT_VM_PUMP_SHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_START_SHOTGUN,
	L4D1_ACT_VM_RELOAD_START_SHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_INSERT_SHOTGUN,
	L4D1_ACT_VM_RELOAD_INSERT_SHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_NOPUMP_SHOTGUN,
	L4D1_ACT_VM_RELOAD_NOPUMP_SHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_START_SHOTGUN,
	L4D1_ACT_VM_RELOAD_EMPTY_START_SHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_INSERT_SHOTGUN,
	L4D1_ACT_VM_RELOAD_EMPTY_INSERT_SHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_PUMP_SHOTGUN,
	L4D1_ACT_VM_RELOAD_EMPTY_PUMP_SHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_PUMPREADY_SHOTGUN,
	L4D1_ACT_VM_RELOAD_EMPTY_PUMPREADY_SHOTGUN_LAYER,
	L4D1_ACT_VM_IDLE_AUTOSHOTGUN,
	L4D1_ACT_VM_DEPLOY_AUTOSHOTGUN,
	L4D1_ACT_VM_DEPLOY_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_PRIMARYATTACK_AUTOSHOTGUN,
	L4D1_ACT_VM_SHOOT_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_MELEE_AUTOSHOTGUN,
	L4D1_ACT_VM_MELEE_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_PICKUP_AUTOSHOTGUN,
	L4D1_ACT_VM_PICKUP_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_PUMP_AUTOSHOTGUN,
	L4D1_ACT_VM_PUMP_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_START_AUTOSHOTGUN,
	L4D1_ACT_VM_RELOAD_START_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_INSERT_AUTOSHOTGUN,
	L4D1_ACT_VM_RELOAD_INSERT_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_NOPUMP_AUTOSHOTGUN,
	L4D1_ACT_VM_RELOAD_NOPUMP_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_START_AUTOSHOTGUN,
	L4D1_ACT_VM_RELOAD_EMPTY_START_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_INSERT_AUTOSHOTGUN,
	L4D1_ACT_VM_RELOAD_EMPTY_INSERT_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_PUMP_AUTOSHOTGUN,
	L4D1_ACT_VM_RELOAD_EMPTY_PUMP_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_RELOAD_EMPTY_PUMPREADY_AUTOSHOTGUN,
	L4D1_ACT_VM_RELOAD_EMPTY_PUMPREADY_AUTOSHOTGUN_LAYER,
	L4D1_ACT_VM_IDLE_PIPEBOMB,
	L4D1_ACT_VM_DEPLOY_PIPEBOMB,
	L4D1_ACT_VM_DEPLOY_PIPEBOMB_LAYER,
	L4D1_ACT_VM_PULLPIN_PIPEBOMB,
	L4D1_ACT_VM_PULLPIN_PIPEBOMB_LAYER,
	L4D1_ACT_VM_THROW_PIPEBOMB,
	L4D1_ACT_VM_THROW_PIPEBOMB_LAYER,
	L4D1_ACT_VM_MELEE_PIPEBOMB,
	L4D1_ACT_VM_MELEE_PIPEBOMB_LAYER,
	L4D1_ACT_VM_IDLE_MOLOTOV,
	L4D1_ACT_VM_DEPLOY_MOLOTOV,
	L4D1_ACT_VM_DEPLOY_MOLOTOV_LAYER,
	L4D1_ACT_VM_PULLPIN_MOLOTOV,
	L4D1_ACT_VM_PULLPIN_MOLOTOV_LAYER,
	L4D1_ACT_VM_THROW_MOLOTOV,
	L4D1_ACT_VM_THROW_MOLOTOV_LAYER,
	L4D1_ACT_VM_MELEE_MOLOTOV,
	L4D1_ACT_VM_MELEE_MOLOTOV_LAYER,
	L4D1_ACT_VM_IDLE_PAINPILLS,
	L4D1_ACT_VM_DEPLOY_PAINPILLS,
	L4D1_ACT_VM_DEPLOY_PAINPILLS_LAYER,
	L4D1_ACT_VM_MELEE_PAINPILLS,
	L4D1_ACT_VM_MELEE_PAINPILLS_LAYER,
	L4D1_ACT_VM_USE_PAINPILLS,
	L4D1_ACT_VM_USE_PAINPILLS_LAYER,
	L4D1_ACT_VM_IDLE_MEDKIT,
	L4D1_ACT_VM_DEPLOY_MEDKIT,
	L4D1_ACT_VM_DEPLOY_MEDKIT_LAYER,
	L4D1_ACT_VM_MELEE_MEDKIT,
	L4D1_ACT_VM_MELEE_MEDKIT_LAYER,
	L4D1_ACT_VM_IDLE_GASCAN,
	L4D1_ACT_VM_DEPLOY_GASCAN,
	L4D1_ACT_VM_PULLPIN_GASCAN,
	L4D1_ACT_VM_THROW_GASCAN,
	L4D1_ACT_VM_MELEE_GASCAN,
	L4D1_ACT_VM_FIZZLE,
	L4D1_ACT_MP_STAND_IDLE,
	L4D1_ACT_MP_CROUCH_IDLE,
	L4D1_ACT_MP_CROUCH_DEPLOYED_IDLE,
	L4D1_ACT_MP_CROUCH_DEPLOYED,
	L4D1_ACT_MP_DEPLOYED_IDLE,
	L4D1_ACT_MP_RUN,
	L4D1_ACT_MP_WALK,
	L4D1_ACT_MP_AIRWALK,
	L4D1_ACT_MP_CROUCHWALK,
	L4D1_ACT_MP_SPRINT,
	L4D1_ACT_MP_JUMP,
	L4D1_ACT_MP_JUMP_START,
	L4D1_ACT_MP_JUMP_FLOAT,
	L4D1_ACT_MP_JUMP_LAND,
	L4D1_ACT_MP_DOUBLEJUMP,
	L4D1_ACT_MP_SWIM,
	L4D1_ACT_MP_DEPLOYED,
	L4D1_ACT_MP_SWIM_DEPLOYED,
	L4D1_ACT_MP_VCD,
	L4D1_ACT_MP_ATTACK_STAND_PRIMARYFIRE,
	L4D1_ACT_MP_ATTACK_STAND_PRIMARYFIRE_DEPLOYED,
	L4D1_ACT_MP_ATTACK_STAND_SECONDARYFIRE,
	L4D1_ACT_MP_ATTACK_STAND_GRENADE,
	L4D1_ACT_MP_ATTACK_CROUCH_PRIMARYFIRE,
	L4D1_ACT_MP_ATTACK_CROUCH_PRIMARYFIRE_DEPLOYED,
	L4D1_ACT_MP_ATTACK_CROUCH_SECONDARYFIRE,
	L4D1_ACT_MP_ATTACK_CROUCH_GRENADE,
	L4D1_ACT_MP_ATTACK_SWIM_PRIMARYFIRE,
	L4D1_ACT_MP_ATTACK_SWIM_SECONDARYFIRE,
	L4D1_ACT_MP_ATTACK_SWIM_GRENADE,
	L4D1_ACT_MP_ATTACK_AIRWALK_PRIMARYFIRE,
	L4D1_ACT_MP_ATTACK_AIRWALK_SECONDARYFIRE,
	L4D1_ACT_MP_ATTACK_AIRWALK_GRENADE,
	L4D1_ACT_MP_RELOAD_STAND,
	L4D1_ACT_MP_RELOAD_STAND_LOOP,
	L4D1_ACT_MP_RELOAD_STAND_END,
	L4D1_ACT_MP_RELOAD_CROUCH,
	L4D1_ACT_MP_RELOAD_CROUCH_LOOP,
	L4D1_ACT_MP_RELOAD_CROUCH_END,
	L4D1_ACT_MP_RELOAD_SWIM,
	L4D1_ACT_MP_RELOAD_SWIM_LOOP,
	L4D1_ACT_MP_RELOAD_SWIM_END,
	L4D1_ACT_MP_RELOAD_AIRWALK,
	L4D1_ACT_MP_RELOAD_AIRWALK_LOOP,
	L4D1_ACT_MP_RELOAD_AIRWALK_END,
	L4D1_ACT_MP_ATTACK_STAND_PREFIRE,
	L4D1_ACT_MP_ATTACK_STAND_POSTFIRE,
	L4D1_ACT_MP_ATTACK_STAND_STARTFIRE,
	L4D1_ACT_MP_ATTACK_CROUCH_PREFIRE,
	L4D1_ACT_MP_ATTACK_CROUCH_POSTFIRE,
	L4D1_ACT_MP_ATTACK_SWIM_PREFIRE,
	L4D1_ACT_MP_ATTACK_SWIM_POSTFIRE,
	L4D1_ACT_MP_STAND_PRIMARY,
	L4D1_ACT_MP_CROUCH_PRIMARY,
	L4D1_ACT_MP_RUN_PRIMARY,
	L4D1_ACT_MP_WALK_PRIMARY,
	L4D1_ACT_MP_AIRWALK_PRIMARY,
	L4D1_ACT_MP_CROUCHWALK_PRIMARY,
	L4D1_ACT_MP_JUMP_PRIMARY,
	L4D1_ACT_MP_JUMP_START_PRIMARY,
	L4D1_ACT_MP_JUMP_FLOAT_PRIMARY,
	L4D1_ACT_MP_JUMP_LAND_PRIMARY,
	L4D1_ACT_MP_SWIM_PRIMARY,
	L4D1_ACT_MP_DEPLOYED_PRIMARY,
	L4D1_ACT_MP_SWIM_DEPLOYED_PRIMARY,
	L4D1_ACT_MP_ATTACK_STAND_PRIMARY,
	L4D1_ACT_MP_ATTACK_STAND_PRIMARY_DEPLOYED,
	L4D1_ACT_MP_ATTACK_CROUCH_PRIMARY,
	L4D1_ACT_MP_ATTACK_CROUCH_PRIMARY_DEPLOYED,
	L4D1_ACT_MP_ATTACK_SWIM_PRIMARY,
	L4D1_ACT_MP_ATTACK_AIRWALK_PRIMARY,
	L4D1_ACT_MP_RELOAD_STAND_PRIMARY,
	L4D1_ACT_MP_RELOAD_STAND_PRIMARY_LOOP,
	L4D1_ACT_MP_RELOAD_STAND_PRIMARY_END,
	L4D1_ACT_MP_RELOAD_CROUCH_PRIMARY,
	L4D1_ACT_MP_RELOAD_CROUCH_PRIMARY_LOOP,
	L4D1_ACT_MP_RELOAD_CROUCH_PRIMARY_END,
	L4D1_ACT_MP_RELOAD_SWIM_PRIMARY,
	L4D1_ACT_MP_RELOAD_SWIM_PRIMARY_LOOP,
	L4D1_ACT_MP_RELOAD_SWIM_PRIMARY_END,
	L4D1_ACT_MP_RELOAD_AIRWALK_PRIMARY,
	L4D1_ACT_MP_RELOAD_AIRWALK_PRIMARY_LOOP,
	L4D1_ACT_MP_RELOAD_AIRWALK_PRIMARY_END,
	L4D1_ACT_MP_ATTACK_STAND_GRENADE_PRIMARY,
	L4D1_ACT_MP_ATTACK_CROUCH_GRENADE_PRIMARY,
	L4D1_ACT_MP_ATTACK_SWIM_GRENADE_PRIMARY,
	L4D1_ACT_MP_ATTACK_AIRWALK_GRENADE_PRIMARY,
	L4D1_ACT_MP_STAND_SECONDARY,
	L4D1_ACT_MP_CROUCH_SECONDARY,
	L4D1_ACT_MP_RUN_SECONDARY,
	L4D1_ACT_MP_WALK_SECONDARY,
	L4D1_ACT_MP_AIRWALK_SECONDARY,
	L4D1_ACT_MP_CROUCHWALK_SECONDARY,
	L4D1_ACT_MP_JUMP_SECONDARY,
	L4D1_ACT_MP_JUMP_START_SECONDARY,
	L4D1_ACT_MP_JUMP_FLOAT_SECONDARY,
	L4D1_ACT_MP_JUMP_LAND_SECONDARY,
	L4D1_ACT_MP_SWIM_SECONDARY,
	L4D1_ACT_MP_ATTACK_STAND_SECONDARY,
	L4D1_ACT_MP_ATTACK_CROUCH_SECONDARY,
	L4D1_ACT_MP_ATTACK_SWIM_SECONDARY,
	L4D1_ACT_MP_ATTACK_AIRWALK_SECONDARY,
	L4D1_ACT_MP_RELOAD_STAND_SECONDARY,
	L4D1_ACT_MP_RELOAD_STAND_SECONDARY_LOOP,
	L4D1_ACT_MP_RELOAD_STAND_SECONDARY_END,
	L4D1_ACT_MP_RELOAD_CROUCH_SECONDARY,
	L4D1_ACT_MP_RELOAD_CROUCH_SECONDARY_LOOP,
	L4D1_ACT_MP_RELOAD_CROUCH_SECONDARY_END,
	L4D1_ACT_MP_RELOAD_SWIM_SECONDARY,
	L4D1_ACT_MP_RELOAD_SWIM_SECONDARY_LOOP,
	L4D1_ACT_MP_RELOAD_SWIM_SECONDARY_END,
	L4D1_ACT_MP_RELOAD_AIRWALK_SECONDARY,
	L4D1_ACT_MP_RELOAD_AIRWALK_SECONDARY_LOOP,
	L4D1_ACT_MP_RELOAD_AIRWALK_SECONDARY_END,
	L4D1_ACT_MP_ATTACK_STAND_GRENADE_SECONDARY,
	L4D1_ACT_MP_ATTACK_CROUCH_GRENADE_SECONDARY,
	L4D1_ACT_MP_ATTACK_SWIM_GRENADE_SECONDARY,
	L4D1_ACT_MP_ATTACK_AIRWALK_GRENADE_SECONDARY,
	L4D1_ACT_MP_STAND_MELEE,
	L4D1_ACT_MP_CROUCH_MELEE,
	L4D1_ACT_MP_RUN_MELEE,
	L4D1_ACT_MP_WALK_MELEE,
	L4D1_ACT_MP_AIRWALK_MELEE,
	L4D1_ACT_MP_CROUCHWALK_MELEE,
	L4D1_ACT_MP_JUMP_MELEE,
	L4D1_ACT_MP_JUMP_START_MELEE,
	L4D1_ACT_MP_JUMP_FLOAT_MELEE,
	L4D1_ACT_MP_JUMP_LAND_MELEE,
	L4D1_ACT_MP_SWIM_MELEE,
	L4D1_ACT_MP_ATTACK_STAND_MELEE,
	L4D1_ACT_MP_ATTACK_STAND_MELEE_SECONDARY,
	L4D1_ACT_MP_ATTACK_CROUCH_MELEE,
	L4D1_ACT_MP_ATTACK_CROUCH_MELEE_SECONDARY,
	L4D1_ACT_MP_ATTACK_SWIM_MELEE,
	L4D1_ACT_MP_ATTACK_AIRWALK_MELEE,
	L4D1_ACT_MP_ATTACK_STAND_GRENADE_MELEE,
	L4D1_ACT_MP_ATTACK_CROUCH_GRENADE_MELEE,
	L4D1_ACT_MP_ATTACK_SWIM_GRENADE_MELEE,
	L4D1_ACT_MP_ATTACK_AIRWALK_GRENADE_MELEE,
	L4D1_ACT_MP_GESTURE_FLINCH,
	L4D1_ACT_MP_GESTURE_FLINCH_PRIMARY,
	L4D1_ACT_MP_GESTURE_FLINCH_SECONDARY,
	L4D1_ACT_MP_GESTURE_FLINCH_MELEE,
	L4D1_ACT_MP_GESTURE_FLINCH_HEAD,
	L4D1_ACT_MP_GESTURE_FLINCH_CHEST,
	L4D1_ACT_MP_GESTURE_FLINCH_STOMACH,
	L4D1_ACT_MP_GESTURE_FLINCH_LEFTARM,
	L4D1_ACT_MP_GESTURE_FLINCH_RIGHTARM,
	L4D1_ACT_MP_GESTURE_FLINCH_LEFTLEG,
	L4D1_ACT_MP_GESTURE_FLINCH_RIGHTLEG,
	L4D1_ACT_MP_GRENADE1_DRAW,
	L4D1_ACT_MP_GRENADE1_IDLE,
	L4D1_ACT_MP_GRENADE1_ATTACK,
	L4D1_ACT_MP_GRENADE2_DRAW,
	L4D1_ACT_MP_GRENADE2_IDLE,
	L4D1_ACT_MP_GRENADE2_ATTACK,
	L4D1_ACT_MP_PRIMARY_GRENADE1_DRAW,
	L4D1_ACT_MP_PRIMARY_GRENADE1_IDLE,
	L4D1_ACT_MP_PRIMARY_GRENADE1_ATTACK,
	L4D1_ACT_MP_PRIMARY_GRENADE2_DRAW,
	L4D1_ACT_MP_PRIMARY_GRENADE2_IDLE,
	L4D1_ACT_MP_PRIMARY_GRENADE2_ATTACK,
	L4D1_ACT_MP_SECONDARY_GRENADE1_DRAW,
	L4D1_ACT_MP_SECONDARY_GRENADE1_IDLE,
	L4D1_ACT_MP_SECONDARY_GRENADE1_ATTACK,
	L4D1_ACT_MP_SECONDARY_GRENADE2_DRAW,
	L4D1_ACT_MP_SECONDARY_GRENADE2_IDLE,
	L4D1_ACT_MP_SECONDARY_GRENADE2_ATTACK,
	L4D1_ACT_MP_MELEE_GRENADE1_DRAW,
	L4D1_ACT_MP_MELEE_GRENADE1_IDLE,
	L4D1_ACT_MP_MELEE_GRENADE1_ATTACK,
	L4D1_ACT_MP_MELEE_GRENADE2_DRAW,
	L4D1_ACT_MP_MELEE_GRENADE2_IDLE,
	L4D1_ACT_MP_MELEE_GRENADE2_ATTACK,
	L4D1_ACT_MP_STAND_BUILDING,
	L4D1_ACT_MP_CROUCH_BUILDING,
	L4D1_ACT_MP_RUN_BUILDING,
	L4D1_ACT_MP_WALK_BUILDING,
	L4D1_ACT_MP_AIRWALK_BUILDING,
	L4D1_ACT_MP_CROUCHWALK_BUILDING,
	L4D1_ACT_MP_JUMP_BUILDING,
	L4D1_ACT_MP_JUMP_START_BUILDING,
	L4D1_ACT_MP_JUMP_FLOAT_BUILDING,
	L4D1_ACT_MP_JUMP_LAND_BUILDING,
	L4D1_ACT_MP_SWIM_BUILDING,
	L4D1_ACT_MP_ATTACK_STAND_BUILDING,
	L4D1_ACT_MP_ATTACK_CROUCH_BUILDING,
	L4D1_ACT_MP_ATTACK_SWIM_BUILDING,
	L4D1_ACT_MP_ATTACK_AIRWALK_BUILDING,
	L4D1_ACT_MP_ATTACK_STAND_GRENADE_BUILDING,
	L4D1_ACT_MP_ATTACK_CROUCH_GRENADE_BUILDING,
	L4D1_ACT_MP_ATTACK_SWIM_GRENADE_BUILDING,
	L4D1_ACT_MP_ATTACK_AIRWALK_GRENADE_BUILDING,
	L4D1_ACT_MP_STAND_PDA,
	L4D1_ACT_MP_CROUCH_PDA,
	L4D1_ACT_MP_RUN_PDA,
	L4D1_ACT_MP_WALK_PDA,
	L4D1_ACT_MP_AIRWALK_PDA,
	L4D1_ACT_MP_CROUCHWALK_PDA,
	L4D1_ACT_MP_JUMP_PDA,
	L4D1_ACT_MP_JUMP_START_PDA,
	L4D1_ACT_MP_JUMP_FLOAT_PDA,
	L4D1_ACT_MP_JUMP_LAND_PDA,
	L4D1_ACT_MP_SWIM_PDA,
	L4D1_ACT_MP_ATTACK_STAND_PDA,
	L4D1_ACT_MP_ATTACK_SWIM_PDA,
	L4D1_ACT_MP_GESTURE_VC_HANDMOUTH,
	L4D1_ACT_MP_GESTURE_VC_FINGERPOINT,
	L4D1_ACT_MP_GESTURE_VC_FISTPUMP,
	L4D1_ACT_MP_GESTURE_VC_THUMBSUP,
	L4D1_ACT_MP_GESTURE_VC_NODYES,
	L4D1_ACT_MP_GESTURE_VC_NODNO,
	L4D1_ACT_MP_GESTURE_VC_HANDMOUTH_PRIMARY,
	L4D1_ACT_MP_GESTURE_VC_FINGERPOINT_PRIMARY,
	L4D1_ACT_MP_GESTURE_VC_FISTPUMP_PRIMARY,
	L4D1_ACT_MP_GESTURE_VC_THUMBSUP_PRIMARY,
	L4D1_ACT_MP_GESTURE_VC_NODYES_PRIMARY,
	L4D1_ACT_MP_GESTURE_VC_NODNO_PRIMARY,
	L4D1_ACT_MP_GESTURE_VC_HANDMOUTH_SECONDARY,
	L4D1_ACT_MP_GESTURE_VC_FINGERPOINT_SECONDARY,
	L4D1_ACT_MP_GESTURE_VC_FISTPUMP_SECONDARY,
	L4D1_ACT_MP_GESTURE_VC_THUMBSUP_SECONDARY,
	L4D1_ACT_MP_GESTURE_VC_NODYES_SECONDARY,
	L4D1_ACT_MP_GESTURE_VC_NODNO_SECONDARY,
	L4D1_ACT_MP_GESTURE_VC_HANDMOUTH_MELEE,
	L4D1_ACT_MP_GESTURE_VC_FINGERPOINT_MELEE,
	L4D1_ACT_MP_GESTURE_VC_FISTPUMP_MELEE,
	L4D1_ACT_MP_GESTURE_VC_THUMBSUP_MELEE,
	L4D1_ACT_MP_GESTURE_VC_NODYES_MELEE,
	L4D1_ACT_MP_GESTURE_VC_NODNO_MELEE,
	L4D1_ACT_MP_GESTURE_VC_HANDMOUTH_BUILDING,
	L4D1_ACT_MP_GESTURE_VC_FINGERPOINT_BUILDING,
	L4D1_ACT_MP_GESTURE_VC_FISTPUMP_BUILDING,
	L4D1_ACT_MP_GESTURE_VC_THUMBSUP_BUILDING,
	L4D1_ACT_MP_GESTURE_VC_NODYES_BUILDING,
	L4D1_ACT_MP_GESTURE_VC_NODNO_BUILDING,
	L4D1_ACT_MP_GESTURE_VC_HANDMOUTH_PDA,
	L4D1_ACT_MP_GESTURE_VC_FINGERPOINT_PDA,
	L4D1_ACT_MP_GESTURE_VC_FISTPUMP_PDA,
	L4D1_ACT_MP_GESTURE_VC_THUMBSUP_PDA,
	L4D1_ACT_MP_GESTURE_VC_NODYES_PDA,
	L4D1_ACT_MP_GESTURE_VC_NODNO_PDA,
	L4D1_ACT_VM_UNUSABLE,
	L4D1_ACT_VM_UNUSABLE_TO_USABLE,
	L4D1_ACT_VM_USABLE_TO_UNUSABLE
}



// ====================================================================================================
//										LEFT 4 DEAD 2
// ====================================================================================================
enum
{
	L4D2_ACT_RESET,
	L4D2_ACT_IDLE,
	L4D2_ACT_TRANSITION,
	L4D2_ACT_COVER,
	L4D2_ACT_COVER_MED,
	L4D2_ACT_COVER_LOW,
	L4D2_ACT_WALK,
	L4D2_ACT_WALK_AIM,
	L4D2_ACT_WALK_CROUCH,
	L4D2_ACT_WALK_CROUCH_AIM,
	L4D2_ACT_RUN,
	L4D2_ACT_RUN_AIM,
	L4D2_ACT_RUN_CROUCH,
	L4D2_ACT_RUN_CROUCH_AIM,
	L4D2_ACT_RUN_PROTECTED,
	L4D2_ACT_SCRIPT_CUSTOM_MOVE,
	L4D2_ACT_RANGE_ATTACK1,
	L4D2_ACT_RANGE_ATTACK2,
	L4D2_ACT_RANGE_ATTACK1_LOW,
	L4D2_ACT_RANGE_ATTACK2_LOW,
	L4D2_ACT_DIESIMPLE,
	L4D2_ACT_DIEBACKWARD,
	L4D2_ACT_DIEFORWARD,
	L4D2_ACT_DIEVIOLENT,
	L4D2_ACT_DIERAGDOLL,
	L4D2_ACT_FLY,
	L4D2_ACT_HOVER,
	L4D2_ACT_GLIDE,
	L4D2_ACT_SWIM,
	L4D2_ACT_JUMP,
	L4D2_ACT_HOP,
	L4D2_ACT_LEAP,
	L4D2_ACT_LAND,
	L4D2_ACT_CLIMB_UP,
	L4D2_ACT_CLIMB_DOWN,
	L4D2_ACT_CLIMB_DISMOUNT,
	L4D2_ACT_SHIPLADDER_UP,
	L4D2_ACT_SHIPLADDER_DOWN,
	L4D2_ACT_STRAFE_LEFT,
	L4D2_ACT_STRAFE_RIGHT,
	L4D2_ACT_ROLL_LEFT,
	L4D2_ACT_ROLL_RIGHT,
	L4D2_ACT_TURN_LEFT,
	L4D2_ACT_TURN_RIGHT,
	L4D2_ACT_CROUCH,
	L4D2_ACT_CROUCHIDLE,
	L4D2_ACT_STAND,
	L4D2_ACT_USE,
	L4D2_ACT_SIGNAL1,
	L4D2_ACT_SIGNAL2,
	L4D2_ACT_SIGNAL3,
	L4D2_ACT_SIGNAL_ADVANCE,
	L4D2_ACT_SIGNAL_FORWARD,
	L4D2_ACT_SIGNAL_GROUP,
	L4D2_ACT_SIGNAL_HALT,
	L4D2_ACT_SIGNAL_LEFT,
	L4D2_ACT_SIGNAL_RIGHT,
	L4D2_ACT_SIGNAL_TAKECOVER,
	L4D2_ACT_LOOKBACK_RIGHT,
	L4D2_ACT_LOOKBACK_LEFT,
	L4D2_ACT_COWER,
	L4D2_ACT_SMALL_FLINCH,
	L4D2_ACT_BIG_FLINCH,
	L4D2_ACT_MELEE_ATTACK1,
	L4D2_ACT_MELEE_ATTACK2,
	L4D2_ACT_RELOAD,
	L4D2_ACT_RELOAD_START,
	L4D2_ACT_RELOAD_FINISH,
	L4D2_ACT_RELOAD_LOW,
	L4D2_ACT_ARM,
	L4D2_ACT_DISARM,
	L4D2_ACT_DROP_WEAPON,
	L4D2_ACT_DROP_WEAPON_SHOTGUN,
	L4D2_ACT_PICKUP_GROUND,
	L4D2_ACT_PICKUP_RACK,
	L4D2_ACT_IDLE_ANGRY,
	L4D2_ACT_IDLE_RELAXED,
	L4D2_ACT_IDLE_STIMULATED,
	L4D2_ACT_IDLE_AGITATED,
	L4D2_ACT_IDLE_STEALTH,
	L4D2_ACT_IDLE_HURT,
	L4D2_ACT_WALK_RELAXED,
	L4D2_ACT_WALK_STIMULATED,
	L4D2_ACT_WALK_AGITATED,
	L4D2_ACT_WALK_STEALTH,
	L4D2_ACT_RUN_RELAXED,
	L4D2_ACT_RUN_STIMULATED,
	L4D2_ACT_RUN_AGITATED,
	L4D2_ACT_RUN_STEALTH,
	L4D2_ACT_IDLE_AIM_RELAXED,
	L4D2_ACT_IDLE_AIM_STIMULATED,
	L4D2_ACT_IDLE_AIM_AGITATED,
	L4D2_ACT_IDLE_AIM_STEALTH,
	L4D2_ACT_WALK_AIM_RELAXED,
	L4D2_ACT_WALK_AIM_STIMULATED,
	L4D2_ACT_WALK_AIM_AGITATED,
	L4D2_ACT_WALK_AIM_STEALTH,
	L4D2_ACT_RUN_AIM_RELAXED,
	L4D2_ACT_RUN_AIM_STIMULATED,
	L4D2_ACT_RUN_AIM_AGITATED,
	L4D2_ACT_RUN_AIM_STEALTH,
	L4D2_ACT_CROUCHIDLE_STIMULATED,
	L4D2_ACT_CROUCHIDLE_AIM_STIMULATED,
	L4D2_ACT_CROUCHIDLE_AGITATED,
	L4D2_ACT_WALK_HURT,
	L4D2_ACT_RUN_HURT,
	L4D2_ACT_SPECIAL_ATTACK1,
	L4D2_ACT_SPECIAL_ATTACK2,
	L4D2_ACT_COMBAT_IDLE,
	L4D2_ACT_WALK_SCARED,
	L4D2_ACT_RUN_SCARED,
	L4D2_ACT_VICTORY_DANCE,
	L4D2_ACT_DIE_HEADSHOT,
	L4D2_ACT_DIE_CHESTSHOT,
	L4D2_ACT_DIE_GUTSHOT,
	L4D2_ACT_DIE_BACKSHOT,
	L4D2_ACT_FLINCH_HEAD,
	L4D2_ACT_FLINCH_CHEST,
	L4D2_ACT_FLINCH_STOMACH,
	L4D2_ACT_FLINCH_LEFTARM,
	L4D2_ACT_FLINCH_RIGHTARM,
	L4D2_ACT_FLINCH_LEFTLEG,
	L4D2_ACT_FLINCH_RIGHTLEG,
	L4D2_ACT_FLINCH_PHYSICS,
	L4D2_ACT_FLINCH_HEAD_BACK,
	L4D2_ACT_FLINCH_CHEST_BACK,
	L4D2_ACT_FLINCH_STOMACH_BACK,
	L4D2_ACT_FLINCH_CROUCH_FRONT,
	L4D2_ACT_FLINCH_CROUCH_BACK,
	L4D2_ACT_FLINCH_CROUCH_LEFT,
	L4D2_ACT_FLINCH_CROUCH_RIGHT,
	L4D2_ACT_IDLE_ON_FIRE,
	L4D2_ACT_WALK_ON_FIRE,
	L4D2_ACT_RUN_ON_FIRE,
	L4D2_ACT_RAPPEL_LOOP,
	L4D2_ACT_180_LEFT,
	L4D2_ACT_180_RIGHT,
	L4D2_ACT_90_LEFT,
	L4D2_ACT_90_RIGHT,
	L4D2_ACT_STEP_LEFT,
	L4D2_ACT_STEP_RIGHT,
	L4D2_ACT_STEP_BACK,
	L4D2_ACT_STEP_FORE,
	L4D2_ACT_GESTURE_RANGE_ATTACK1,
	L4D2_ACT_GESTURE_RANGE_ATTACK2,
	L4D2_ACT_GESTURE_MELEE_ATTACK1,
	L4D2_ACT_GESTURE_MELEE_ATTACK2,
	L4D2_ACT_GESTURE_RANGE_ATTACK1_LOW,
	L4D2_ACT_GESTURE_RANGE_ATTACK2_LOW,
	L4D2_ACT_MELEE_ATTACK_SWING_GESTURE,
	L4D2_ACT_GESTURE_SMALL_FLINCH,
	L4D2_ACT_GESTURE_BIG_FLINCH,
	L4D2_ACT_GESTURE_FLINCH_BLAST,
	L4D2_ACT_GESTURE_FLINCH_BLAST_SHOTGUN,
	L4D2_ACT_GESTURE_FLINCH_BLAST_DAMAGED,
	L4D2_ACT_GESTURE_FLINCH_BLAST_DAMAGED_SHOTGUN,
	L4D2_ACT_GESTURE_FLINCH_HEAD,
	L4D2_ACT_GESTURE_FLINCH_CHEST,
	L4D2_ACT_GESTURE_FLINCH_STOMACH,
	L4D2_ACT_GESTURE_FLINCH_LEFTARM,
	L4D2_ACT_GESTURE_FLINCH_RIGHTARM,
	L4D2_ACT_GESTURE_FLINCH_LEFTLEG,
	L4D2_ACT_GESTURE_FLINCH_RIGHTLEG,
	L4D2_ACT_GESTURE_TURN_LEFT,
	L4D2_ACT_GESTURE_TURN_RIGHT,
	L4D2_ACT_GESTURE_TURN_LEFT45,
	L4D2_ACT_GESTURE_TURN_RIGHT45,
	L4D2_ACT_GESTURE_TURN_LEFT90,
	L4D2_ACT_GESTURE_TURN_RIGHT90,
	L4D2_ACT_GESTURE_TURN_LEFT45_FLAT,
	L4D2_ACT_GESTURE_TURN_RIGHT45_FLAT,
	L4D2_ACT_GESTURE_TURN_LEFT90_FLAT,
	L4D2_ACT_GESTURE_TURN_RIGHT90_FLAT,
	L4D2_ACT_BARNACLE_HIT,
	L4D2_ACT_BARNACLE_PULL,
	L4D2_ACT_BARNACLE_CHOMP,
	L4D2_ACT_BARNACLE_CHEW,
	L4D2_ACT_DO_NOT_DISTURB,
	L4D2_ACT_SPECIFIC_SEQUENCE,
	L4D2_ACT_VM_DEPLOY,
	L4D2_ACT_VM_RELOAD_EMPTY,
	L4D2_ACT_VM_DRAW,
	L4D2_ACT_VM_HOLSTER,
	L4D2_ACT_VM_IDLE,
	L4D2_ACT_VM_FIDGET,
	L4D2_ACT_VM_PULLBACK,
	L4D2_ACT_VM_PULLBACK_HIGH,
	L4D2_ACT_VM_PULLBACK_LOW,
	L4D2_ACT_VM_THROW,
	L4D2_ACT_VM_DROP,
	L4D2_ACT_VM_PULLPIN,
	L4D2_ACT_VM_PRIMARYATTACK,
	L4D2_ACT_VM_SECONDARYATTACK,
	L4D2_ACT_VM_RELOAD,
	L4D2_ACT_VM_DRYFIRE,
	L4D2_ACT_VM_HITLEFT,
	L4D2_ACT_VM_HITLEFT2,
	L4D2_ACT_VM_HITRIGHT,
	L4D2_ACT_VM_HITRIGHT2,
	L4D2_ACT_VM_HITCENTER,
	L4D2_ACT_VM_HITCENTER2,
	L4D2_ACT_VM_MISSLEFT,
	L4D2_ACT_VM_MISSLEFT2,
	L4D2_ACT_VM_MISSRIGHT,
	L4D2_ACT_VM_MISSRIGHT2,
	L4D2_ACT_VM_MISSCENTER,
	L4D2_ACT_VM_MISSCENTER2,
	L4D2_ACT_VM_HAULBACK,
	L4D2_ACT_VM_SWINGHARD,
	L4D2_ACT_VM_SWINGMISS,
	L4D2_ACT_VM_SWINGHIT,
	L4D2_ACT_VM_IDLE_TO_LOWERED,
	L4D2_ACT_VM_IDLE_LOWERED,
	L4D2_ACT_VM_LOWERED_TO_IDLE,
	L4D2_ACT_VM_RECOIL1,
	L4D2_ACT_VM_RECOIL2,
	L4D2_ACT_VM_RECOIL3,
	L4D2_ACT_VM_PICKUP,
	L4D2_ACT_VM_RELEASE,
	L4D2_ACT_VM_MAUL_LOOP,
	L4D2_ACT_VM_ATTACH_SILENCER,
	L4D2_ACT_VM_DETACH_SILENCER,
	L4D2_ACT_SLAM_STICKWALL_IDLE,
	L4D2_ACT_SLAM_STICKWALL_ND_IDLE,
	L4D2_ACT_SLAM_STICKWALL_ATTACH,
	L4D2_ACT_SLAM_STICKWALL_ATTACH2,
	L4D2_ACT_SLAM_STICKWALL_ND_ATTACH,
	L4D2_ACT_SLAM_STICKWALL_ND_ATTACH2,
	L4D2_ACT_SLAM_STICKWALL_DETONATE,
	L4D2_ACT_SLAM_STICKWALL_DETONATOR_HOLSTER,
	L4D2_ACT_SLAM_STICKWALL_DRAW,
	L4D2_ACT_SLAM_STICKWALL_ND_DRAW,
	L4D2_ACT_SLAM_STICKWALL_TO_THROW,
	L4D2_ACT_SLAM_STICKWALL_TO_THROW_ND,
	L4D2_ACT_SLAM_STICKWALL_TO_TRIPMINE_ND,
	L4D2_ACT_SLAM_THROW_IDLE,
	L4D2_ACT_SLAM_THROW_ND_IDLE,
	L4D2_ACT_SLAM_THROW_THROW,
	L4D2_ACT_SLAM_THROW_THROW2,
	L4D2_ACT_SLAM_THROW_THROW_ND,
	L4D2_ACT_SLAM_THROW_THROW_ND2,
	L4D2_ACT_SLAM_THROW_DRAW,
	L4D2_ACT_SLAM_THROW_ND_DRAW,
	L4D2_ACT_SLAM_THROW_TO_STICKWALL,
	L4D2_ACT_SLAM_THROW_TO_STICKWALL_ND,
	L4D2_ACT_SLAM_THROW_DETONATE,
	L4D2_ACT_SLAM_THROW_DETONATOR_HOLSTER,
	L4D2_ACT_SLAM_THROW_TO_TRIPMINE_ND,
	L4D2_ACT_SLAM_TRIPMINE_IDLE,
	L4D2_ACT_SLAM_TRIPMINE_DRAW,
	L4D2_ACT_SLAM_TRIPMINE_ATTACH,
	L4D2_ACT_SLAM_TRIPMINE_ATTACH2,
	L4D2_ACT_SLAM_TRIPMINE_TO_STICKWALL_ND,
	L4D2_ACT_SLAM_TRIPMINE_TO_THROW_ND,
	L4D2_ACT_SLAM_DETONATOR_IDLE,
	L4D2_ACT_SLAM_DETONATOR_DRAW,
	L4D2_ACT_SLAM_DETONATOR_DETONATE,
	L4D2_ACT_SLAM_DETONATOR_HOLSTER,
	L4D2_ACT_SLAM_DETONATOR_STICKWALL_DRAW,
	L4D2_ACT_SLAM_DETONATOR_THROW_DRAW,
	L4D2_ACT_SHOTGUN_RELOAD_START,
	L4D2_ACT_SHOTGUN_RELOAD_FINISH,
	L4D2_ACT_SHOTGUN_PUMP,
	L4D2_ACT_SMG2_IDLE2,
	L4D2_ACT_SMG2_FIRE2,
	L4D2_ACT_SMG2_DRAW2,
	L4D2_ACT_SMG2_RELOAD2,
	L4D2_ACT_SMG2_DRYFIRE2,
	L4D2_ACT_SMG2_TOAUTO,
	L4D2_ACT_SMG2_TOBURST,
	L4D2_ACT_PHYSCANNON_UPGRADE,
	L4D2_ACT_RANGE_ATTACK_AR1,
	L4D2_ACT_RANGE_ATTACK_AR2,
	L4D2_ACT_RANGE_ATTACK_AR2_LOW,
	L4D2_ACT_RANGE_ATTACK_AR2_GRENADE,
	L4D2_ACT_RANGE_ATTACK_HMG1,
	L4D2_ACT_RANGE_ATTACK_ML,
	L4D2_ACT_RANGE_ATTACK_SMG1,
	L4D2_ACT_RANGE_ATTACK_SMG1_LOW,
	L4D2_ACT_RANGE_ATTACK_SMG2,
	L4D2_ACT_RANGE_ATTACK_SHOTGUN,
	L4D2_ACT_RANGE_ATTACK_SHOTGUN_LOW,
	L4D2_ACT_RANGE_ATTACK_PISTOL,
	L4D2_ACT_RANGE_ATTACK_PISTOL_LOW,
	L4D2_ACT_RANGE_ATTACK_SLAM,
	L4D2_ACT_RANGE_ATTACK_TRIPWIRE,
	L4D2_ACT_RANGE_ATTACK_THROW,
	L4D2_ACT_RANGE_ATTACK_SNIPER_RIFLE,
	L4D2_ACT_RANGE_ATTACK_RPG,
	L4D2_ACT_MELEE_ATTACK_SWING,
	L4D2_ACT_RANGE_AIM_LOW,
	L4D2_ACT_RANGE_AIM_SMG1_LOW,
	L4D2_ACT_RANGE_AIM_PISTOL_LOW,
	L4D2_ACT_RANGE_AIM_AR2_LOW,
	L4D2_ACT_COVER_PISTOL_LOW,
	L4D2_ACT_COVER_SMG1_LOW,
	L4D2_ACT_GESTURE_RANGE_ATTACK_AR1,
	L4D2_ACT_GESTURE_RANGE_ATTACK_AR2,
	L4D2_ACT_GESTURE_RANGE_ATTACK_AR2_GRENADE,
	L4D2_ACT_GESTURE_RANGE_ATTACK_HMG1,
	L4D2_ACT_GESTURE_RANGE_ATTACK_ML,
	L4D2_ACT_GESTURE_RANGE_ATTACK_SMG1,
	L4D2_ACT_GESTURE_RANGE_ATTACK_SMG1_LOW,
	L4D2_ACT_GESTURE_RANGE_ATTACK_SMG2,
	L4D2_ACT_GESTURE_RANGE_ATTACK_SHOTGUN,
	L4D2_ACT_GESTURE_RANGE_ATTACK_PISTOL,
	L4D2_ACT_GESTURE_RANGE_ATTACK_PISTOL_LOW,
	L4D2_ACT_GESTURE_RANGE_ATTACK_SLAM,
	L4D2_ACT_GESTURE_RANGE_ATTACK_TRIPWIRE,
	L4D2_ACT_GESTURE_RANGE_ATTACK_THROW,
	L4D2_ACT_GESTURE_RANGE_ATTACK_SNIPER_RIFLE,
	L4D2_ACT_GESTURE_MELEE_ATTACK_SWING,
	L4D2_ACT_IDLE_RIFLE,
	L4D2_ACT_IDLE_SMG1,
	L4D2_ACT_IDLE_ANGRY_SMG1,
	L4D2_ACT_IDLE_PISTOL,
	L4D2_ACT_IDLE_ANGRY_PISTOL,
	L4D2_ACT_IDLE_ANGRY_SHOTGUN,
	L4D2_ACT_IDLE_STEALTH_PISTOL,
	L4D2_ACT_IDLE_PACKAGE,
	L4D2_ACT_WALK_PACKAGE,
	L4D2_ACT_IDLE_SUITCASE,
	L4D2_ACT_WALK_SUITCASE,
	L4D2_ACT_IDLE_SMG1_RELAXED,
	L4D2_ACT_IDLE_SMG1_STIMULATED,
	L4D2_ACT_WALK_RIFLE_RELAXED,
	L4D2_ACT_RUN_RIFLE_RELAXED,
	L4D2_ACT_WALK_RIFLE_STIMULATED,
	L4D2_ACT_RUN_RIFLE_STIMULATED,
	L4D2_ACT_IDLE_AIM_RIFLE_STIMULATED,
	L4D2_ACT_WALK_AIM_RIFLE_STIMULATED,
	L4D2_ACT_RUN_AIM_RIFLE_STIMULATED,
	L4D2_ACT_IDLE_SHOTGUN_RELAXED,
	L4D2_ACT_IDLE_SHOTGUN_STIMULATED,
	L4D2_ACT_IDLE_SHOTGUN_AGITATED,
	L4D2_ACT_WALK_ANGRY,
	L4D2_ACT_POLICE_HARASS1,
	L4D2_ACT_POLICE_HARASS2,
	L4D2_ACT_IDLE_MANNEDGUN,
	L4D2_ACT_IDLE_MELEE,
	L4D2_ACT_IDLE_ANGRY_MELEE,
	L4D2_ACT_IDLE_RPG_RELAXED,
	L4D2_ACT_IDLE_RPG,
	L4D2_ACT_IDLE_ANGRY_RPG,
	L4D2_ACT_COVER_LOW_RPG,
	L4D2_ACT_WALK_RPG,
	L4D2_ACT_RUN_RPG,
	L4D2_ACT_WALK_CROUCH_RPG,
	L4D2_ACT_RUN_CROUCH_RPG,
	L4D2_ACT_WALK_RPG_RELAXED,
	L4D2_ACT_RUN_RPG_RELAXED,
	L4D2_ACT_WALK_RIFLE,
	L4D2_ACT_WALK_AIM_RIFLE,
	L4D2_ACT_WALK_CROUCH_RIFLE,
	L4D2_ACT_WALK_CROUCH_AIM_RIFLE,
	L4D2_ACT_RUN_RIFLE,
	L4D2_ACT_RUN_AIM_RIFLE,
	L4D2_ACT_RUN_CROUCH_RIFLE,
	L4D2_ACT_RUN_CROUCH_AIM_RIFLE,
	L4D2_ACT_RUN_STEALTH_PISTOL,
	L4D2_ACT_WALK_AIM_SHOTGUN,
	L4D2_ACT_RUN_AIM_SHOTGUN,
	L4D2_ACT_WALK_PISTOL,
	L4D2_ACT_RUN_PISTOL,
	L4D2_ACT_WALK_AIM_PISTOL,
	L4D2_ACT_RUN_AIM_PISTOL,
	L4D2_ACT_WALK_STEALTH_PISTOL,
	L4D2_ACT_WALK_AIM_STEALTH_PISTOL,
	L4D2_ACT_RUN_AIM_STEALTH_PISTOL,
	L4D2_ACT_RELOAD_PISTOL,
	L4D2_ACT_RELOAD_PISTOL_LOW,
	L4D2_ACT_RELOAD_SMG1,
	L4D2_ACT_RELOAD_SMG1_LOW,
	L4D2_ACT_RELOAD_SHOTGUN,
	L4D2_ACT_RELOAD_SHOTGUN_LOW,
	L4D2_ACT_GESTURE_RELOAD,
	L4D2_ACT_GESTURE_RELOAD_PISTOL,
	L4D2_ACT_GESTURE_RELOAD_SMG1,
	L4D2_ACT_GESTURE_RELOAD_SHOTGUN,
	L4D2_ACT_BUSY_LEAN_LEFT,
	L4D2_ACT_BUSY_LEAN_LEFT_ENTRY,
	L4D2_ACT_BUSY_LEAN_LEFT_EXIT,
	L4D2_ACT_BUSY_LEAN_BACK,
	L4D2_ACT_BUSY_LEAN_BACK_ENTRY,
	L4D2_ACT_BUSY_LEAN_BACK_EXIT,
	L4D2_ACT_BUSY_SIT_GROUND,
	L4D2_ACT_BUSY_SIT_GROUND_ENTRY,
	L4D2_ACT_BUSY_SIT_GROUND_EXIT,
	L4D2_ACT_BUSY_SIT_CHAIR,
	L4D2_ACT_BUSY_SIT_CHAIR_ENTRY,
	L4D2_ACT_BUSY_SIT_CHAIR_EXIT,
	L4D2_ACT_BUSY_STAND,
	L4D2_ACT_BUSY_QUEUE,
	L4D2_ACT_DUCK_DODGE,
	L4D2_ACT_DIE_BARNACLE_SWALLOW,
	L4D2_ACT_GESTURE_BARNACLE_STRANGLE,
	L4D2_ACT_PHYSCANNON_DETACH,
	L4D2_ACT_PHYSCANNON_ANIMATE,
	L4D2_ACT_PHYSCANNON_ANIMATE_PRE,
	L4D2_ACT_PHYSCANNON_ANIMATE_POST,
	L4D2_ACT_DIE_FRONTSIDE,
	L4D2_ACT_DIE_RIGHTSIDE,
	L4D2_ACT_DIE_BACKSIDE,
	L4D2_ACT_DIE_LEFTSIDE,
	L4D2_ACT_DIE_CROUCH_FRONTSIDE,
	L4D2_ACT_DIE_CROUCH_RIGHTSIDE,
	L4D2_ACT_DIE_CROUCH_BACKSIDE,
	L4D2_ACT_DIE_CROUCH_LEFTSIDE,
	L4D2_ACT_DIE_INCAP,
	L4D2_ACT_DIE_STANDING,
	L4D2_ACT_OPEN_DOOR,
	L4D2_ACT_DI_ALYX_ZOMBIE_MELEE,
	L4D2_ACT_DI_ALYX_ZOMBIE_TORSO_MELEE,
	L4D2_ACT_DI_ALYX_HEADCRAB_MELEE,
	L4D2_ACT_DI_ALYX_ANTLION,
	L4D2_ACT_DI_ALYX_ZOMBIE_SHOTGUN64,
	L4D2_ACT_DI_ALYX_ZOMBIE_SHOTGUN26,
	L4D2_ACT_READINESS_RELAXED_TO_STIMULATED,
	L4D2_ACT_READINESS_RELAXED_TO_STIMULATED_WALK,
	L4D2_ACT_READINESS_AGITATED_TO_STIMULATED,
	L4D2_ACT_READINESS_STIMULATED_TO_RELAXED,
	L4D2_ACT_READINESS_PISTOL_RELAXED_TO_STIMULATED,
	L4D2_ACT_READINESS_PISTOL_RELAXED_TO_STIMULATED_WALK,
	L4D2_ACT_READINESS_PISTOL_AGITATED_TO_STIMULATED,
	L4D2_ACT_READINESS_PISTOL_STIMULATED_TO_RELAXED,
	L4D2_ACT_IDLE_CARRY,
	L4D2_ACT_WALK_CARRY,
	L4D2_ACT_STARTDYING,
	L4D2_ACT_DYINGLOOP,
	L4D2_ACT_DYINGTODEAD,
	L4D2_ACT_RIDE_MANNED_GUN,
	L4D2_ACT_VM_SPRINT_ENTER,
	L4D2_ACT_VM_SPRINT_IDLE,
	L4D2_ACT_VM_SPRINT_LEAVE,
	L4D2_ACT_FIRE_START,
	L4D2_ACT_FIRE_LOOP,
	L4D2_ACT_FIRE_END,
	L4D2_ACT_CROUCHING_GRENADEIDLE,
	L4D2_ACT_CROUCHING_GRENADEREADY,
	L4D2_ACT_CROUCHING_PRIMARYATTACK,
	L4D2_ACT_OVERLAY_GRENADEIDLE,
	L4D2_ACT_OVERLAY_GRENADEREADY,
	L4D2_ACT_OVERLAY_PRIMARYATTACK,
	L4D2_ACT_OVERLAY_SHIELD_UP,
	L4D2_ACT_OVERLAY_SHIELD_DOWN,
	L4D2_ACT_OVERLAY_SHIELD_UP_IDLE,
	L4D2_ACT_OVERLAY_SHIELD_ATTACK,
	L4D2_ACT_OVERLAY_SHIELD_KNOCKBACK,
	L4D2_ACT_SHIELD_UP,
	L4D2_ACT_SHIELD_DOWN,
	L4D2_ACT_SHIELD_UP_IDLE,
	L4D2_ACT_SHIELD_ATTACK,
	L4D2_ACT_SHIELD_KNOCKBACK,
	L4D2_ACT_CROUCHING_SHIELD_UP,
	L4D2_ACT_CROUCHING_SHIELD_DOWN,
	L4D2_ACT_CROUCHING_SHIELD_UP_IDLE,
	L4D2_ACT_CROUCHING_SHIELD_ATTACK,
	L4D2_ACT_CROUCHING_SHIELD_KNOCKBACK,
	L4D2_ACT_TURNRIGHT45,
	L4D2_ACT_TURNLEFT45,
	L4D2_ACT_TURN,
	L4D2_ACT_OBJ_ASSEMBLING,
	L4D2_ACT_OBJ_DISMANTLING,
	L4D2_ACT_OBJ_STARTUP,
	L4D2_ACT_OBJ_RUNNING,
	L4D2_ACT_OBJ_IDLE,
	L4D2_ACT_OBJ_PLACING,
	L4D2_ACT_OBJ_DETERIORATING,
	L4D2_ACT_OBJ_UPGRADING,
	L4D2_ACT_DEPLOY,
	L4D2_ACT_DEPLOY_IDLE,
	L4D2_ACT_UNDEPLOY,
	L4D2_ACT_CROSSBOW_DRAW_UNLOADED,
	L4D2_ACT_GAUSS_SPINUP,
	L4D2_ACT_GAUSS_SPINCYCLE,
	L4D2_ACT_VM_PRIMARYATTACK_SILENCED,
	L4D2_ACT_VM_RELOAD_SILENCED,
	L4D2_ACT_VM_DRYFIRE_SILENCED,
	L4D2_ACT_VM_IDLE_SILENCED,
	L4D2_ACT_VM_DRAW_SILENCED,
	L4D2_ACT_VM_IDLE_EMPTY_LEFT,
	L4D2_ACT_VM_DRYFIRE_LEFT,
	L4D2_ACT_VM_IS_DRAW,
	L4D2_ACT_VM_IS_HOLSTER,
	L4D2_ACT_VM_IS_IDLE,
	L4D2_ACT_VM_IS_PRIMARYATTACK,
	L4D2_ACT_PLAYER_IDLE_FIRE,
	L4D2_ACT_PLAYER_CROUCH_FIRE,
	L4D2_ACT_PLAYER_CROUCH_WALK_FIRE,
	L4D2_ACT_PLAYER_WALK_FIRE,
	L4D2_ACT_PLAYER_RUN_FIRE,
	L4D2_ACT_IDLETORUN,
	L4D2_ACT_RUNTOIDLE,
	L4D2_ACT_VM_DRAW_DEPLOYED,
	L4D2_ACT_HL2MP_IDLE_MELEE,
	L4D2_ACT_HL2MP_RUN_MELEE,
	L4D2_ACT_HL2MP_IDLE_CROUCH_MELEE,
	L4D2_ACT_HL2MP_WALK_CROUCH_MELEE,
	L4D2_ACT_HL2MP_GESTURE_RANGE_ATTACK_MELEE,
	L4D2_ACT_HL2MP_GESTURE_RELOAD_MELEE,
	L4D2_ACT_HL2MP_JUMP_MELEE,
	L4D2_ACT_VM_SHRIEK,
	L4D2_ACT_VM_VOMIT,
	L4D2_ACT_VM_COUGH,
	L4D2_ACT_VM_LUNGE,
	L4D2_ACT_VM_PUMMEL_ATTACK,
	L4D2_ACT_VM_IDLE2REV,
	L4D2_ACT_VM_REV2IDLE,
	L4D2_ACT_IDLE_INJURED,
	L4D2_ACT_IDLE_INCAP,
	L4D2_ACT_WALK_INJURED,
	L4D2_ACT_RUN_INJURED,
	L4D2_ACT_IDLE_CALM,
	L4D2_ACT_WALK_CALM,
	L4D2_ACT_RUN_CALM,
	L4D2_ACT_JUMP_SHOTGUN,
	L4D2_ACT_JUMP_RIFLE,
	L4D2_ACT_JUMP_SMG,
	L4D2_ACT_JUMP_PISTOL,
	L4D2_ACT_JUMP_DUAL_PISTOL,
	L4D2_ACT_JUMP_ITEM,
	L4D2_ACT_TERROR_HIT_BY_TANKPUNCH,
	L4D2_ACT_TERROR_IDLE_FALL_FROM_TANKPUNCH,
	L4D2_ACT_TERROR_TANKPUNCH_LAND,
	L4D2_ACT_TERROR_HIT_BY_CHARGER,
	L4D2_ACT_TERROR_IDLE_FALL_FROM_CHARGERHIT,
	L4D2_ACT_TERROR_CHARGERHIT_LAND_SLOW,
	L4D2_ACT_TERROR_CHARGER_PUMMELED,
	L4D2_ACT_TERROR_CARRIED,
	L4D2_ACT_TERROR_SLAMMED_WALL,
	L4D2_ACT_TERROR_SLAMMED_GROUND,
	L4D2_ACT_TERROR_CHARGER_POUNDED_NORTH,
	L4D2_ACT_TERROR_CHARGER_POUNDED_UP,
	L4D2_ACT_TERROR_CHARGER_POUNDED_INCAP,
	L4D2_ACT_TERROR_CHARGER_POUNDED_DOWN,
	L4D2_ACT_TERROR_CHARGER_PUMMEL,
	L4D2_ACT_TERROR_SLAM_GROUND,
	L4D2_ACT_TERROR_CHARGER_POUND_NORTH,
	L4D2_ACT_TERROR_CHARGER_POUND_UP,
	L4D2_ACT_TERROR_CHARGER_POUND_DOWN_PRODUCER,
	L4D2_ACT_TERROR_CHARGER_POUND_DOWN_COACH,
	L4D2_ACT_TERROR_CHARGER_POUND_DOWN,
	L4D2_ACT_TERROR_PUMMELED_LOOP,
	L4D2_ACT_TERROR_SPITTER_SPIT,
	L4D2_ACT_TERROR_HEAL_SELF,
	L4D2_ACT_TERROR_HEAL_FRIEND,
	L4D2_ACT_TERROR_HEAL_INCAPACITATED,
	L4D2_ACT_TERROR_CROUCH_HEAL_SELF,
	L4D2_ACT_TERROR_CROUCH_HEAL_FRIEND,
	L4D2_ACT_TERROR_CROUCH_HEAL_INCAPACITATED,
	L4D2_ACT_TERROR_HEAL_INCAPACITATED_ABOVE,
	L4D2_ACT_TERROR_CROUCH_HEAL_INCAPACITATED_ABOVE,
	L4D2_ACT_TERROR_USE_PILLS,
	L4D2_ACT_TERROR_USE_ADRENALINE,
	L4D2_ACT_TERROR_USE_ADRENALINE_CROUCHING,
	L4D2_ACT_TERROR_USE_DEFIBRILLATOR,
	L4D2_ACT_TERROR_CROUCH_USE_DEFIBRILLATOR,
	L4D2_ACT_TERROR_REVIVE_FROM_DEATH,
	L4D2_ACT_TERROR_DEFIBRILLATOR_SHOCK,
	L4D2_ACT_TERROR_USE_UPGRADEPACK,
	L4D2_ACT_TERROR_CROUCH_USE_UPGRADEPACK,
	L4D2_ACT_TERROR_USE_GAS_CAN,
	L4D2_ACT_TERROR_USE_COLA,
	L4D2_ACT_TERROR_IDLE_NEUTRAL,
	L4D2_ACT_TERROR_IDLE_ALERT,
	L4D2_ACT_TERROR_IDLE_INTENSE,
	L4D2_ACT_TERROR_IDLE_ALERT_INJURED_AHEAD,
	L4D2_ACT_TERROR_IDLE_ALERT_AHEAD,
	L4D2_ACT_TERROR_IDLE_ALERT_INJURED_BEHIND,
	L4D2_ACT_TERROR_IDLE_ALERT_BEHIND,
	L4D2_ACT_TERROR_IDLE_ALERT_INJURED_LEFT,
	L4D2_ACT_TERROR_IDLE_ALERT_LEFT,
	L4D2_ACT_TERROR_IDLE_ALERT_INJURED_RIGHT,
	L4D2_ACT_TERROR_IDLE_ALERT_RIGHT,
	L4D2_ACT_TERROR_IDLE_ACQUIRE,
	L4D2_ACT_TERROR_LEAN_FORWARD_IDLE,
	L4D2_ACT_TERROR_LEAN_BACKWARD_IDLE,
	L4D2_ACT_TERROR_LEAN_LEFTWARD_IDLE,
	L4D2_ACT_TERROR_LEAN_RIGHTWARD_IDLE,
	L4D2_ACT_TERROR_FIDGET,
	L4D2_ACT_TERROR_NEUTRAL_TO_ALERT,
	L4D2_ACT_TERROR_ALERT_TO_NEUTRAL,
	L4D2_ACT_TERROR_FACE_LEFT_NEUTRAL,
	L4D2_ACT_TERROR_FACE_LEFT_ALERT,
	L4D2_ACT_TERROR_FACE_LEFT_INTENSE,
	L4D2_ACT_TERROR_FACE_RIGHT_NEUTRAL,
	L4D2_ACT_TERROR_FACE_RIGHT_ALERT,
	L4D2_ACT_TERROR_FACE_RIGHT_INTENSE,
	L4D2_ACT_TERROR_ABOUT_FACE_NEUTRAL,
	L4D2_ACT_TERROR_ABOUT_FACE_ALERT,
	L4D2_ACT_TERROR_ABOUT_FACE_INTENSE,
	L4D2_ACT_TERROR_SHAMBLE,
	L4D2_ACT_TERROR_WALK_NEUTRAL,
	L4D2_ACT_TERROR_WALK_ALERT,
	L4D2_ACT_TERROR_WALK_INTENSE,
	L4D2_ACT_TERROR_RUN_NEUTRAL,
	L4D2_ACT_TERROR_RUN_ALERT,
	L4D2_ACT_TERROR_RUN_INTENSE,
	L4D2_ACT_TERROR_RUN_ON_FIRE_INTENSE,
	L4D2_ACT_TERROR_RUN_INTENSE_TO_STAND_ALERT,
	L4D2_ACT_TERROR_CROUCH_IDLE_NEUTRAL,
	L4D2_ACT_TERROR_CROUCH_IDLE_ALERT,
	L4D2_ACT_TERROR_CROUCH_IDLE_INTENSE,
	L4D2_ACT_TERROR_CROUCH_WALK_NEUTRAL,
	L4D2_ACT_TERROR_CROUCH_WALK_ALERT,
	L4D2_ACT_TERROR_CROUCH_WALK_INTENSE,
	L4D2_ACT_TERROR_CROUCH_RUN_NEUTRAL,
	L4D2_ACT_TERROR_CROUCH_RUN_ALERT,
	L4D2_ACT_TERROR_CROUCH_RUN_INTENSE,
	L4D2_ACT_TERROR_CRAWL_RUN,
	L4D2_ACT_TERROR_IDLE_ON_FIRE,
	L4D2_ACT_TERROR_WALK_ON_FIRE,
	L4D2_ACT_TERROR_RUN_ON_FIRE,
	L4D2_ACT_TERROR_ATTACK,
	L4D2_ACT_TERROR_ATTACK_CONTINUOUSLY,
	L4D2_ACT_TERROR_ATTACK_LOW,
	L4D2_ACT_TERROR_ATTACK_LOW_CONTINUOUSLY,
	L4D2_ACT_TERROR_ATTACK_DOOR,
	L4D2_ACT_TERROR_ATTACK_DOOR_CONTINUOUSLY,
	L4D2_ACT_TERROR_UNABLE_TO_REACH_TARGET,
	L4D2_ACT_TERROR_REACH_THROUGH_DOOR,
	L4D2_ACT_TERROR_SHOVED_FORWARD,
	L4D2_ACT_TERROR_SHOVED_BACKWARD,
	L4D2_ACT_TERROR_SHOVED_LEFTWARD,
	L4D2_ACT_TERROR_SHOVED_RIGHTWARD,
	L4D2_ACT_TERROR_SHOVED_FORWARD_CHAINSAW,
	L4D2_ACT_TERROR_SHOVED_BACKWARD_CHAINSAW,
	L4D2_ACT_TERROR_SHOVED_LEFTWARD_CHAINSAW,
	L4D2_ACT_TERROR_SHOVED_RIGHTWARD_CHAINSAW,
	L4D2_ACT_TERROR_SHOVED_FORWARD_BAT,
	L4D2_ACT_TERROR_SHOVED_BACKWARD_BAT,
	L4D2_ACT_TERROR_SHOVED_LEFTWARD_BAT,
	L4D2_ACT_TERROR_SHOVED_RIGHTWARD_BAT,
	L4D2_ACT_TERROR_SHOVED_FORWARD_MELEE,
	L4D2_ACT_TERROR_SHOVED_BACKWARD_MELEE,
	L4D2_ACT_TERROR_SHOVED_LEFTWARD_MELEE,
	L4D2_ACT_TERROR_SHOVED_RIGHTWARD_MELEE,
	L4D2_ACT_TERROR_SHOVED_BACKWARD_INTO_WALL,
	L4D2_ACT_TERROR_SHOVED_FORWARD_INTO_WALL,
	L4D2_ACT_TERROR_SHOVED_LEFTWARD_INTO_WALL,
	L4D2_ACT_TERROR_SHOVED_RIGHTWARD_INTO_WALL,
	L4D2_ACT_TERROR_SHOVED_BACKWARD_FROM_SIT,
	L4D2_ACT_TERROR_SHOVED_RIGHTWARD_FROM_SIT,
	L4D2_ACT_TERROR_SHOVED_LEFTWARD_FROM_SIT,
	L4D2_ACT_TERROR_TUGGED_FORWARD,
	L4D2_ACT_TERROR_TUGGED_BACKWARD,
	L4D2_ACT_TERROR_TUGGED_LEFTWARD,
	L4D2_ACT_TERROR_TUGGED_RIGHTWARD,
	L4D2_ACT_TERROR_SIT_IDLE,
	L4D2_ACT_TERROR_SIT_FROM_STAND,
	L4D2_ACT_TERROR_SIT_TO_STAND,
	L4D2_ACT_TERROR_SIT_TO_STAND_ALERT,
	L4D2_ACT_TERROR_SIT_TO_LIE,
	L4D2_ACT_TERROR_LIE_IDLE,
	L4D2_ACT_TERROR_LIE_FROM_STAND,
	L4D2_ACT_TERROR_LIE_TO_STAND,
	L4D2_ACT_TERROR_LIE_TO_STAND_ALERT,
	L4D2_ACT_TERROR_LIE_TO_SIT,
	L4D2_ACT_TERROR_JUMP,
	L4D2_ACT_TERROR_JUMP_UP_TO_LEDGE,
	L4D2_ACT_TERROR_JUMP_DOWN_FROM_LEDGE,
	L4D2_ACT_TERROR_JUMP_OVER_GAP,
	L4D2_ACT_TERROR_JUMP_LANDING,
	L4D2_ACT_TERROR_JUMP_LANDING_HARD,
	L4D2_ACT_TERROR_JUMP_LANDING_NEUTRAL,
	L4D2_ACT_TERROR_JUMP_LANDING_HARD_NEUTRAL,
	L4D2_ACT_TERROR_FALL,
	L4D2_ACT_TERROR_DIE_FROM_STAND,
	L4D2_ACT_TERROR_DIE_FROM_CROUCH,
	L4D2_ACT_TERROR_DIE_WHILE_RUNNING,
	L4D2_ACT_TERROR_DIE_BACKWARD_FROM_SHOTGUN,
	L4D2_ACT_TERROR_DIE_FORWARD_FROM_SHOTGUN,
	L4D2_ACT_TERROR_DIE_LEFTWARD_FROM_SHOTGUN,
	L4D2_ACT_TERROR_DIE_RIGHTWARD_FROM_SHOTGUN,
	L4D2_ACT_BLUDGEON_DEATH_BACK,
	L4D2_ACT_BLUDGEON_DEATH_FORWARD,
	L4D2_ACT_BLUDGEON_DEATH_LEFT,
	L4D2_ACT_BLUDGEON_DEATH_RIGHT,
	L4D2_ACT_SLICING_DEATH_BACK,
	L4D2_ACT_SLICING_DEATH_FORWARD,
	L4D2_ACT_SLICING_DEATH_LEFT,
	L4D2_ACT_SLICING_DEATH_RIGHT,
	L4D2_ACT_TERROR_WITCH_ANGRY,
	L4D2_ACT_TERROR_WITCH_ANGRY_HIGH,
	L4D2_ACT_TERROR_WITCH_IDLE,
	L4D2_ACT_TERROR_WITCH_IDLE_PRE_RETREAT,
	L4D2_ACT_TERROR_WITCH_RETREAT,
	L4D2_ACT_TERROR_WITCH_KILL_DISPLAY,
	L4D2_ACT_TERROR_WITCH_KILL_LOOP,
	L4D2_ACT_TERROR_WITCH_WANDER_WALK,
	L4D2_ACT_TERROR_WITCH_WANDER_IDLE,
	L4D2_ACT_TERROR_WITCH_WANDER_ACQUIRE,
	L4D2_ACT_TERROR_TUG,
	L4D2_ACT_TERROR_FLINCH,
	L4D2_ACT_TERROR_FLINCH_LEDGE,
	L4D2_ACT_SECONDARYATTACK,
	L4D2_ACT_TERROR_INCAP_CRAWL,
	L4D2_ACT_TERROR_INCAP_TO_STAND,
	L4D2_ACT_TERROR_INCAP_TO_CROUCH,
	L4D2_ACT_TERROR_ITEM_PICKUP,
	L4D2_ACT_IDLE_INCAP_PISTOL,
	L4D2_ACT_IDLE_INCAP_ELITES,
	L4D2_ACT_FALL,
	L4D2_ACT_CALL_FOR_RESCUE,
	L4D2_ACT_PUSH,
	L4D2_ACT_IDLE_MINIGUN,
	L4D2_ACT_PRIMARYATTACK_MINIGUN,
	L4D2_ACT_GEST_HEAD_NOD,
	L4D2_ACT_GEST_HEAD_YES,
	L4D2_ACT_GEST_HEAD_NO,
	L4D2_ACT_TERROR_LEDGE_CLIMB,
	L4D2_ACT_TERROR_LEDGE_CLIMB_TO_CROUCH,
	L4D2_ACT_TERROR_FALL_GRAB_LEDGE,
	L4D2_ACT_TERROR_LEDGE_HANG_FIRM,
	L4D2_ACT_TERROR_LEDGE_HANG_WEAK,
	L4D2_ACT_TERROR_LEDGE_HANG_DANGLE,
	L4D2_ACT_TERROR_IDLE_LADDER,
	L4D2_ACT_TERROR_LADDER_DISMOUNT,
	L4D2_ACT_TERROR_CLIMB_24_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_36_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_38_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_48_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_50_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_60_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_70_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_72_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_84_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_96_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_108_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_115_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_120_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_130_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_132_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_144_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_150_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_156_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_166_FROM_STAND,
	L4D2_ACT_TERROR_CLIMB_168_FROM_STAND,
	L4D2_ACT_TERROR_HUNTER_LUNGE_INTO_SURVIVOR,
	L4D2_ACT_TERROR_HUNTER_LUNGE,
	L4D2_ACT_TERROR_HUNTER_LUNGE_WHILE_RUNNING,
	L4D2_ACT_TERROR_HUNTER_LANDING_HARD,
	L4D2_ACT_TERROR_HUNTER_LUNGE_OFF_WALL_SPIN_RIGHT,
	L4D2_ACT_TERROR_HUNTER_LUNGE_OFF_WALL_SPIN_LEFT,
	L4D2_ACT_TERROR_HUNTER_LUNGE_OFF_WALL_BACK,
	L4D2_ACT_TERROR_HUNTER_LUNGE_IDLE,
	L4D2_ACT_TERROR_HUNTER_LUNGE_ONTO_WALL,
	L4D2_ACT_TERROR_HUNTER_POUNCE,
	L4D2_ACT_TERROR_HUNTER_POUNCE_IDLE,
	L4D2_ACT_TERROR_SMOKER_PREPARE_TONGUE_LAUNCH,
	L4D2_ACT_TERROR_SMOKER_SENDING_OUT_TONGUE,
	L4D2_ACT_TERROR_SMOKER_SENDING_OUT_TONGUE_IDLE,
	L4D2_ACT_TERROR_SMOKER_REELING_IN_TONGUE,
	L4D2_ACT_TERROR_SMOKER_REELING_IN_TONGUE_IDLE,
	L4D2_ACT_TERROR_SMOKER_CRITICAL_ATTACK,
	L4D2_ACT_TERROR_SMOKER_CRITICAL_ATTACK_IDLE,
	L4D2_ACT_TERROR_SMOKER_END_TONGUE_ATTACK,
	L4D2_ACT_TERROR_RUN_LEAKER,
	L4D2_ACT_TERROR_CROUCH_LEAKER,
	L4D2_ACT_TERROR_CROUCHIDLE_LEAKER,
	L4D2_ACT_TERROR_GUARD,
	L4D2_ACT_HULK_THROW,
	L4D2_ACT_TANK_OVERHEAD_THROW,
	L4D2_ACT_HULK_ATTACK_LOW,
	L4D2_ACT_TERROR_HUNTER_POUNCE_MELEE,
	L4D2_ACT_TERROR_HUNTER_POUNCE_KNOCKOFF_L,
	L4D2_ACT_TERROR_HUNTER_POUNCE_KNOCKOFF_R,
	L4D2_ACT_TERROR_HUNTER_POUNCE_KNOCKOFF_BACKWARD,
	L4D2_ACT_TERROR_HUNTER_POUNCE_KNOCKOFF_FORWARD,
	L4D2_ACT_TERROR_CHARGER_CHARGE,
	L4D2_ACT_IDLE_POUNCED,
	L4D2_ACT_TERROR_POUNCED_TO_STAND,
	L4D2_ACT_TERROR_INCAP_FROM_POUNCE,
	L4D2_ACT_TERROR_INCAP_FROM_TONGUE,
	L4D2_ACT_TERROR_IDLE_FALL_FROM_TONGUE,
	L4D2_ACT_TERROR_HANGING_FROM_TONGUE,
	L4D2_ACT_TERROR_HANGING_FROM_TONGUE_MALE,
	L4D2_ACT_TERROR_HANGING_FROM_TONGUE_FEMALE,
	L4D2_ACT_TERROR_STANDING_CHOKE_FROM_TONGUE,
	L4D2_ACT_TERROR_DRAGGING_FROM_TONGUE,
	L4D2_ACT_TERROR_DRAGGING_FROM_TONGUE_MALE,
	L4D2_ACT_TERROR_DRAGGING_FROM_TONGUE_FEMALE,
	L4D2_ACT_TERROR_DRAGGING_FROM_TONGUE_DEPLOY,
	L4D2_ACT_TERROR_CHOKING_TONGUE_GROUND,
	L4D2_ACT_TERROR_CHOKING_TONGUE_GROUND_MALE,
	L4D2_ACT_TERROR_CHOKING_TONGUE_GROUND_FEMALE,
	L4D2_ACT_TERROR_INCAP_FROM_TONGUE_GERMANY,
	L4D2_ACT_TERROR_HANGING_FROM_TONGUE_GERMANY,
	L4D2_ACT_TERROR_IDLE_FALL_FROM_TONGUE_GERMANY,
	L4D2_ACT_TERROR_STANDING_CHOKE_FROM_TONGUE_GERMANY,
	L4D2_ACT_TERROR_ATTACK_MOVING,
	L4D2_ACT_TERROR_TANKROCK_TO_STAND,
	L4D2_ACT_TERROR_HULK_VICTORY,
	L4D2_ACT_TERROR_HULK_VICTORY_B,
	L4D2_ACT_TERROR_RAGE_AT_ENEMY,
	L4D2_ACT_TERROR_RAGE_AT_KNOCKDOWN,
	L4D2_ACT_TERROR_SMASH_LEFT,
	L4D2_ACT_TERROR_SMASH_RIGHT,
	L4D2_ACT_TERROR_JOCKEY_RIDE,
	L4D2_ACT_TERROR_JOCKEY_RIDDEN,
	L4D2_ACT_VM_LUNGE_LAYER,
	L4D2_ACT_VM_LUNGE_PUSH,
	L4D2_ACT_VM_LUNGE_PUSH_LAYER,
	L4D2_ACT_VM_LUNGE_OFFWALL,
	L4D2_ACT_VM_LUNGE_OFFWALL_LAYER,
	L4D2_ACT_VM_LUNGE_POUNCE,
	L4D2_ACT_VM_LUNGE_POUNCE_LAYER,
	L4D2_ACT_VM_POUNCE,
	L4D2_ACT_VM_POUNCE_LAYER,
	L4D2_ACT_VM_VOMIT_LAYER,
	L4D2_ACT_VM_TONGUE,
	L4D2_ACT_VM_TONGUE_LAYER,
	L4D2_ACT_VM_BITE,
	L4D2_ACT_VM_BITE_LAYER,
	L4D2_ACT_VM_COUGH_LAYER,
	L4D2_ACT_VM_FIDGET_LAYER,
	L4D2_ACT_DOOR_OPEN,
	L4D2_ACT_DOOR_OPENFAIL,
	L4D2_ACT_DOOR_ANIMTOLOCK,
	L4D2_ACT_DOOR_ANIMTOUNLOCK,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_LOOP,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_LAYER,
	L4D2_ACT_VM_HELPINGHAND_EXTEND,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_LAYER,
	L4D2_ACT_VM_HELPINGHAND_LOOP,
	L4D2_ACT_VM_HELPINGHAND_LOOP_LAYER,
	L4D2_ACT_VM_HELPINGHAND_RETRACT,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_PISTOL,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_PISTOL_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_PISTOL,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_PISTOL_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_PISTOL,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_PISTOL_LAYER,
	L4D2_ACT_VM_HELPINGHAND_LOOP_PISTOL,
	L4D2_ACT_VM_HELPINGHAND_LOOP_PISTOL_LAYER,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_PISTOL,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_PISTOL_LAYER,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_PISTOL,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_PISTOL_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_DUAL_PISTOL,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_DUAL_PISTOL,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_DUAL_PISTOL,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_HELPINGHAND_LOOP_DUAL_PISTOL,
	L4D2_ACT_VM_HELPINGHAND_LOOP_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_DUAL_PISTOL,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_DUAL_PISTOL,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_RIFLE,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_RIFLE_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_RIFLE,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_RIFLE_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_RIFLE,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_RIFLE_LAYER,
	L4D2_ACT_VM_HELPINGHAND_LOOP_RIFLE,
	L4D2_ACT_VM_HELPINGHAND_LOOP_RIFLE_LAYER,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_RIFLE,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_RIFLE_LAYER,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_RIFLE,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_RIFLE_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_SMG,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_SMG_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_SMG,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_SMG_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_SMG,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_SMG_LAYER,
	L4D2_ACT_VM_HELPINGHAND_LOOP_SMG,
	L4D2_ACT_VM_HELPINGHAND_LOOP_SMG_LAYER,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_SMG,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_SMG_LAYER,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_SMG,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_SMG_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_SNIPER,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_SNIPER_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_SNIPER,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_SNIPER_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_SNIPER,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_SNIPER_LAYER,
	L4D2_ACT_VM_HELPINGHAND_LOOP_SNIPER,
	L4D2_ACT_VM_HELPINGHAND_LOOP_SNIPER_LAYER,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_SNIPER,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_SNIPER_LAYER,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_SNIPER,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_SNIPER_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_PIPEBOMB,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_PIPEBOMB_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_PIPEBOMB,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_PIPEBOMB_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_PIPEBOMB,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_PIPEBOMB_LAYER,
	L4D2_ACT_VM_HELPINGHAND_LOOP_PIPEBOMB,
	L4D2_ACT_VM_HELPINGHAND_LOOP_PIPEBOMB_LAYER,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_PIPEBOMB,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_PIPEBOMB_LAYER,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_PIPEBOMB,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_PIPEBOMB_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_MOLOTOV,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_MOLOTOV_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_MOLOTOV,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_MOLOTOV_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_MOLOTOV,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_MOLOTOV_LAYER,
	L4D2_ACT_VM_HELPINGHAND_LOOP_MOLOTOV,
	L4D2_ACT_VM_HELPINGHAND_LOOP_MOLOTOV_LAYER,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_MOLOTOV,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_MOLOTOV_LAYER,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_MOLOTOV,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_MOLOTOV_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_MEDKIT,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_MEDKIT_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_MEDKIT,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_MEDKIT_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_MEDKIT,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_MEDKIT_LAYER,
	L4D2_ACT_VM_HELPINGHAND_LOOP_MEDKIT,
	L4D2_ACT_VM_HELPINGHAND_LOOP_MEDKIT_LAYER,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_MEDKIT,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_MEDKIT_LAYER,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_MEDKIT,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_MEDKIT_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_PAINPILLS,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_PAINPILLS_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_PAINPILLS,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_PAINPILLS_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_PAINPILLS,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_PAINPILLS_LAYER,
	L4D2_ACT_VM_HELPINGHAND_LOOP_PAINPILLS,
	L4D2_ACT_VM_HELPINGHAND_LOOP_PAINPILLS_LAYER,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_PAINPILLS,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_PAINPILLS_LAYER,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_PAINPILLS,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_PAINPILLS_LAYER,
	L4D2_ACT_VM_CANCEL,
	L4D2_ACT_VM_CANCEL_LAYER,
	L4D2_ACT_CROUCHIDLE_RIFLE,
	L4D2_ACT_IDLE_INJURED_RIFLE,
	L4D2_ACT_WALK_INJURED_RIFLE,
	L4D2_ACT_RUN_INJURED_RIFLE,
	L4D2_ACT_DEPLOY_RIFLE,
	L4D2_ACT_PRIMARYATTACK_RIFLE,
	L4D2_ACT_RELOAD_RIFLE,
	L4D2_ACT_IDLE_CALM_RIFLE,
	L4D2_ACT_WALK_CALM_RIFLE,
	L4D2_ACT_RUN_CALM_RIFLE,
	L4D2_ACT_MELEE_SWEEP_RIFLE_IDLE,
	L4D2_ACT_MELEE_SHOVE_RIFLE_IDLE,
	L4D2_ACT_MELEE_STRAIGHT_RIFLE_IDLE,
	L4D2_ACT_MELEE_SWEEP_RIFLE_RUN,
	L4D2_ACT_MELEE_SHOVE_RIFLE_RUN,
	L4D2_ACT_MELEE_STRAIGHT_RIFLE_RUN,
	L4D2_ACT_TERROR_PULLED_RUN_RIFLE,
	L4D2_ACT_MELEE_STOMP_RIFLE_IDLE,
	L4D2_ACT_MELEE_STOMP_RIFLE_WALK,
	L4D2_ACT_IDLE_SHOTGUN,
	L4D2_ACT_WALK_SHOTGUN,
	L4D2_ACT_RUN_SHOTGUN,
	L4D2_ACT_IDLE_CALM_SHOTGUN,
	L4D2_ACT_CROUCHIDLE_SHOTGUN,
	L4D2_ACT_RUN_CROUCH_SHOTGUN,
	L4D2_ACT_DEPLOY_SHOTGUN,
	L4D2_ACT_PRIMARYATTACK_SHOTGUN,
	L4D2_ACT_RELOAD_SHOTGUN_START,
	L4D2_ACT_RELOAD_SHOTGUN_LOOP,
	L4D2_ACT_RELOAD_SHOTGUN_END,
	L4D2_ACT_IDLE_PUMPSHOTGUN,
	L4D2_ACT_CROUCHIDLE_PUMPSHOTGUN,
	L4D2_ACT_IDLE_CALM_PUMPSHOTGUN,
	L4D2_ACT_IDLE_INJURED_PUMPSHOTGUN,
	L4D2_ACT_WALK_PUMPSHOTGUN,
	L4D2_ACT_RUN_CROUCH_PUMPSHOTGUN,
	L4D2_ACT_RUN_PUMPSHOTGUN,
	L4D2_ACT_WALK_CALM_PUMPSHOTGUN,
	L4D2_ACT_RUN_CALM_PUMPSHOTGUN,
	L4D2_ACT_WALK_INJURED_PUMPSHOTGUN,
	L4D2_ACT_RUN_INJURED_PUMPSHOTGUN,
	L4D2_ACT_PRIMARYATTACK_PUMPSHOTGUN,
	L4D2_ACT_RELOAD_PUMPSHOTGUN_START,
	L4D2_ACT_RELOAD_PUMPSHOTGUN_LOOP,
	L4D2_ACT_RELOAD_PUMPSHOTGUN_END,
	L4D2_ACT_RELOAD_GRENADE_LAUNCHER,
	L4D2_ACT_IDLE_GREN,
	L4D2_ACT_CROUCHIDLE_GREN,
	L4D2_ACT_IDLE_INJURED_GREN,
	L4D2_ACT_RUN_GREN,
	L4D2_ACT_WALK_GREN,
	L4D2_ACT_RUN_CROUCH_GREN,
	L4D2_ACT_DEPLOY_GREN,
	L4D2_ACT_WALK_INJURED_GREN,
	L4D2_ACT_RUN_INJURED_GREN,
	L4D2_ACT_PRIMARYATTACK_GREN1_IDLE,
	L4D2_ACT_PRIMARYATTACK_GREN2_IDLE,
	L4D2_ACT_PRIMARYATTACK_GREN1_RUN,
	L4D2_ACT_PRIMARYATTACK_GREN2_RUN,
	L4D2_ACT_IDLE_GREN_PULL_BACK,
	L4D2_ACT_CROUCHIDLE_GREN_PULL_BACK,
	L4D2_ACT_IDLE_INJURED_GREN_PULL_BACK,
	L4D2_ACT_RUN_GREN_PULL_BACK,
	L4D2_ACT_WALK_GREN_PULL_BACK,
	L4D2_ACT_RUN_CROUCH_GREN_PULL_BACK,
	L4D2_ACT_WALK_INJURED_GREN_PULL_BACK,
	L4D2_ACT_RUN_INJURED_GREN_PULL_BACK,
	L4D2_ACT_IDLE_SNIPER,
	L4D2_ACT_CROUCHIDLE_SNIPER,
	L4D2_ACT_IDLE_CALM_SNIPER,
	L4D2_ACT_IDLE_INJURED_SNIPER,
	L4D2_ACT_WALK_SNIPER,
	L4D2_ACT_RUN_CROUCH_SNIPER,
	L4D2_ACT_RUN_SNIPER,
	L4D2_ACT_WALK_CALM_SNIPER,
	L4D2_ACT_RUN_CALM_SNIPER,
	L4D2_ACT_WALK_INJURED_SNIPER,
	L4D2_ACT_RUN_INJURED_SNIPER,
	L4D2_ACT_IDLE_SNIPER_ZOOMED,
	L4D2_ACT_CROUCHIDLE_SNIPER_ZOOMED,
	L4D2_ACT_IDLE_INJURED_SNIPER_ZOOMED,
	L4D2_ACT_IDLE_SNIPER_MILITARY,
	L4D2_ACT_IDLE_SNIPER_MILITARYZOOMED,
	L4D2_ACT_CROUCHIDLE_SNIPER_MILITARY,
	L4D2_ACT_CROUCHIDLE_SNIPER_MILITARYZOOMED,
	L4D2_ACT_IDLE_CALM_SNIPER_MILITARY,
	L4D2_ACT_IDLE_INJURED_SNIPER_MILITARY,
	L4D2_ACT_IDLE_INJURED_SNIPER_MILITARYZOOMED,
	L4D2_ACT_RUN_CROUCH_SNIPER_MILITARY,
	L4D2_ACT_WALK_SNIPER_MILITARY,
	L4D2_ACT_RUN_SNIPER_MILITARY,
	L4D2_ACT_WALK_CALM_SNIPER_MILITARY,
	L4D2_ACT_RUN_CALM_SNIPER_MILITARY,
	L4D2_ACT_WALK_INJURED_SNIPER_MILITARY,
	L4D2_ACT_RUN_INJURED_SNIPER_MILITARY,
	L4D2_ACT_IDLE_SMG,
	L4D2_ACT_WALK_SMG,
	L4D2_ACT_RUN_SMG,
	L4D2_ACT_CROUCHIDLE_SMG,
	L4D2_ACT_RUN_CROUCH_SMG,
	L4D2_ACT_IDLE_INJURED_SMG,
	L4D2_ACT_WALK_INJURED_SMG,
	L4D2_ACT_RUN_INJURED_SMG,
	L4D2_ACT_IDLE_CALM_SMG,
	L4D2_ACT_WALK_CALM_SMG,
	L4D2_ACT_RUN_CALM_SMG,
	L4D2_ACT_PRIMARYATTACK_SMG,
	L4D2_ACT_RELOAD_SMG,
	L4D2_ACT_IDLE_FIRSTAIDKIT,
	L4D2_ACT_CROUCHIDLE_FIRSTAIDKIT,
	L4D2_ACT_IDLE_INJURED_FIRSTAIDKIT,
	L4D2_ACT_RUN_FIRSTAIDKIT,
	L4D2_ACT_WALK_FIRSTAIDKIT,
	L4D2_ACT_RUN_CROUCH_FIRSTAIDKIT,
	L4D2_ACT_WALK_INJURED_FIRSTAIDKIT,
	L4D2_ACT_RUN_INJURED_FIRSTAIDKIT,
	L4D2_ACT_MELEE_SWEEP_FIRSTAIDKIT,
	L4D2_ACT_MELEE_SWEEP_COLA,
	L4D2_ACT_MELEE_SWEEP_DEFIBRILLATOR,
	L4D2_ACT_MELEE_SWEEP_UPGRADE_PACK,
	L4D2_ACT_CROUCHIDLE_PISTOL,
	L4D2_ACT_RUN_CROUCH_PISTOL,
	L4D2_ACT_IDLE_INJURED_PISTOL,
	L4D2_ACT_WALK_INJURED_PISTOL,
	L4D2_ACT_RUN_INJURED_PISTOL,
	L4D2_ACT_DEPLOY_PISTOL,
	L4D2_ACT_PRIMARYATTACK_PISTOL,
	L4D2_ACT_IDLE_CALM_PISTOL,
	L4D2_ACT_WALK_CALM_PISTOL,
	L4D2_ACT_RUN_CALM_PISTOL,
	L4D2_ACT_IDLE_ELITES,
	L4D2_ACT_WALK_ELITES,
	L4D2_ACT_RUN_ELITES,
	L4D2_ACT_CROUCHIDLE_ELITES,
	L4D2_ACT_RUN_CROUCH_ELITES,
	L4D2_ACT_IDLE_INJURED_ELITES,
	L4D2_ACT_WALK_INJURED_ELITES,
	L4D2_ACT_RUN_INJURED_ELITES,
	L4D2_ACT_DEPLOY_ELITES,
	L4D2_ACT_PRIMARYATTACK_ELITES_R,
	L4D2_ACT_PRIMARYATTACK_ELITES_L,
	L4D2_ACT_IDLE_CALM_ELITES,
	L4D2_ACT_WALK_CALM_ELITES,
	L4D2_ACT_RUN_CALM_ELITES,
	L4D2_ACT_RELOAD_ELITES,
	L4D2_ACT_RELOAD_M4,
	L4D2_ACT_PRIMARYATTACK_XM1014,
	L4D2_ACT_PRIMARYATTACK_M3S90,
	L4D2_ACT_IDLE_GASCAN,
	L4D2_ACT_CROUCHIDLE_GASCAN,
	L4D2_ACT_IDLE_CALM_GASCAN,
	L4D2_ACT_IDLE_INJURED_GASCAN,
	L4D2_ACT_RUN_GASCAN,
	L4D2_ACT_WALK_GASCAN,
	L4D2_ACT_RUN_CROUCH_GASCAN,
	L4D2_ACT_RUN_INJURED_GASCAN,
	L4D2_ACT_JUMP_GASCAN,
	L4D2_ACT_MELEE_SWEEP_GASCAN,
	L4D2_ACT_IDLE_O2,
	L4D2_ACT_CROUCHIDLE_O2,
	L4D2_ACT_IDLE_CALM_O2,
	L4D2_ACT_IDLE_INJURED_O2,
	L4D2_ACT_RUN_O2,
	L4D2_ACT_WALK_O2,
	L4D2_ACT_RUN_CROUCH_O2,
	L4D2_ACT_RUN_INJURED_O2,
	L4D2_ACT_JUMP_O2,
	L4D2_ACT_MELEE_SWEEP_O2,
	L4D2_ACT_IDLE_GNOME,
	L4D2_ACT_CROUCHIDLE_GNOME,
	L4D2_ACT_IDLE_CALM_GNOME,
	L4D2_ACT_IDLE_INJURED_GNOME,
	L4D2_ACT_RUN_CROUCH_GNOME,
	L4D2_ACT_WALK_GNOME,
	L4D2_ACT_RUN_GNOME,
	L4D2_ACT_WALK_CALM_GNOME,
	L4D2_ACT_RUN_CALM_GNOME,
	L4D2_ACT_WALK_INJURED_GNOME,
	L4D2_ACT_RUN_INJURED_GNOME,
	L4D2_ACT_JUMP_GNOME,
	L4D2_ACT_MELEE_SWEEP_GNOME,
	L4D2_ACT_SHOOT_E2W_AXE,
	L4D2_ACT_SHOOT_E2W_IDLE_AXE,
	L4D2_ACT_SHOOT_W2E_AXE,
	L4D2_ACT_SHOOT_W2E_IDLE_AXE,
	L4D2_ACT_SHOOT_N2S_AXE,
	L4D2_ACT_SHOOT_N2S_IDLE_AXE,
	L4D2_ACT_SHOOT_S2N_AXE,
	L4D2_ACT_SHOOT_S2N_IDLE_AXE,
	L4D2_ACT_SHOOT_STRONG_AXE,
	L4D2_ACT_SHOOT_STRONG_IDLE_AXE,
	L4D2_ACT_SHOOT_SECONDARY_AXE,
	L4D2_ACT_SHOOT_SECONDARY_IDLE_AXE,
	L4D2_ACT_IDLE_AXE,
	L4D2_ACT_CROUCHIDLE_AXE,
	L4D2_ACT_IDLE_CALM_AXE,
	L4D2_ACT_IDLE_INJURED_AXE,
	L4D2_ACT_RUN_AXE,
	L4D2_ACT_WALK_AXE,
	L4D2_ACT_WALK_INJURED_AXE,
	L4D2_ACT_RUN_CROUCH_AXE,
	L4D2_ACT_RUN_INJURED_AXE,
	L4D2_ACT_JUMP_AXE,
	L4D2_ACT_SHOOT_E2W_BAT,
	L4D2_ACT_SHOOT_E2W_IDLE_BAT,
	L4D2_ACT_SHOOT_W2E_BAT,
	L4D2_ACT_SHOOT_W2E_IDLE_BAT,
	L4D2_ACT_SHOOT_N2S_BAT,
	L4D2_ACT_SHOOT_N2S_IDLE_BAT,
	L4D2_ACT_SHOOT_S2N_BAT,
	L4D2_ACT_SHOOT_S2N_IDLE_BAT,
	L4D2_ACT_SHOOT_STRONG_BAT,
	L4D2_ACT_SHOOT_STRONG_IDLE_BAT,
	L4D2_ACT_SHOOT_SECONDARY_BAT,
	L4D2_ACT_SHOOT_SECONDARY_IDLE_BAT,
	L4D2_ACT_IDLE_BAT,
	L4D2_ACT_CROUCHIDLE_BAT,
	L4D2_ACT_IDLE_CALM_BAT,
	L4D2_ACT_IDLE_INJURED_BAT,
	L4D2_ACT_RUN_BAT,
	L4D2_ACT_WALK_BAT,
	L4D2_ACT_WALK_INJURED_BAT,
	L4D2_ACT_RUN_CROUCH_BAT,
	L4D2_ACT_RUN_INJURED_BAT,
	L4D2_ACT_JUMP_BAT,
	L4D2_ACT_SHOOT_E2W_KATANA,
	L4D2_ACT_SHOOT_E2W_IDLE_KATANA,
	L4D2_ACT_SHOOT_W2E_KATANA,
	L4D2_ACT_SHOOT_W2E_IDLE_KATANA,
	L4D2_ACT_SHOOT_E2W_FRYINGPAN,
	L4D2_ACT_SHOOT_E2W_IDLE_FRYINGPAN,
	L4D2_ACT_SHOOT_W2E_FRYINGPAN,
	L4D2_ACT_SHOOT_W2E_IDLE_FRYINGPAN,
	L4D2_ACT_SHOOT_N2S_FRYINGPAN,
	L4D2_ACT_SHOOT_N2S_IDLE_FRYINGPAN,
	L4D2_ACT_SHOOT_S2N_FRYINGPAN,
	L4D2_ACT_SHOOT_S2N_IDLE_FRYINGPAN,
	L4D2_ACT_SHOOT_STRONG_FRYINGPAN,
	L4D2_ACT_SHOOT_STRONG_IDLE_FRYINGPAN,
	L4D2_ACT_SHOOT_SECONDARY_FRYINGPAN,
	L4D2_ACT_SHOOT_SECONDARY_IDLE_FRYINGPAN,
	L4D2_ACT_IDLE_FRYINGPAN,
	L4D2_ACT_CROUCHIDLE_FRYINGPAN,
	L4D2_ACT_IDLE_CALM_FRYINGPAN,
	L4D2_ACT_IDLE_INJURED_FRYINGPAN,
	L4D2_ACT_RUN_FRYINGPAN,
	L4D2_ACT_WALK_FRYINGPAN,
	L4D2_ACT_WALK_INJURED_FRYINGPAN,
	L4D2_ACT_RUN_CROUCH_FRYINGPAN,
	L4D2_ACT_RUN_INJURED_FRYINGPAN,
	L4D2_ACT_JUMP_FRYINGPAN,
	L4D2_ACT_SHOOT_E2W_GUITAR,
	L4D2_ACT_SHOOT_E2W_IDLE_GUITAR,
	L4D2_ACT_SHOOT_W2E_GUITAR,
	L4D2_ACT_SHOOT_W2E_IDLE_GUITAR,
	L4D2_ACT_SHOOT_N2S_GUITAR,
	L4D2_ACT_SHOOT_N2S_IDLE_GUITAR,
	L4D2_ACT_SHOOT_S2N_GUITAR,
	L4D2_ACT_SHOOT_S2N_IDLE_GUITAR,
	L4D2_ACT_SHOOT_STRONG_GUITAR,
	L4D2_ACT_SHOOT_STRONG_IDLE_GUITAR,
	L4D2_ACT_SHOOT_SECONDARY_GUITAR,
	L4D2_ACT_SHOOT_SECONDARY_IDLE_GUITAR,
	L4D2_ACT_IDLE_GUITAR,
	L4D2_ACT_CROUCHIDLE_GUITAR,
	L4D2_ACT_IDLE_CALM_GUITAR,
	L4D2_ACT_IDLE_INJURED_GUITAR,
	L4D2_ACT_RUN_GUITAR,
	L4D2_ACT_WALK_GUITAR,
	L4D2_ACT_WALK_INJURED_GUITAR,
	L4D2_ACT_RUN_CROUCH_GUITAR,
	L4D2_ACT_RUN_INJURED_GUITAR,
	L4D2_ACT_JUMP_GUITAR,
	L4D2_ACT_IDLE_CHAINSAW,
	L4D2_ACT_IDLE_ATTACK_CHAINSAW,
	L4D2_ACT_WALK_CHAINSAW,
	L4D2_ACT_RUN_CHAINSAW,
	L4D2_ACT_CROUCHIDLE_CHAINSAW,
	L4D2_ACT_IDLE_CALM_CHAINSAW,
	L4D2_ACT_IDLE_INJURED_CHAINSAW,
	L4D2_ACT_RUN_CALM_CHAINSAW,
	L4D2_ACT_WALK_CALM_CHAINSAW,
	L4D2_ACT_WALK_INJURED_CHAINSAW,
	L4D2_ACT_RUN_CROUCH_CHAINSAW,
	L4D2_ACT_RUN_INJURED_CHAINSAW,
	L4D2_ACT_JUMP_CHAINSAW,
	L4D2_ACT_MELEE_SWEEP_CHAINSAW,
	L4D2_ACT_PRIMARYATTACK_CHAINSAW,
	L4D2_ACT_DEPLOY_CHAINSAW,
	L4D2_ACT_TERROR_CHAINSAW_START,
	L4D2_ACT_SHOOT_W2E_CHAINSAW,
	L4D2_ACT_SHOOT_CHAINSAW,
	L4D2_ACT_CHAINSAW_STARTUP,
	L4D2_ACT_IDLE_GRENADELAUNCHER,
	L4D2_ACT_WALK_GRENADELAUNCHER,
	L4D2_ACT_RUN_GRENADELAUNCHER,
	L4D2_ACT_CROUCHIDLE_GRENADELAUNCHER,
	L4D2_ACT_IDLE_CALM_GRENADELAUNCHER,
	L4D2_ACT_IDLE_INJURED_GRENADELAUNCHER,
	L4D2_ACT_RUN_CALM_GRENADELAUNCHER,
	L4D2_ACT_WALK_CALM_GRENADELAUNCHER,
	L4D2_ACT_WALK_INJURED_GRENADELAUNCHER,
	L4D2_ACT_RUN_CROUCH_GRENADELAUNCHER,
	L4D2_ACT_RUN_INJURED_GRENADELAUNCHER,
	L4D2_ACT_JUMP_GRENADELAUNCHER,
	L4D2_ACT_MELEE_SWEEP_GRENADELAUNCHER,
	L4D2_ACT_PRIMARYATTACK_GRENADELAUNCHER,
	L4D2_ACT_DEPLOY_GRENADELAUNCHER,
	L4D2_ACT_VM_MELEE,
	L4D2_ACT_VM_SHOOT_LAYER,
	L4D2_ACT_VM_PRIMARYATTACK_LAYER,
	L4D2_ACT_VM_SECONDARYATTACK_LAYER,
	L4D2_ACT_VM_MELEE_LAYER,
	L4D2_ACT_VM_PICKUP_LAYER,
	L4D2_ACT_VM_PICKUP_CLIPIN,
	L4D2_ACT_VM_PICKUP_CLIPIN_LAYER,
	L4D2_ACT_VM_PICKUP_CHARGING,
	L4D2_ACT_VM_PICKUP_CHARGING_LAYER,
	L4D2_ACT_VM_PICKUP_FASSIST,
	L4D2_ACT_VM_PICKUP_FASSIST_LAYER,
	L4D2_ACT_VM_RELOAD_PUMP,
	L4D2_ACT_VM_RELOAD_PUMP_LAYER,
	L4D2_ACT_VM_RELOAD_LAYER,
	L4D2_ACT_VM_RELOAD_CLIPOUT,
	L4D2_ACT_VM_RELOAD_CLIPOUT_LAYER,
	L4D2_ACT_VM_RELOAD_CLIPIN,
	L4D2_ACT_VM_RELOAD_CLIPIN_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPOUT,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPOUT_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN2,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN2_LAYER,
	L4D2_ACT_VM_DEPLOY_LAYER,
	L4D2_ACT_VM_THROW_LAYER,
	L4D2_ACT_VM_PULLPIN_LAYER,
	L4D2_ACT_VM_IDLE_BAREHAND,
	L4D2_ACT_VM_DEPLOY_BAREHAND,
	L4D2_ACT_VM_DEPLOY_BAREHAND_LAYER,
	L4D2_ACT_VM_MELEE_BAREHAND,
	L4D2_ACT_VM_MELEE_BAREHAND_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_BAREHAND,
	L4D2_ACT_VM_ITEMPICKUP_EXTEND_BAREHAND_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_BAREHAND,
	L4D2_ACT_VM_ITEMPICKUP_LOOP_BAREHAND_LAYER,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_BAREHAND,
	L4D2_ACT_VM_ITEMPICKUP_RETRACT_BAREHAND_LAYER,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_BAREHAND,
	L4D2_ACT_VM_HELPINGHAND_EXTEND_BAREHAND_LAYER,
	L4D2_ACT_VM_HELPINGHAND_LOOP_BAREHAND,
	L4D2_ACT_VM_HELPINGHAND_LOOP_BAREHAND_LAYER,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_BAREHAND,
	L4D2_ACT_VM_HELPINGHAND_RETRACT_BAREHAND_LAYER,
	L4D2_ACT_VM_IDLE_DUAL_PISTOL,
	L4D2_ACT_VM_DEPLOY_DUAL_PISTOL,
	L4D2_ACT_VM_DEPLOY_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_PRIMARYATTACK_DUAL_PISTOL,
	L4D2_ACT_VM_SHOOT_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_SECONDARYATTACK_DUAL_PISTOL,
	L4D2_ACT_VM_SECONDARYATTACK_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_RELOAD_DUAL_PISTOL,
	L4D2_ACT_VM_RELOAD_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_RELOAD_CLIPOUT_DUAL_PISTOL,
	L4D2_ACT_VM_RELOAD_CLIPOUT_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_RELOAD_CLIPIN_DUAL_PISTOL,
	L4D2_ACT_VM_RELOAD_CLIPIN_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_DUAL_PISTOL,
	L4D2_ACT_VM_RELOAD_EMPTY_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPOUT_DUAL_PISTOL,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPOUT_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN_DUAL_PISTOL,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN2_DUAL_PISTOL,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN2_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_PICKUP_DUAL_PISTOL,
	L4D2_ACT_VM_PICKUP_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_PICKUP_CLIPIN_DUAL_PISTOL,
	L4D2_ACT_VM_PICKUP_CLIPIN_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_PICKUP_CHARGING_DUAL_PISTOL,
	L4D2_ACT_VM_PICKUP_CHARGING_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_MELEE_DUAL_PISTOL,
	L4D2_ACT_VM_MELEE_DUAL_PISTOL_LAYER,
	L4D2_ACT_VM_IDLE_PISTOL,
	L4D2_ACT_VM_DEPLOY_PISTOL,
	L4D2_ACT_VM_DEPLOY_PISTOL_LAYER,
	L4D2_ACT_VM_PRIMARYATTACK_PISTOL,
	L4D2_ACT_VM_SHOOT_PISTOL_LAYER,
	L4D2_ACT_VM_RELOAD_PISTOL,
	L4D2_ACT_VM_RELOAD_PISTOL_LAYER,
	L4D2_ACT_VM_RELOAD_CLIPOUT_PISTOL,
	L4D2_ACT_VM_RELOAD_CLIPOUT_PISTOL_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_PISTOL,
	L4D2_ACT_VM_RELOAD_EMPTY_PISTOL_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPOUT_PISTOL,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPOUT_PISTOL_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN_PISTOL,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN_PISTOL_LAYER,
	L4D2_ACT_VM_MELEE_PISTOL,
	L4D2_ACT_VM_MELEE_PISTOL_LAYER,
	L4D2_ACT_VM_IDLE_RIFLE,
	L4D2_ACT_VM_PICKUP_RIFLE,
	L4D2_ACT_VM_PICKUP_RIFLE_LAYER,
	L4D2_ACT_VM_PICKUP_CLIPIN_RIFLE,
	L4D2_ACT_VM_PICKUP_CLIPIN_RIFLE_LAYER,
	L4D2_ACT_VM_PICKUP_CHARGING_RIFLE,
	L4D2_ACT_VM_PICKUP_CHARGING_RIFLE_LAYER,
	L4D2_ACT_VM_PICKUP_FASSIST_RIFLE,
	L4D2_ACT_VM_PICKUP_FASSIST_RIFLE_LAYER,
	L4D2_ACT_VM_DEPLOY_RIFLE,
	L4D2_ACT_VM_DEPLOY_RIFLE_LAYER,
	L4D2_ACT_VM_PRIMARYATTACK_RIFLE,
	L4D2_ACT_VM_SHOOT_RIFLE_LAYER,
	L4D2_ACT_VM_RELOAD_RIFLE,
	L4D2_ACT_VM_RELOAD_RIFLE_LAYER,
	L4D2_ACT_VM_RELOAD_CLIPOUT_RIFLE,
	L4D2_ACT_VM_RELOAD_CLIPOUT_RIFLE_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_RIFLE,
	L4D2_ACT_VM_RELOAD_EMPTY_RIFLE_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPOUT_RIFLE,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPOUT_RIFLE_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN_RIFLE,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN_RIFLE_LAYER,
	L4D2_ACT_VM_MELEE_RIFLE,
	L4D2_ACT_VM_MELEE_RIFLE_LAYER,
	L4D2_ACT_VM_FIDGET_RIFLE_LAYER,
	L4D2_ACT_VM_IDLE_SMG,
	L4D2_ACT_VM_PICKUP_SMG,
	L4D2_ACT_VM_PICKUP_SMG_LAYER,
	L4D2_ACT_VM_PICKUP_CLIPIN_SMG,
	L4D2_ACT_VM_PICKUP_CLIPIN_SMG_LAYER,
	L4D2_ACT_VM_PICKUP_CHARGING_SMG,
	L4D2_ACT_VM_PICKUP_CHARGING_SMG_LAYER,
	L4D2_ACT_VM_DEPLOY_SMG,
	L4D2_ACT_VM_DEPLOY_SMG_LAYER,
	L4D2_ACT_VM_PRIMARYATTACK_SMG,
	L4D2_ACT_VM_SHOOT_SMG_LAYER,
	L4D2_ACT_VM_RELOAD_SMG,
	L4D2_ACT_VM_RELOAD_SMG_LAYER,
	L4D2_ACT_VM_RELOAD_CLIPOUT_SMG,
	L4D2_ACT_VM_RELOAD_CLIPOUT_SMG_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_SMG,
	L4D2_ACT_VM_RELOAD_EMPTY_SMG_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPOUT_SMG,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPOUT_SMG_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN_SMG,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN_SMG_LAYER,
	L4D2_ACT_VM_MELEE_SMG,
	L4D2_ACT_VM_MELEE_SMG_LAYER,
	L4D2_ACT_VM_FIDGET_SMG_LAYER,
	L4D2_ACT_VM_IDLE_SNIPER,
	L4D2_ACT_VM_PICKUP_SNIPER,
	L4D2_ACT_VM_PICKUP_SNIPER_LAYER,
	L4D2_ACT_VM_PICKUP_CLIPIN_SNIPER,
	L4D2_ACT_VM_PICKUP_CLIPIN_SNIPER_LAYER,
	L4D2_ACT_VM_PICKUP_CHARGING_SNIPER,
	L4D2_ACT_VM_PICKUP_CHARGING_SNIPER_LAYER,
	L4D2_ACT_VM_DEPLOY_SNIPER,
	L4D2_ACT_VM_DEPLOY_SNIPER_LAYER,
	L4D2_ACT_VM_PRIMARYATTACK_SNIPER,
	L4D2_ACT_VM_SHOOT_SNIPER_LAYER,
	L4D2_ACT_VM_RELOAD_SNIPER,
	L4D2_ACT_VM_RELOAD_SNIPER_LAYER,
	L4D2_ACT_VM_RELOAD_CLIPOUT_SNIPER,
	L4D2_ACT_VM_RELOAD_CLIPOUT_SNIPER_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_SNIPER,
	L4D2_ACT_VM_RELOAD_EMPTY_SNIPER_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPOUT_SNIPER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPOUT_SNIPER_LAYER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN_SNIPER,
	L4D2_ACT_VM_RELOAD_EMPTY_CLIPIN_SNIPER_LAYER,
	L4D2_ACT_VM_MELEE_SNIPER,
	L4D2_ACT_VM_MELEE_SNIPER_LAYER,
	L4D2_ACT_VM_FIDGET_SNIPER_LAYER,
	L4D2_ACT_VM_RELOAD_LOOP,
	L4D2_ACT_VM_RELOAD_LOOP_LAYER,
	L4D2_ACT_VM_RELOAD_END,
	L4D2_ACT_VM_RELOAD_END_LAYER,
	L4D2_ACT_VM_IDLE_PIPEBOMB,
	L4D2_ACT_VM_DEPLOY_PIPEBOMB,
	L4D2_ACT_VM_DEPLOY_PIPEBOMB_LAYER,
	L4D2_ACT_VM_PULLPIN_PIPEBOMB,
	L4D2_ACT_VM_PULLPIN_PIPEBOMB_LAYER,
	L4D2_ACT_VM_THROW_PIPEBOMB,
	L4D2_ACT_VM_THROW_PIPEBOMB_LAYER,
	L4D2_ACT_VM_MELEE_PIPEBOMB,
	L4D2_ACT_VM_MELEE_PIPEBOMB_LAYER,
	L4D2_ACT_VM_IDLE_MOLOTOV,
	L4D2_ACT_VM_DEPLOY_MOLOTOV,
	L4D2_ACT_VM_DEPLOY_MOLOTOV_LAYER,
	L4D2_ACT_VM_PULLPIN_MOLOTOV,
	L4D2_ACT_VM_PULLPIN_MOLOTOV_LAYER,
	L4D2_ACT_VM_THROW_MOLOTOV,
	L4D2_ACT_VM_THROW_MOLOTOV_LAYER,
	L4D2_ACT_VM_MELEE_MOLOTOV,
	L4D2_ACT_VM_MELEE_MOLOTOV_LAYER,
	L4D2_ACT_VM_IDLE_PAINPILLS,
	L4D2_ACT_VM_DEPLOY_PAINPILLS,
	L4D2_ACT_VM_DEPLOY_PAINPILLS_LAYER,
	L4D2_ACT_VM_MELEE_PAINPILLS,
	L4D2_ACT_VM_MELEE_PAINPILLS_LAYER,
	L4D2_ACT_VM_USE_PAINPILLS,
	L4D2_ACT_VM_USE_PAINPILLS_LAYER,
	L4D2_ACT_VM_IDLE_MEDKIT,
	L4D2_ACT_VM_DEPLOY_MEDKIT,
	L4D2_ACT_VM_DEPLOY_MEDKIT_LAYER,
	L4D2_ACT_VM_MELEE_MEDKIT,
	L4D2_ACT_VM_MELEE_MEDKIT_LAYER,
	L4D2_ACT_VM_IDLE_GASCAN,
	L4D2_ACT_VM_DEPLOY_GASCAN,
	L4D2_ACT_VM_PULLPIN_GASCAN,
	L4D2_ACT_VM_THROW_GASCAN,
	L4D2_ACT_VM_MELEE_GASCAN,
	L4D2_ACT_GEST_OVERHERE,
	L4D2_ACT_GEST_POINTLEFT,
	L4D2_ACT_GEST_POINTLEFT_QUICK,
	L4D2_ACT_GEST_HEAD_TWISTLEFT,
	L4D2_ACT_GEST_HEAD_DOWN,
	L4D2_ACT_GEST_GOGOGO,
	L4D2_ACT_GEST_WAVE,
	L4D2_ACT_GEST_COUGH,
	L4D2_ACT_GEST_COUGH01,
	L4D2_ACT_GEST_COUGH02,
	L4D2_ACT_GEST_COUGH03,
	L4D2_ACT_GEST_COUGH1,
	L4D2_ACT_GEST_COUGH2,
	L4D2_ACT_GEST_COUGH3,
	L4D2_ACT_GEST_VOMIT,
	L4D2_ACT_GEST_VOMIT01,
	L4D2_ACT_GEST_INTRO_CALM,
	L4D2_ACT_GEST_INTRO_WAVE,
	L4D2_ACT_DLCINTRO_01,
	L4D2_ACT_DLCINTRO_02,
	L4D2_ACT_DLCINTRO_03,
	L4D2_ACT_DLCINTRO_04,
	L4D2_ACT_DLCFI_FRANCIS_HLD,
	L4D2_ACT_DLCFI_FRANCIS_017MB,
	L4D2_ACT_DLCFI_FRANCIS_018MB,
	L4D2_ACT_DLCFI_FRANCIS_01,
	L4D2_ACT_DLCFI_FRANCIS_02,
	L4D2_ACT_DLCFI_FRANCIS_03,
	L4D2_ACT_DLCFI_FRANCIS_04,
	L4D2_ACT_DLCFI_FRANCIS_05,
	L4D2_ACT_DLCFI_FRANCIS_06,
	L4D2_ACT_DLCFI_FRANCIS_07,
	L4D2_ACT_DLCFI_FRANCIS_08,
	L4D2_ACT_DLCFI_FRANCIS_08b,
	L4D2_ACT_DLCFI_FRANCIS_09,
	L4D2_ACT_DLCFI_FRANCIS_09b,
	L4D2_ACT_DLCFI_FRANCIS_10,
	L4D2_ACT_DLCFI_FRANCIS_10b,
	L4D2_ACT_DLCFI_FRANCIS_11,
	L4D2_ACT_DLCFI_FRANCIS_11b,
	L4D2_ACT_DLCFI_FRANCIS_12AH,
	L4D2_ACT_DLCFI_FRANCIS_12mr,
	L4D2_ACT_DLCFI_FRANCIS_13mr,
	L4D2_ACT_DLCFI_FRANCIS_13AH,
	L4D2_ACT_DLCFI_FRANCIS_14AH,
	L4D2_ACT_DLCFI_FRANCIS_15AH,
	L4D2_ACT_DLCFI_FRANCIS_16AH,
	L4D2_ACT_DLCFI_FRANCIS_17AH,
	L4D2_ACT_DLCFI_FRANCIS_18AH,
	L4D2_ACT_DLCFI_FRANCIS_19AH,
	L4D2_ACT_DLCFI_FRANCIS_20AH,
	L4D2_ACT_DLCFI_FRANCIS_21AH,
	L4D2_ACT_DLCFI_FRANCIS_14mr,
	L4D2_ACT_DLCFI_FRANCIS_15mr,
	L4D2_ACT_DLCFI_FRANCIS_16mr,
	L4D2_ACT_DLCFI_FRANCIS_17mr,
	L4D2_ACT_DLCFI_FRANCIS_014MB,
	L4D2_ACT_DLCFI_FRANCIS_014MBX,
	L4D2_ACT_DLCFI_FRANCIS_13nm,
	L4D2_ACT_DLCFI_LOUIS_01,
	L4D2_ACT_DLCFI_LOUIS_02,
	L4D2_ACT_DLCFI_LOUIS_03,
	L4D2_ACT_DLCFI_LOUIS_04,
	L4D2_ACT_DLCFI_LOUIS_05,
	L4D2_ACT_DLCFI_LOUIS_06,
	L4D2_ACT_DLCFI_LOUIS_07MB,
	L4D2_ACT_DLCFI_LOUIS_08MB,
	L4D2_ACT_DLCFI_LOUIS_09MB,
	L4D2_ACT_DLCFI_LOUIS_10MB,
	L4D2_ACT_DLCFI_LOUIS_11,
	L4D2_ACT_DLCFI_LOUIS_11MB,
	L4D2_ACT_DLCFI_LOUIS_11AH,
	L4D2_ACT_DLCFI_LOUIS_12,
	L4D2_ACT_DLCFI_LOUIS_012MB,
	L4D2_ACT_DLCFI_LOUIS_012MR,
	L4D2_ACT_DLCFI_LOUIS_13,
	L4D2_ACT_DLCFI_LOUIS_013MB,
	L4D2_ACT_DLCFI_LOUIS_013MR,
	L4D2_ACT_DLCFI_LOUIS_14,
	L4D2_ACT_DLCFI_LOUIS_014MB,
	L4D2_ACT_DLCFI_LOUIS_14MR,
	L4D2_ACT_DLCFI_LOUIS_15,
	L4D2_ACT_DLCFI_LOUIS_015MB,
	L4D2_ACT_DLCFI_LOUIS_016MB,
	L4D2_ACT_DLCFI_LOUIS_16JM,
	L4D2_ACT_DLCFI_LOUIS_16AJM,
	L4D2_ACT_DLCFI_LOUIS_017MB,
	L4D2_ACT_DLCFI_ZOEY_01,
	L4D2_ACT_DLCFI_ZOEY_02,
	L4D2_ACT_DLCFI_ZOEY_03,
	L4D2_ACT_DLCFI_ZOEY_04,
	L4D2_ACT_DLCFI_ZOEY_05,
	L4D2_ACT_DLCFI_ZOEY_06,
	L4D2_ACT_DLCFI_ZOEY_07MB,
	L4D2_ACT_DLCFI_ZOEY_08MB,
	L4D2_ACT_DLCFI_ZOEY_09MB,
	L4D2_ACT_DLCFI_ZOEY_10MB,
	L4D2_ACT_DLCFI_ZOEY_11MB,
	L4D2_ACT_DLCFI_ZOEY_06AH,
	L4D2_ACT_DLCFI_ZOEY_07AH,
	L4D2_ACT_DLCFI_ZOEY_07mr,
	L4D2_ACT_DLCFI_ZOEY_12MB,
	L4D2_ACT_DLCFI_ZOEY_08mr,
	L4D2_ACT_DLCFI_ZOEY_10MR,
	L4D2_ACT_DLCINTRO_ZOEY_01,
	L4D2_ACT_DLCINTRO_ZOEY_02,
	L4D2_ACT_DLCINTRO_ZOEY_03,
	L4D2_ACT_DLCINTRO_ZOEY_04,
	L4D2_ACT_DLCINTRO_ZOEY_05,
	L4D2_ACT_DLCINTRO_ZOEY_06,
	L4D2_ACT_VM_FIZZLE,
	L4D2_ACT_MP_STAND_IDLE,
	L4D2_ACT_MP_CROUCH_IDLE,
	L4D2_ACT_MP_CROUCH_DEPLOYED_IDLE,
	L4D2_ACT_MP_CROUCH_DEPLOYED,
	L4D2_ACT_MP_DEPLOYED_IDLE,
	L4D2_ACT_MP_RUN,
	L4D2_ACT_MP_WALK,
	L4D2_ACT_MP_AIRWALK,
	L4D2_ACT_MP_CROUCHWALK,
	L4D2_ACT_MP_SPRINT,
	L4D2_ACT_MP_JUMP,
	L4D2_ACT_MP_JUMP_START,
	L4D2_ACT_MP_JUMP_FLOAT,
	L4D2_ACT_MP_JUMP_LAND,
	L4D2_ACT_MP_DOUBLEJUMP,
	L4D2_ACT_MP_SWIM,
	L4D2_ACT_MP_DEPLOYED,
	L4D2_ACT_MP_SWIM_DEPLOYED,
	L4D2_ACT_MP_VCD,
	L4D2_ACT_MP_ATTACK_STAND_PRIMARYFIRE,
	L4D2_ACT_MP_ATTACK_STAND_PRIMARYFIRE_DEPLOYED,
	L4D2_ACT_MP_ATTACK_STAND_SECONDARYFIRE,
	L4D2_ACT_MP_ATTACK_STAND_GRENADE,
	L4D2_ACT_MP_ATTACK_CROUCH_PRIMARYFIRE,
	L4D2_ACT_MP_ATTACK_CROUCH_PRIMARYFIRE_DEPLOYED,
	L4D2_ACT_MP_ATTACK_CROUCH_SECONDARYFIRE,
	L4D2_ACT_MP_ATTACK_CROUCH_GRENADE,
	L4D2_ACT_MP_ATTACK_SWIM_PRIMARYFIRE,
	L4D2_ACT_MP_ATTACK_SWIM_SECONDARYFIRE,
	L4D2_ACT_MP_ATTACK_SWIM_GRENADE,
	L4D2_ACT_MP_ATTACK_AIRWALK_PRIMARYFIRE,
	L4D2_ACT_MP_ATTACK_AIRWALK_SECONDARYFIRE,
	L4D2_ACT_MP_ATTACK_AIRWALK_GRENADE,
	L4D2_ACT_MP_RELOAD_STAND,
	L4D2_ACT_MP_RELOAD_STAND_LOOP,
	L4D2_ACT_MP_RELOAD_STAND_END,
	L4D2_ACT_MP_RELOAD_CROUCH,
	L4D2_ACT_MP_RELOAD_CROUCH_LOOP,
	L4D2_ACT_MP_RELOAD_CROUCH_END,
	L4D2_ACT_MP_RELOAD_SWIM,
	L4D2_ACT_MP_RELOAD_SWIM_LOOP,
	L4D2_ACT_MP_RELOAD_SWIM_END,
	L4D2_ACT_MP_RELOAD_AIRWALK,
	L4D2_ACT_MP_RELOAD_AIRWALK_LOOP,
	L4D2_ACT_MP_RELOAD_AIRWALK_END,
	L4D2_ACT_MP_ATTACK_STAND_PREFIRE,
	L4D2_ACT_MP_ATTACK_STAND_POSTFIRE,
	L4D2_ACT_MP_ATTACK_STAND_STARTFIRE,
	L4D2_ACT_MP_ATTACK_CROUCH_PREFIRE,
	L4D2_ACT_MP_ATTACK_CROUCH_POSTFIRE,
	L4D2_ACT_MP_ATTACK_SWIM_PREFIRE,
	L4D2_ACT_MP_ATTACK_SWIM_POSTFIRE,
	L4D2_ACT_MP_STAND_PRIMARY,
	L4D2_ACT_MP_CROUCH_PRIMARY,
	L4D2_ACT_MP_RUN_PRIMARY,
	L4D2_ACT_MP_WALK_PRIMARY,
	L4D2_ACT_MP_AIRWALK_PRIMARY,
	L4D2_ACT_MP_CROUCHWALK_PRIMARY,
	L4D2_ACT_MP_JUMP_PRIMARY,
	L4D2_ACT_MP_JUMP_START_PRIMARY,
	L4D2_ACT_MP_JUMP_FLOAT_PRIMARY,
	L4D2_ACT_MP_JUMP_LAND_PRIMARY,
	L4D2_ACT_MP_SWIM_PRIMARY,
	L4D2_ACT_MP_DEPLOYED_PRIMARY,
	L4D2_ACT_MP_SWIM_DEPLOYED_PRIMARY,
	L4D2_ACT_MP_ATTACK_STAND_PRIMARY,
	L4D2_ACT_MP_ATTACK_STAND_PRIMARY_DEPLOYED,
	L4D2_ACT_MP_ATTACK_CROUCH_PRIMARY,
	L4D2_ACT_MP_ATTACK_CROUCH_PRIMARY_DEPLOYED,
	L4D2_ACT_MP_ATTACK_SWIM_PRIMARY,
	L4D2_ACT_MP_ATTACK_AIRWALK_PRIMARY,
	L4D2_ACT_MP_RELOAD_STAND_PRIMARY,
	L4D2_ACT_MP_RELOAD_STAND_PRIMARY_LOOP,
	L4D2_ACT_MP_RELOAD_STAND_PRIMARY_END,
	L4D2_ACT_MP_RELOAD_CROUCH_PRIMARY,
	L4D2_ACT_MP_RELOAD_CROUCH_PRIMARY_LOOP,
	L4D2_ACT_MP_RELOAD_CROUCH_PRIMARY_END,
	L4D2_ACT_MP_RELOAD_SWIM_PRIMARY,
	L4D2_ACT_MP_RELOAD_SWIM_PRIMARY_LOOP,
	L4D2_ACT_MP_RELOAD_SWIM_PRIMARY_END,
	L4D2_ACT_MP_RELOAD_AIRWALK_PRIMARY,
	L4D2_ACT_MP_RELOAD_AIRWALK_PRIMARY_LOOP,
	L4D2_ACT_MP_RELOAD_AIRWALK_PRIMARY_END,
	L4D2_ACT_MP_ATTACK_STAND_GRENADE_PRIMARY,
	L4D2_ACT_MP_ATTACK_CROUCH_GRENADE_PRIMARY,
	L4D2_ACT_MP_ATTACK_SWIM_GRENADE_PRIMARY,
	L4D2_ACT_MP_ATTACK_AIRWALK_GRENADE_PRIMARY,
	L4D2_ACT_MP_STAND_SECONDARY,
	L4D2_ACT_MP_CROUCH_SECONDARY,
	L4D2_ACT_MP_RUN_SECONDARY,
	L4D2_ACT_MP_WALK_SECONDARY,
	L4D2_ACT_MP_AIRWALK_SECONDARY,
	L4D2_ACT_MP_CROUCHWALK_SECONDARY,
	L4D2_ACT_MP_JUMP_SECONDARY,
	L4D2_ACT_MP_JUMP_START_SECONDARY,
	L4D2_ACT_MP_JUMP_FLOAT_SECONDARY,
	L4D2_ACT_MP_JUMP_LAND_SECONDARY,
	L4D2_ACT_MP_SWIM_SECONDARY,
	L4D2_ACT_MP_ATTACK_STAND_SECONDARY,
	L4D2_ACT_MP_ATTACK_CROUCH_SECONDARY,
	L4D2_ACT_MP_ATTACK_SWIM_SECONDARY,
	L4D2_ACT_MP_ATTACK_AIRWALK_SECONDARY,
	L4D2_ACT_MP_RELOAD_STAND_SECONDARY,
	L4D2_ACT_MP_RELOAD_STAND_SECONDARY_LOOP,
	L4D2_ACT_MP_RELOAD_STAND_SECONDARY_END,
	L4D2_ACT_MP_RELOAD_CROUCH_SECONDARY,
	L4D2_ACT_MP_RELOAD_CROUCH_SECONDARY_LOOP,
	L4D2_ACT_MP_RELOAD_CROUCH_SECONDARY_END,
	L4D2_ACT_MP_RELOAD_SWIM_SECONDARY,
	L4D2_ACT_MP_RELOAD_SWIM_SECONDARY_LOOP,
	L4D2_ACT_MP_RELOAD_SWIM_SECONDARY_END,
	L4D2_ACT_MP_RELOAD_AIRWALK_SECONDARY,
	L4D2_ACT_MP_RELOAD_AIRWALK_SECONDARY_LOOP,
	L4D2_ACT_MP_RELOAD_AIRWALK_SECONDARY_END,
	L4D2_ACT_MP_ATTACK_STAND_GRENADE_SECONDARY,
	L4D2_ACT_MP_ATTACK_CROUCH_GRENADE_SECONDARY,
	L4D2_ACT_MP_ATTACK_SWIM_GRENADE_SECONDARY,
	L4D2_ACT_MP_ATTACK_AIRWALK_GRENADE_SECONDARY,
	L4D2_ACT_MP_STAND_MELEE,
	L4D2_ACT_MP_CROUCH_MELEE,
	L4D2_ACT_MP_RUN_MELEE,
	L4D2_ACT_MP_WALK_MELEE,
	L4D2_ACT_MP_AIRWALK_MELEE,
	L4D2_ACT_MP_CROUCHWALK_MELEE,
	L4D2_ACT_MP_JUMP_MELEE,
	L4D2_ACT_MP_JUMP_START_MELEE,
	L4D2_ACT_MP_JUMP_FLOAT_MELEE,
	L4D2_ACT_MP_JUMP_LAND_MELEE,
	L4D2_ACT_MP_SWIM_MELEE,
	L4D2_ACT_MP_ATTACK_STAND_MELEE,
	L4D2_ACT_MP_ATTACK_STAND_MELEE_SECONDARY,
	L4D2_ACT_MP_ATTACK_CROUCH_MELEE,
	L4D2_ACT_MP_ATTACK_CROUCH_MELEE_SECONDARY,
	L4D2_ACT_MP_ATTACK_SWIM_MELEE,
	L4D2_ACT_MP_ATTACK_AIRWALK_MELEE,
	L4D2_ACT_MP_ATTACK_STAND_GRENADE_MELEE,
	L4D2_ACT_MP_ATTACK_CROUCH_GRENADE_MELEE,
	L4D2_ACT_MP_ATTACK_SWIM_GRENADE_MELEE,
	L4D2_ACT_MP_ATTACK_AIRWALK_GRENADE_MELEE,
	L4D2_ACT_MP_STAND_ITEM1,
	L4D2_ACT_MP_CROUCH_ITEM1,
	L4D2_ACT_MP_RUN_ITEM1,
	L4D2_ACT_MP_WALK_ITEM1,
	L4D2_ACT_MP_AIRWALK_ITEM1,
	L4D2_ACT_MP_CROUCHWALK_ITEM1,
	L4D2_ACT_MP_JUMP_ITEM1,
	L4D2_ACT_MP_JUMP_START_ITEM1,
	L4D2_ACT_MP_JUMP_FLOAT_ITEM1,
	L4D2_ACT_MP_JUMP_LAND_ITEM1,
	L4D2_ACT_MP_SWIM_ITEM1,
	L4D2_ACT_MP_ATTACK_STAND_ITEM1,
	L4D2_ACT_MP_ATTACK_STAND_ITEM1_SECONDARY,
	L4D2_ACT_MP_ATTACK_CROUCH_ITEM1,
	L4D2_ACT_MP_ATTACK_CROUCH_ITEM1_SECONDARY,
	L4D2_ACT_MP_ATTACK_SWIM_ITEM1,
	L4D2_ACT_MP_ATTACK_AIRWALK_ITEM1,
	L4D2_ACT_MP_STAND_ITEM2,
	L4D2_ACT_MP_CROUCH_ITEM2,
	L4D2_ACT_MP_RUN_ITEM2,
	L4D2_ACT_MP_WALK_ITEM2,
	L4D2_ACT_MP_AIRWALK_ITEM2,
	L4D2_ACT_MP_CROUCHWALK_ITEM2,
	L4D2_ACT_MP_JUMP_ITEM2,
	L4D2_ACT_MP_JUMP_START_ITEM2,
	L4D2_ACT_MP_JUMP_FLOAT_ITEM2,
	L4D2_ACT_MP_JUMP_LAND_ITEM2,
	L4D2_ACT_MP_SWIM_ITEM2,
	L4D2_ACT_MP_ATTACK_STAND_ITEM2,
	L4D2_ACT_MP_ATTACK_STAND_ITEM2_SECONDARY,
	L4D2_ACT_MP_ATTACK_CROUCH_ITEM2,
	L4D2_ACT_MP_ATTACK_CROUCH_ITEM2_SECONDARY,
	L4D2_ACT_MP_ATTACK_SWIM_ITEM2,
	L4D2_ACT_MP_ATTACK_AIRWALK_ITEM2,
	L4D2_ACT_MP_GESTURE_FLINCH,
	L4D2_ACT_MP_GESTURE_FLINCH_PRIMARY,
	L4D2_ACT_MP_GESTURE_FLINCH_SECONDARY,
	L4D2_ACT_MP_GESTURE_FLINCH_MELEE,
	L4D2_ACT_MP_GESTURE_FLINCH_ITEM1,
	L4D2_ACT_MP_GESTURE_FLINCH_ITEM2,
	L4D2_ACT_MP_GESTURE_FLINCH_HEAD,
	L4D2_ACT_MP_GESTURE_FLINCH_CHEST,
	L4D2_ACT_MP_GESTURE_FLINCH_STOMACH,
	L4D2_ACT_MP_GESTURE_FLINCH_LEFTARM,
	L4D2_ACT_MP_GESTURE_FLINCH_RIGHTARM,
	L4D2_ACT_MP_GESTURE_FLINCH_LEFTLEG,
	L4D2_ACT_MP_GESTURE_FLINCH_RIGHTLEG,
	L4D2_ACT_MP_GRENADE1_DRAW,
	L4D2_ACT_MP_GRENADE1_IDLE,
	L4D2_ACT_MP_GRENADE1_ATTACK,
	L4D2_ACT_MP_GRENADE2_DRAW,
	L4D2_ACT_MP_GRENADE2_IDLE,
	L4D2_ACT_MP_GRENADE2_ATTACK,
	L4D2_ACT_MP_PRIMARY_GRENADE1_DRAW,
	L4D2_ACT_MP_PRIMARY_GRENADE1_IDLE,
	L4D2_ACT_MP_PRIMARY_GRENADE1_ATTACK,
	L4D2_ACT_MP_PRIMARY_GRENADE2_DRAW,
	L4D2_ACT_MP_PRIMARY_GRENADE2_IDLE,
	L4D2_ACT_MP_PRIMARY_GRENADE2_ATTACK,
	L4D2_ACT_MP_SECONDARY_GRENADE1_DRAW,
	L4D2_ACT_MP_SECONDARY_GRENADE1_IDLE,
	L4D2_ACT_MP_SECONDARY_GRENADE1_ATTACK,
	L4D2_ACT_MP_SECONDARY_GRENADE2_DRAW,
	L4D2_ACT_MP_SECONDARY_GRENADE2_IDLE,
	L4D2_ACT_MP_SECONDARY_GRENADE2_ATTACK,
	L4D2_ACT_MP_MELEE_GRENADE1_DRAW,
	L4D2_ACT_MP_MELEE_GRENADE1_IDLE,
	L4D2_ACT_MP_MELEE_GRENADE1_ATTACK,
	L4D2_ACT_MP_MELEE_GRENADE2_DRAW,
	L4D2_ACT_MP_MELEE_GRENADE2_IDLE,
	L4D2_ACT_MP_MELEE_GRENADE2_ATTACK,
	L4D2_ACT_MP_ITEM1_GRENADE1_DRAW,
	L4D2_ACT_MP_ITEM1_GRENADE1_IDLE,
	L4D2_ACT_MP_ITEM1_GRENADE1_ATTACK,
	L4D2_ACT_MP_ITEM1_GRENADE2_DRAW,
	L4D2_ACT_MP_ITEM1_GRENADE2_IDLE,
	L4D2_ACT_MP_ITEM1_GRENADE2_ATTACK,
	L4D2_ACT_MP_ITEM2_GRENADE1_DRAW,
	L4D2_ACT_MP_ITEM2_GRENADE1_IDLE,
	L4D2_ACT_MP_ITEM2_GRENADE1_ATTACK,
	L4D2_ACT_MP_ITEM2_GRENADE2_DRAW,
	L4D2_ACT_MP_ITEM2_GRENADE2_IDLE,
	L4D2_ACT_MP_ITEM2_GRENADE2_ATTACK,
	L4D2_ACT_MP_STAND_BUILDING,
	L4D2_ACT_MP_CROUCH_BUILDING,
	L4D2_ACT_MP_RUN_BUILDING,
	L4D2_ACT_MP_WALK_BUILDING,
	L4D2_ACT_MP_AIRWALK_BUILDING,
	L4D2_ACT_MP_CROUCHWALK_BUILDING,
	L4D2_ACT_MP_JUMP_BUILDING,
	L4D2_ACT_MP_JUMP_START_BUILDING,
	L4D2_ACT_MP_JUMP_FLOAT_BUILDING,
	L4D2_ACT_MP_JUMP_LAND_BUILDING,
	L4D2_ACT_MP_SWIM_BUILDING,
	L4D2_ACT_MP_ATTACK_STAND_BUILDING,
	L4D2_ACT_MP_ATTACK_CROUCH_BUILDING,
	L4D2_ACT_MP_ATTACK_SWIM_BUILDING,
	L4D2_ACT_MP_ATTACK_AIRWALK_BUILDING,
	L4D2_ACT_MP_ATTACK_STAND_GRENADE_BUILDING,
	L4D2_ACT_MP_ATTACK_CROUCH_GRENADE_BUILDING,
	L4D2_ACT_MP_ATTACK_SWIM_GRENADE_BUILDING,
	L4D2_ACT_MP_ATTACK_AIRWALK_GRENADE_BUILDING,
	L4D2_ACT_MP_STAND_PDA,
	L4D2_ACT_MP_CROUCH_PDA,
	L4D2_ACT_MP_RUN_PDA,
	L4D2_ACT_MP_WALK_PDA,
	L4D2_ACT_MP_AIRWALK_PDA,
	L4D2_ACT_MP_CROUCHWALK_PDA,
	L4D2_ACT_MP_JUMP_PDA,
	L4D2_ACT_MP_JUMP_START_PDA,
	L4D2_ACT_MP_JUMP_FLOAT_PDA,
	L4D2_ACT_MP_JUMP_LAND_PDA,
	L4D2_ACT_MP_SWIM_PDA,
	L4D2_ACT_MP_ATTACK_STAND_PDA,
	L4D2_ACT_MP_ATTACK_SWIM_PDA,
	L4D2_ACT_MP_GESTURE_VC_HANDMOUTH,
	L4D2_ACT_MP_GESTURE_VC_FINGERPOINT,
	L4D2_ACT_MP_GESTURE_VC_FISTPUMP,
	L4D2_ACT_MP_GESTURE_VC_THUMBSUP,
	L4D2_ACT_MP_GESTURE_VC_NODYES,
	L4D2_ACT_MP_GESTURE_VC_NODNO,
	L4D2_ACT_MP_GESTURE_VC_HANDMOUTH_PRIMARY,
	L4D2_ACT_MP_GESTURE_VC_FINGERPOINT_PRIMARY,
	L4D2_ACT_MP_GESTURE_VC_FISTPUMP_PRIMARY,
	L4D2_ACT_MP_GESTURE_VC_THUMBSUP_PRIMARY,
	L4D2_ACT_MP_GESTURE_VC_NODYES_PRIMARY,
	L4D2_ACT_MP_GESTURE_VC_NODNO_PRIMARY,
	L4D2_ACT_MP_GESTURE_VC_HANDMOUTH_SECONDARY,
	L4D2_ACT_MP_GESTURE_VC_FINGERPOINT_SECONDARY,
	L4D2_ACT_MP_GESTURE_VC_FISTPUMP_SECONDARY,
	L4D2_ACT_MP_GESTURE_VC_THUMBSUP_SECONDARY,
	L4D2_ACT_MP_GESTURE_VC_NODYES_SECONDARY,
	L4D2_ACT_MP_GESTURE_VC_NODNO_SECONDARY,
	L4D2_ACT_MP_GESTURE_VC_HANDMOUTH_MELEE,
	L4D2_ACT_MP_GESTURE_VC_FINGERPOINT_MELEE,
	L4D2_ACT_MP_GESTURE_VC_FISTPUMP_MELEE,
	L4D2_ACT_MP_GESTURE_VC_THUMBSUP_MELEE,
	L4D2_ACT_MP_GESTURE_VC_NODYES_MELEE,
	L4D2_ACT_MP_GESTURE_VC_NODNO_MELEE,
	L4D2_ACT_MP_GESTURE_VC_HANDMOUTH_ITEM1,
	L4D2_ACT_MP_GESTURE_VC_FINGERPOINT_ITEM1,
	L4D2_ACT_MP_GESTURE_VC_FISTPUMP_ITEM1,
	L4D2_ACT_MP_GESTURE_VC_THUMBSUP_ITEM1,
	L4D2_ACT_MP_GESTURE_VC_NODYES_ITEM1,
	L4D2_ACT_MP_GESTURE_VC_NODNO_ITEM1,
	L4D2_ACT_MP_GESTURE_VC_HANDMOUTH_ITEM2,
	L4D2_ACT_MP_GESTURE_VC_FINGERPOINT_ITEM2,
	L4D2_ACT_MP_GESTURE_VC_FISTPUMP_ITEM2,
	L4D2_ACT_MP_GESTURE_VC_THUMBSUP_ITEM2,
	L4D2_ACT_MP_GESTURE_VC_NODYES_ITEM2,
	L4D2_ACT_MP_GESTURE_VC_NODNO_ITEM2,
	L4D2_ACT_MP_GESTURE_VC_HANDMOUTH_BUILDING,
	L4D2_ACT_MP_GESTURE_VC_FINGERPOINT_BUILDING,
	L4D2_ACT_MP_GESTURE_VC_FISTPUMP_BUILDING,
	L4D2_ACT_MP_GESTURE_VC_THUMBSUP_BUILDING,
	L4D2_ACT_MP_GESTURE_VC_NODYES_BUILDING,
	L4D2_ACT_MP_GESTURE_VC_NODNO_BUILDING,
	L4D2_ACT_MP_GESTURE_VC_HANDMOUTH_PDA,
	L4D2_ACT_MP_GESTURE_VC_FINGERPOINT_PDA,
	L4D2_ACT_MP_GESTURE_VC_FISTPUMP_PDA,
	L4D2_ACT_MP_GESTURE_VC_THUMBSUP_PDA,
	L4D2_ACT_MP_GESTURE_VC_NODYES_PDA,
	L4D2_ACT_MP_GESTURE_VC_NODNO_PDA,
	L4D2_ACT_VM_UNUSABLE,
	L4D2_ACT_VM_UNUSABLE_TO_USABLE,
	L4D2_ACT_VM_USABLE_TO_UNUSABLE,
	L4D2_ACT_PRIMARY_VM_DRAW,
	L4D2_ACT_PRIMARY_VM_HOLSTER,
	L4D2_ACT_PRIMARY_VM_IDLE,
	L4D2_ACT_PRIMARY_VM_PULLBACK,
	L4D2_ACT_PRIMARY_VM_PRIMARYATTACK,
	L4D2_ACT_PRIMARY_VM_SECONDARYATTACK,
	L4D2_ACT_PRIMARY_VM_RELOAD,
	L4D2_ACT_PRIMARY_VM_DRYFIRE,
	L4D2_ACT_PRIMARY_VM_IDLE_TO_LOWERED,
	L4D2_ACT_PRIMARY_VM_IDLE_LOWERED,
	L4D2_ACT_PRIMARY_VM_LOWERED_TO_IDLE,
	L4D2_ACT_SECONDARY_VM_DRAW,
	L4D2_ACT_SECONDARY_VM_HOLSTER,
	L4D2_ACT_SECONDARY_VM_IDLE,
	L4D2_ACT_SECONDARY_VM_PULLBACK,
	L4D2_ACT_SECONDARY_VM_PRIMARYATTACK,
	L4D2_ACT_SECONDARY_VM_SECONDARYATTACK,
	L4D2_ACT_SECONDARY_VM_RELOAD,
	L4D2_ACT_SECONDARY_VM_DRYFIRE,
	L4D2_ACT_SECONDARY_VM_IDLE_TO_LOWERED,
	L4D2_ACT_SECONDARY_VM_IDLE_LOWERED,
	L4D2_ACT_SECONDARY_VM_LOWERED_TO_IDLE,
	L4D2_ACT_MELEE_VM_DRAW,
	L4D2_ACT_MELEE_VM_HOLSTER,
	L4D2_ACT_MELEE_VM_IDLE,
	L4D2_ACT_MELEE_VM_PULLBACK,
	L4D2_ACT_MELEE_VM_PRIMARYATTACK,
	L4D2_ACT_MELEE_VM_SECONDARYATTACK,
	L4D2_ACT_MELEE_VM_RELOAD,
	L4D2_ACT_MELEE_VM_DRYFIRE,
	L4D2_ACT_MELEE_VM_IDLE_TO_LOWERED,
	L4D2_ACT_MELEE_VM_IDLE_LOWERED,
	L4D2_ACT_MELEE_VM_LOWERED_TO_IDLE,
	L4D2_ACT_PDA_VM_DRAW,
	L4D2_ACT_PDA_VM_HOLSTER,
	L4D2_ACT_PDA_VM_IDLE,
	L4D2_ACT_PDA_VM_PULLBACK,
	L4D2_ACT_PDA_VM_PRIMARYATTACK,
	L4D2_ACT_PDA_VM_SECONDARYATTACK,
	L4D2_ACT_PDA_VM_RELOAD,
	L4D2_ACT_PDA_VM_DRYFIRE,
	L4D2_ACT_PDA_VM_IDLE_TO_LOWERED,
	L4D2_ACT_PDA_VM_IDLE_LOWERED,
	L4D2_ACT_PDA_VM_LOWERED_TO_IDLE,
	L4D2_ACT_ITEM1_VM_DRAW,
	L4D2_ACT_ITEM1_VM_HOLSTER,
	L4D2_ACT_ITEM1_VM_IDLE,
	L4D2_ACT_ITEM1_VM_PULLBACK,
	L4D2_ACT_ITEM1_VM_PRIMARYATTACK,
	L4D2_ACT_ITEM1_VM_SECONDARYATTACK,
	L4D2_ACT_ITEM1_VM_RELOAD,
	L4D2_ACT_ITEM1_VM_DRYFIRE,
	L4D2_ACT_ITEM1_VM_IDLE_TO_LOWERED,
	L4D2_ACT_ITEM1_VM_IDLE_LOWERED,
	L4D2_ACT_ITEM1_VM_LOWERED_TO_IDLE,
	L4D2_ACT_ITEM2_VM_DRAW,
	L4D2_ACT_ITEM2_VM_HOLSTER,
	L4D2_ACT_ITEM2_VM_IDLE,
	L4D2_ACT_ITEM2_VM_PULLBACK,
	L4D2_ACT_ITEM2_VM_PRIMARYATTACK,
	L4D2_ACT_ITEM2_VM_SECONDARYATTACK,
	L4D2_ACT_ITEM2_VM_RELOAD,
	L4D2_ACT_ITEM2_VM_DRYFIRE,
	L4D2_ACT_ITEM2_VM_IDLE_TO_LOWERED,
	L4D2_ACT_ITEM2_VM_IDLE_LOWERED,
	L4D2_ACT_ITEM2_VM_LOWERED_TO_IDLE
}