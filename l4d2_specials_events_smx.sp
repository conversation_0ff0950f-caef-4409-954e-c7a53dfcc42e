public PlVers:__version =
{
	version = 5,
	filevers = "1.11.0.6714",
	date = "08/29/2023",
	time = "11:35:38"
};
new Float:NULL_VECTOR[3];
new String:NULL_STRING[16];
public Extension:__ext_core =
{
	name = "Core",
	file = "core",
	autoload = 0,
	required = 0,
};
new MaxClients;
public Extension:__ext_sdktools =
{
	name = "SDKTools",
	file = "sdktools.ext",
	autoload = 1,
	required = 1,
};
public Extension:__ext_sdkhooks =
{
	name = "SDKHooks",
	file = "sdkhooks.ext",
	autoload = 1,
	required = 1,
};
public SharedPlugin:__pl_l4dh =
{
	name = "left4dhooks",
	file = "left4dhooks.smx",
	required = 1,
};
new Handle:Timer_LeftStart;
new Handle:Timer_SpecialEvents;
new Handle:Timer_ThreePanic;
new Handle:Timer_BeDizzy;
new g_sprite;
new UpdateRainNums;
new UpdatePanicNums;
new bool:g_FriendLyFire;
new bool:IsDamaged[66];
new bool:g_BeDizzy;
new bool:g_ExplodeInfected;
new bool:g_FireInfected;
new bool:g_RapidJockey;
new bool:g_StealthSmoker;
new bool:g_StealthSpitter;
new bool:IsMYZZ;
new bool:g_StealthBoomer;
new bool:IsZEPZ;
new bool:IsShouMangJiaoLuan;
new bool:IsSpitterControl;
new bool:IsTankControl;
new bool:IsWitchRbirth;
new bool:IsHunterHeal;
new bool:IsCommonPunch;
new bool:IsSmokerPunch;
new bool:IsChargerDamage;
new bool:IsTankPunch;
new bool:IsHardTank;
new bool:IsFenShen;
new String:g_sSpawnZombieClass[20][36];
new String:chuckleSound[160][16];
new bool:IsSpitterDod;
new bool:IsTimerBomb;
new bool:IsAddHeaklthTank;
new bool:IsDrugCircle;
new bool:IsAmmoLose;
new bool:IsFriendLyFireDeath;
new bool:IsIncapKongju;
new bool:IsBoomerBomb;
new bool:IsShovedDamage;
new bool:IsReviveDamage;
public Plugin:myinfo =
{
	name = "特殊事件",
	description = "特殊事件",
	author = "灵现江湖",
	version = "1.0",
	url = "qq 791347186"
};
new Old_ombie_player_l;
new Old_zombie_minion_l;
new Old_boomer_limit_l;
void:DoJump(userid)
{
	if (g_RapidJockey)
	{
		new client = GetClientOfUserId(userid);
		new var1;
		if (client && IsClientInGame(client) && GetClientTeam(client) == 3 && IsPlayerAlive(client))
		{
			new victim = GetEntPropEnt(client, 0, "m_jockeyVictim", 0);
			new var2;
			if (victim > 0 && IsClientInGame(victim) && IsPlayerAlive(victim) && client == GetEntPropEnt(victim, 0, "m_jockeyAttacker", 0))
			{
				if (GetEntPropEnt(victim, 0, "m_hGroundEntity", 0) == 0)
				{
					new Float:vel[3] = 0.0;
					GetEntPropVector(victim, 0, "m_vecBaseVelocity", vel, 0);
					vel + 8/* ERROR unknown load Binary */ += 610.0;
					TeleportEntity(victim, NULL_VECTOR, NULL_VECTOR, vel);
					CreateTimer(GetRandomFloat(0.1, 0.5), 299, userid, 0);
				}
				RequestFrame(5, userid);
			}
		}
	}
	return 0;
}

void:Event_MissionLost(Event:event, String:name[], bool:dontBroadcast)
{
	IsSpitterDod = false;
	IsTimerBomb = false;
	IsAddHeaklthTank = false;
	IsAmmoLose = false;
	IsFriendLyFireDeath = false;
	g_StealthSmoker = false;
	IsSpitterControl = false;
	IsDrugCircle = false;
	IsReviveDamage = false;
	g_StealthSpitter = false;
	g_StealthBoomer = false;
	IsHardTank = false;
	IsMYZZ = false;
	IsWitchRbirth = false;
	g_ExplodeInfected = false;
	SetConVarInt(FindConVar("survivor_incap_health"), 200, false, false);
	SetConVarInt(FindConVar("survivor_ledge_grab_health"), 100, false, false);
	SetConVarInt(FindConVar("survivor_revive_health"), 30, false, false);
	SetConVarInt(FindConVar("survivor_revive_duration"), 5, false, false);
	SetConVarInt(FindConVar("defibrillator_use_duration"), 3, false, false);
	PrintToChatAll("所有玩家已死亡，事件难度将降低");
	return 0;
}

void:OnFrame(userid)
{
	DoJump(userid);
	return 0;
}

void:TriggerMultipleDamage(entity, Float:vPos[3], Float:vMaxs[3], Float:vMins[3])
{
	new trigger = CreateEntityByName("trigger_multiple", -1);
	DispatchKeyValue(trigger, "spawnflags", "1");
	DispatchKeyValue(trigger, "entireteam", "0");
	DispatchKeyValue(trigger, "allowincap", "1");
	DispatchKeyValue(trigger, "allowghost", "0");
	SetEntityModel(trigger, "models/props/cs_militia/silo_01.mdl");
	DispatchSpawn(trigger);
	SetEntProp(trigger, 0, "m_nSolidType", 2, 4, 0);
	SetEntPropVector(trigger, 0, "m_vecMins", vMins[0], 0);
	SetEntPropVector(trigger, 0, "m_vecMaxs", vMaxs[0], 0);
	TeleportEntity(trigger, vPos[0], NULL_VECTOR, NULL_VECTOR);
	SetVariantString("!activator");
	AcceptEntityInput(trigger, "SetParent", entity, -1, 0);
	SDKHook(trigger, 8, 9);
	return 0;
}

void:OnTouchTriggerFreezer(entity, target)
{
	new var1;
	if (entity > 0 && IsValidEntity(entity) && IsSurvivor(target) && IsPlayerAlive(target))
	{
		if (HasEntProp(entity, 0, "moveparent"))
		{
			new parent = GetEntPropEnt(entity, 0, "moveparent", 0);
			new DataPack:dp = DataPack.DataPack();
			DataPack.WriteCell(dp, GetClientUserId(target), false);
			DataPack.WriteCell(dp, EntIndexToEntRef(parent), false);
			CreateTimer(10.0, 11, dp, 2);
		}
		DealDamage(target, target, 5, 0, "");
		PrintToChatAll("\x01 %N 踩中了胖胖死亡掉落的陷阱,被禁锢了!", target);
		SDKUnhook(entity, 8, 9);
		RemoveEntity(entity);
		SetEntityMoveType(target, 6);
	}
	return 0;
}

Action:Timer_UnFreeze(Handle:timer, DataPack:dp)
{
	DataPack.Reset(dp, false);
	new client = GetClientOfUserId(DataPack.ReadCell(dp));
	new entity = EntRefToEntIndex(DataPack.ReadCell(dp));
	new var1;
	if (IsSurvivor(client) && IsPlayerAlive(client))
	{
		SetEntityMoveType(client, 2);
		new var2;
		if (entity > 0 && IsValidEntity(entity))
		{
			RemoveEntity(entity);
		}
	}
	return 0;
}

void:ExecSpecialEvents()
{
	new i = 1;
	while (i <= MaxClients)
	{
		if (IsSurvivor(i))
		{
			new var1 = GetRandomInt(0, 3) << 2 + 3684;
			EmitSoundToAll(var1 + var1, i, 0, 75, 0, 1.0, 100, -1, NULL_VECTOR, NULL_VECTOR, true, 0.0);
		}
		i++;
	}
	switch (GetRandomInt(1, 60))
	{
		case 1:
		{
			FallBoomer();
		}
		case 2:
		{
			ThreePanic();
		}
		case 3:
		{
			SpawnTank();
		}
		case 4:
		{
			SpawnWitch();
		}
		case 5:
		{
			FriendLyFire();
		}
		case 6:
		{
			NoHeal();
		}
		case 7:
		{
			BeDizzy();
		}
		case 8:
		{
			ExplodeInfected();
		}
		case 9:
		{
			DeleteAmmo();
		}
		case 10:
		{
			FireInfected();
		}
		case 11:
		{
			SlowlyMan();
		}
		case 12:
		{
			StealthSpitter();
		}
		case 13:
		{
			SmokerHp();
		}
		case 14:
		{
			ZombieStronger();
		}
		case 15:
		{
			DaoDi();
		}
		case 16:
		{
			WitchKill();
		}
		case 17:
		{
			HappyCharger();
		}
		case 18:
		{
			HappySpitter();
		}
		case 19:
		{
			FriendlyHit();
		}
		case 20:
		{
			HappySmoker();
		}
		case 21:
		{
			HappyBoomer();
		}
		case 22:
		{
			RealMode();
		}
		case 23:
		{
			HardRevive();
		}
		case 24:
		{
			RapidJockey();
		}
		case 25:
		{
			StealthSmoker();
		}
		case 26:
		{
			HardHeal();
		}
		case 27:
		{
			BommerHp();
		}
		case 28:
		{
			ChargerHp();
		}
		case 29:
		{
			JockeyHp();
		}
		case 30:
		{
			HunterHp();
		}
		case 31:
		{
			LessHope();
		}
		case 32:
		{
			HeavySpiter();
		}
		case 33:
		{
			BackAttact();
		}
		case 34:
		{
			MoreZombie();
		}
		case 35:
		{
			FireTank();
		}
		case 36:
		{
			ZombiePunch();
		}
		case 37:
		{
			TankHp();
		}
		case 38:
		{
			MYZZ();
		}
		case 39:
		{
			ZEPZ();
		}
		case 40:
		{
			ChargerSpeed();
		}
		case 41:
		{
			StealthBoomer();
		}
		case 42:
		{
			HunterRush();
		}
		case 43:
		{
			SpitterDod();
		}
		case 44:
		{
			TimerBomb();
		}
		case 45:
		{
			AddHeaklthTank();
		}
		case 46:
		{
			DrugCircle();
		}
		case 47:
		{
			Headshotmode();
		}
		case 48:
		{
			HealDown();
		}
		case 49:
		{
			AmmoLose();
		}
		case 50:
		{
			FriendLyFireDeath();
		}
		case 51:
		{
			IncapKongju();
		}
		case 52:
		{
			BoomerBomb();
		}
		case 53:
		{
			WenYiTank();
		}
		case 54:
		{
			SpitterControl();
		}
		case 55:
		{
			TankControl();
		}
		case 56:
		{
			WithRbirth();
		}
		case 57:
		{
			HunterHeal();
		}
		case 58:
		{
			CommonPunch();
		}
		case 59:
		{
			HardTank();
		}
		case 60:
		{
			FenShen();
		}
		default:
		{
		}
	}
	return 0;
}

void:FenShen()
{
	IsFenShen = true;
	PrintToChatAll("☠触发特殊事件:影流之主（僵尸攻击到玩家会额外刷新一只新的小僵尸在身边，持续一局）");
	return 0;
}

void:HardTank()
{
	IsHardTank = true;
	PrintToChatAll("☠触发永久特殊事件:皮糙肉厚（tank受到低于30的攻击只会承受百分之10伤害(团灭后事件消失)）");
	return 0;
}

void:CommonPunch()
{
	IsCommonPunch = true;
	PrintToChatAll("☠触发永久特殊事件:拳拳到肉（小僵尸的攻击伤害大幅提升）");
	return 0;
}

void:HunterHeal()
{
	IsHunterHeal = true;
	PrintToChatAll("☠触发永久特殊事件:猎取养分（hunter的每次攻击，会使场中所有特感恢复50hp）");
	return 0;
}

void:WithRbirth()
{
	IsWitchRbirth = true;
	PrintToChatAll("☠触发永久特殊事件:巫蛊坦克（坦克每次击杀一个玩家，会生成一只额外的女巫(团灭后事件消失)）");
	return 0;
}

void:TankControl()
{
	IsTankControl = true;
	PrintToChatAll("☠触发特殊事件:地动山摇（坦克每次攻击到一个玩家，所有玩家都会因此无法站稳被硬直，持续一局(提示：在空中可以规避硬直)）");
	return 0;
}

void:SpitterControl()
{
	IsSpitterControl = true;
	PrintToChatAll("☠触发永久特殊事件:润滑毒液（口水的攻击会使玩家脚滑失去控制(团灭后事件消失)）");
	return 0;
}

void:WenYiTank()
{
	PrintToChatAll("☠触发永久特殊事件:瘟疫坦克（坦克会对1.6米内的玩家持续造成伤害，每3秒扣除5hp）");
	CreateTimer(3.0, 285, 0, 1);
	return 0;
}

void:BoomerBomb()
{
	IsBoomerBomb = true;
	PrintToChatAll("☠触发永久特殊事件:爆裂胖胖（Boomer死亡会造成额外的爆炸伤害，扣除20hp）");
	return 0;
}

void:IncapKongju()
{
	IsIncapKongju = true;
	PrintToChatAll("☠触发特殊事件:晕头转向（每次玩家倒地会使所有其他玩家操作打乱两秒钟，持续一局）");
	return 0;
}

void:FriendLyFireDeath()
{
	IsFriendLyFireDeath = true;
	SetConVarInt(FindConVar("survivor_friendly_fire_factor_normal"), 2, false, false);
	PrintToChatAll("☠触发特殊事件:生命连锁（玩家在本局杀死队友后自己也会死亡，且黑枪的伤害小幅度增加）");
	return 0;
}

void:AmmoLose()
{
	IsAmmoLose = true;
	PrintToChatAll("☠触发永久特殊事件:弹药窃取 (僵尸攻击到的玩家会有20概率使其武器子弹减少10发(团灭后事件消失))");
	return 0;
}

void:HealDown()
{
	new i = 1;
	while (i <= MaxClients)
	{
		new var1;
		if (IsClientInGame(i) && IsPlayerAlive(i) && GetClientTeam(i) == 2)
		{
			new j = 3;
			while (j < 5)
			{
				new ent = GetPlayerWeaponSlot(i, j);
				new var2;
				if (ent > 0 && IsValidEntity(ent))
				{
					new String:sActiveWeapon[256];
					GetEntityClassname(ent, sActiveWeapon, 64);
					if (StrEqual(sActiveWeapon, "weapon_pain_pills", true))
					{
						RemovePlayerItem(i, ent);
					}
					else
					{
						if (StrEqual(sActiveWeapon, "weapon_adrenaline", true))
						{
							RemovePlayerItem(i, ent);
						}
					}
				}
				j++;
			}
		}
		i++;
	}
	SetConVarInt(FindConVar("pain_pills_health_value"), 30, false, false);
	SetConVarInt(FindConVar("adrenaline_health_buffer"), 20, false, false);
	PrintToChatAll("☠触发永久特殊事件:治疗弱化(肾上腺素和止痛药全部将损坏，且止痛药和肾上腺素的回复量降低)");
	return 0;
}

void:Headshotmode()
{
	IsShovedDamage = true;
	PrintToChatAll("☠触发特殊事件:疼痛贴贴(玩家推到特感和队友时会收到2点伤害,持续一局)");
	return 0;
}

void:DrugCircle()
{
	IsDrugCircle = true;
	PrintToChatAll("☠触发永久特殊事件:毒雾笼罩(特感死亡后有小概率生成烟雾(团灭后事件消失))");
	return 0;
}

void:TimerBomb()
{
	IsTimerBomb = true;
	PrintToChatAll("☠触发永久特殊事件:定时炸弹(玩家死亡的时候会原地生成毒液或者胆汁爆炸(团灭后事件消失))");
	return 0;
}

void:AddHeaklthTank()
{
	IsAddHeaklthTank = true;
	PrintToChatAll("☠触发永久特殊事件:生命之拳(tank攻击到玩家每次恢复自身250hp(团灭后事件消失))");
	return 0;
}

void:SpitterDod()
{
	IsSpitterDod = true;
	PrintToChatAll("☠触发永久特殊事件:骑手保护(jockey造成伤害时获得3秒的无敌(团灭后事件消失)）");
	return 0;
}

void:HunterRush()
{
	SetConVarInt(FindConVar("z_pounce_stumble_radius"), 120, false, false);
	PrintToChatAll("☠触发永久特殊事件:肉弹冲击（Hunter和Jockey控制玩家时会对附近幸存者造成硬直）");
	return 0;
}

void:StealthBoomer()
{
	g_StealthBoomer = true;
	PrintToChatAll("☠触发永久特殊事件:忍者胖胖(bommer受伤之后会进入隐身状态(团灭后事件消失))");
	return 0;
}

void:ChargerSpeed()
{
	SetConVarInt(FindConVar("z_charge_max_force"), 1050, false, false);
	SetConVarInt(FindConVar("z_charge_min_force"), 850, false, false);
	SetConVarInt(FindConVar("z_charge_max_speed"), 12000, false, false);
	SetConVarInt(FindConVar("z_charge_warmup"), 1, false, false);
	SetConVarInt(FindConVar("z_charge_duration"), 100, false, false);
	SetConVarInt(FindConVar("z_charge_impact_radius"), 150, false, false);
	PrintToChatAll("☠触发永久特殊事件:大力神牛（charger技能持续时间极大幅度增加，撞击力度增加）");
	return 0;
}

void:ZEPZ()
{
	IsZEPZ = true;
	PrintToChatAll("☠触发永久特殊事件:憎恶胖子（boomer死亡后会掉落一个陷阱,玩家经过会扣5血并禁锢10s）");
	return 0;
}

void:MYZZ()
{
	IsMYZZ = true;
	PrintToChatAll("☠触发永久特殊事件:梦魇之爪（特感普通攻击会让幸存者持续5秒流血效果(团灭后事件消失)）");
	return 0;
}

void:TankHp()
{
	SetConVarInt(FindConVar("z_tank_speed"), 300, false, false);
	IsTankPunch = true;
	PrintToChatAll("☠触发永久特殊事件:狂暴坦克（tank的速度小幅度提升，且攻击伤害大幅提升）");
	return 0;
}

void:ZombiePunch()
{
	SetConVarInt(FindConVar("z_claw_hit_pitch_max"), 80, false, false);
	SetConVarInt(FindConVar("z_claw_hit_pitch_min"), -80, false, false);
	PrintToChatAll("☠触发特殊事件:沉重打击（玩家更容易被打晃视角，持续一局）");
	return 0;
}

void:FireTank()
{
	SetConVarInt(FindConVar("tank_burn_duration"), 300, false, false);
	SetConVarInt(FindConVar("z_special_burn_dmg_scale"), 1, false, false);
	PrintToChatAll("☠触发永久特殊事件:火抗特感（所有特感的燃烧抗性提升）");
	return 0;
}

void:MoreZombie()
{
	IsReviveDamage = true;
	PrintToChatAll("☠触发永久特殊事件:刺痛救援（玩家救起队友后自身承受5点伤害(团灭后事件消失)）");
	return 0;
}

void:BackAttact()
{
	IsChargerDamage = true;
	PrintToChatAll("☠触发永久特殊事件:暴击神拳（牛牛攻击时有50概率会造成双倍伤害）");
	return 0;
}

void:HeavySpiter()
{
	SetConVarInt(FindConVar("z_spitter_health"), 500, false, false);
	SetConVarInt(FindConVar("z_spitter_speed"), 470, false, false);
	PrintToChatAll("☠触发永久特殊事件:强化口水（spitter的生命和速度提升）");
	return 0;
}

void:LessHope()
{
	SetConVarInt(FindConVar("survivor_incap_health"), 150, false, false);
	SetConVarInt(FindConVar("survivor_ledge_grab_health"), 70, false, false);
	SetConVarInt(FindConVar("survivor_revive_health"), 25, false, false);
	PrintToChatAll("☠触发永久特殊事件:难以支撑（幸存者倒地、挂边和被救起的血量降低了(团灭后事件消失)）");
	return 0;
}

void:HunterHp()
{
	SetConVarInt(FindConVar("z_hunter_health"), 500, false, false);
	SetConVarInt(FindConVar("z_hunter_speed"), 410, false, false);
	PrintToChatAll("☠触发永久特殊事件:强化猎人（hunter的生命值和速度提升）");
	return 0;
}

void:JockeyHp()
{
	SetConVarInt(FindConVar("z_jockey_health"), 500, false, false);
	SetConVarInt(FindConVar("z_jockey_speed"), 370, false, false);
	SetConVarInt(FindConVar("z_jockey_ride_damage"), 3, false, false);
	PrintToChatAll("☠触发永久特殊事件:强化猴猴（jockey的生命值和速度提升,抓住玩家造成伤害增加）");
	return 0;
}

void:ChargerHp()
{
	SetConVarInt(FindConVar("z_charger_health"), 800, false, false);
	PrintToChatAll("☠触发永久特殊事件:肉化牛牛（charger的生命值提升）");
	return 0;
}

void:BommerHp()
{
	SetConVarInt(FindConVar("z_exploding_health"), 500, false, false);
	PrintToChatAll("☠触发永久特殊事件:肉化胖胖（bommer的生命值大幅度提升）");
	return 0;
}

void:HardHeal()
{
	SetConVarInt(FindConVar("first_aid_kit_use_duration"), 10, false, false);
	PrintToChatAll("☠触发永久特殊事件:缓慢治疗(玩家需要使用医疗包的时间大幅度提升)");
	return 0;
}

void:StealthSmoker()
{
	g_StealthSmoker = true;
	PrintToChatAll("☠触发永久特殊事件:硬直反弹(hunter被推后会反弹硬直效果给该玩家(团灭后事件消失))");
	return 0;
}

void:RapidJockey()
{
	g_RapidJockey = true;
	PrintToChatAll("☠触发永久特殊事件:深度控制(Jockey抓住幸存者之后会使用跳跃)");
	return 0;
}

void:HardRevive()
{
	SetConVarInt(FindConVar("survivor_revive_duration"), 10, false, false);
	SetConVarInt(FindConVar("defibrillator_use_duration"), 10, false, false);
	SetConVarInt(FindConVar("upgrade_pack_use_duration"), 5, false, false);
	PrintToChatAll("☠触发永久特殊事件:沉重负担(救起队友和安放弹药包需要的时间增加(团灭后事件消失))");
	return 0;
}

Float:5*0(Float:oper1, oper2)
{
	return oper1 * float(oper2);
}

void:RealMode()
{
	SetConVarInt(FindConVar("sv_disable_glow_survivors"), 1, false, false);
	PrintToChatAll("☠触发特殊事件:真实模式(玩家的轮廓将消失，持续一局)");
	return 0;
}

void:HappyBoomer()
{
	SetConVarInt(FindConVar("z_exploding_inner_radius"), 240, false, false);
	SetConVarInt(FindConVar("z_exploding_outer_radius"), 275, false, false);
	PrintToChatAll("☠触发永久特殊事件:胆汁扩散(bommer自爆范围大幅度增强)");
	return 0;
}

void:HappySmoker()
{
	SetConVarInt(FindConVar("tongue_miss_delay"), 2, false, false);
	SetConVarInt(FindConVar("tongue_hit_delay"), 2, false, false);
	SetConVarInt(FindConVar("tongue_range"), 3000, false, false);
	PrintToChatAll("☠触发永久特殊事件:王牌狙击(smoker技能CD缩短，距离大幅度增强)");
	return 0;
}

void:FriendlyHit()
{
	SetConVarInt(FindConVar("survivor_friendly_fire_factor_normal"), 3, false, false);
	PrintToChatAll("☠触发特殊事件:友军之围(黑枪队友的伤害大幅度增加了，持续一局)");
	return 0;
}

Float:5-0(Float:oper1, oper2)
{
	return oper1 - float(oper2);
}

void:SlowlyMan()
{
	SetConVarInt(FindConVar("survivor_limp_health"), 500, false, false);
	PrintToChatAll("☠触发特殊事件:行动缓慢(玩家的移速下降，持续一局)");
	return 0;
}

void:HappySpitter()
{
	SetConVarInt(FindConVar("z_spit_interval"), 3, false, false);
	SetConVarInt(FindConVar("z_spit_range"), 200, false, false);
	PrintToChatAll("☠触发永久特殊事件:口水狂欢(spitter的技能范围缩小，技能CD大幅度降低)");
	return 0;
}

void:HappyCharger()
{
	SetConVarInt(FindConVar("z_charge_interval"), 1, false, false);
	PrintToChatAll("☠触发永久特殊事件:快乐牛牛(charger的冲撞技能CD大幅度降低)");
	return 0;
}

void:WitchKill()
{
	SetConVarInt(FindConVar("z_witch_always_kills"), 1, false, false);
	SetConVarInt(FindConVar("z_witch_speed"), 9999, false, false);
	SetConVarInt(FindConVar("z_witch_speed_inured"), 9999, false, false);
	SetConVarInt(FindConVar("z_witch_health"), 2800, false, false);
	PrintToChatAll("☠触发特殊事件:女巫狂怒(witch的生命和速度永久增加，并且会在本局一下击杀玩家)");
	return 0;
}

bool:5==0(Float:oper1, oper2)
{
	return oper1 == float(oper2);
}

void:DaoDi()
{
	IsSmokerPunch = true;
	PrintToChatAll("☠触发永久特殊事件:强力触手(舌头的伤害增加)");
	return 0;
}

void:ZombieStronger()
{
	SetConVarInt(FindConVar("z_speed"), 300, false, false);
	SetConVarInt(FindConVar("z_health"), 70, false, false);
	SetConVarInt(FindConVar("z_door_pound_damage"), 300, false, false);
	PrintToChatAll("☠触发永久特殊事件:血月升起(所有僵尸的生命和速度都小幅提升了...)");
	return 0;
}

void:SmokerHp()
{
	SetConVarInt(FindConVar("z_gas_health"), 500, false, false);
	SetConVarInt(FindConVar("tongue_health"), 50, false, false);
	SetConVarInt(FindConVar("z_gas_speed"), 450, false, false);
	SetConVarInt(FindConVar("tongue_victim_max_speed"), 1000, false, false);
	PrintToChatAll("☠触发永久特殊事件:强化舌头（smoker生命值和速度提升,并且可以一瞬间拽走玩家）");
	return 0;
}

bool:5<0(Float:oper1, oper2)
{
	return oper1 < float(oper2);
}

void:StealthSpitter()
{
	g_StealthSpitter = true;
	PrintToChatAll("☠触发永久特殊事件:隐匿射手(spitter被攻击之后会进入隐匿状态(团灭后事件消失))");
	return 0;
}

void:FireInfected()
{
	g_FireInfected = true;
	PrintToChatAll("☠触发特殊事件:自焚特感（特感死后有概率产生火焰，持续一局）");
	return 0;
}

void:DeleteAmmo()
{
	SetConVarInt(FindConVar("ammo_assaultrifle_max"), 400, false, false);
	SetConVarInt(FindConVar("ammo_autoshotgun_max"), 120, false, false);
	SetConVarInt(FindConVar("ammo_shotgun_max"), 120, false, false);
	SetConVarInt(FindConVar("ammo_huntingrifle_max"), 70, false, false);
	SetConVarInt(FindConVar("ammo_grenadelauncher_max"), 20, false, false);
	SetConVarInt(FindConVar("ammo_smg_max"), 500, false, false);
	PrintToChatAll("☠触发永久特殊事件:弹药紧缺（武器的后备弹药上限降低）");
	return 0;
}

bool:5<=0(Float:oper1, oper2)
{
	return oper1 <= float(oper2);
}

void:ExplodeInfected()
{
	g_ExplodeInfected = true;
	PrintToChatAll("☠触发永久特殊事件:自爆特感（特感死后有小概率产生爆炸(团灭后事件消失)）");
	return 0;
}

void:BeDizzy()
{
	g_BeDizzy = true;
	Timer_BeDizzy = CreateTimer(15.0, 233, 0, 0);
	PrintToChatAll("☠触发特殊事件:头晕目眩（玩家的方向操控将打乱，持续15秒");
	return 0;
}

void:AddVectors(Float:vec1[3], Float:vec2[3], Float:result[3])
{
	result[0] = vec1[0] + vec2[0];
	result[0] + 4/* ERROR unknown load Binary */ = vec1[0] + 4/* ERROR unknown load Binary */ + vec2[0] + 4/* ERROR unknown load Binary */;
	result[0] + 8/* ERROR unknown load Binary */ = vec1[0] + 8/* ERROR unknown load Binary */ + vec2[0] + 8/* ERROR unknown load Binary */;
	return 0;
}

void:NoHeal()
{
	new i = 1;
	while (i <= MaxClients)
	{
		new var1;
		if (IsClientInGame(i) && IsPlayerAlive(i) && GetClientTeam(i) == 2)
		{
			new j = 3;
			while (j < 5)
			{
				new ent = GetPlayerWeaponSlot(i, j);
				new var2;
				if (ent > 0 && IsValidEntity(ent))
				{
					new String:sActiveWeapon[256];
					GetEntityClassname(ent, sActiveWeapon, 64);
					if (StrEqual(sActiveWeapon, "weapon_first_aid_kit", true))
					{
						RemovePlayerItem(i, ent);
					}
					else
					{
						if (StrEqual(sActiveWeapon, "weapon_defibrillator", true))
						{
							RemovePlayerItem(i, ent);
						}
					}
				}
				j++;
			}
		}
		i++;
	}
	PrintToChatAll("☠触发特殊事件:没有治疗（玩家的医疗包电击器将损坏，且医疗包无法补充生命，持续一局）");
	SetConVarInt(FindConVar("first_aid_heal_percent"), 0, false, false);
	return 0;
}

void:FriendLyFire()
{
	g_FriendLyFire = true;
	PrintToChatAll("☠触发特殊事件:队友克星（击中友军将会自己扣15血，持续一局）");
	return 0;
}

void:ScaleVector(Float:vec[3], Float:scale)
{
	vec[0] *= scale;
	vec[0] + 4/* ERROR unknown load Binary */ *= scale;
	vec[0] + 8/* ERROR unknown load Binary */ *= scale;
	return 0;
}

void:MakeVectorFromPoints(Float:pt1[3], Float:pt2[3], Float:output[3])
{
	output[0] = pt2[0] - pt1[0];
	output[0] + 4/* ERROR unknown load Binary */ = pt2[0] + 4/* ERROR unknown load Binary */ - pt1[0] + 4/* ERROR unknown load Binary */;
	output[0] + 8/* ERROR unknown load Binary */ = pt2[0] + 8/* ERROR unknown load Binary */ - pt1[0] + 8/* ERROR unknown load Binary */;
	return 0;
}

bool:IsIncaped(client)
{
	return GetEntProp(client, 0, "m_isIncapacitated", 1, 0);
}

SetIncapState(client, isIncapacitated)
{
	SetEntProp(client, 0, "m_isIncapacitated", isIncapacitated, 4, 0);
	SetEntityHealth(client, 200);
	return 0;
}

void:SpawnWitch()
{
	new client = iGetRandomSurvivor();
	new i;
	while (i < 5)
	{
		new flags = GetCommandFlags("z_spawn");
		SetCommandFlags("z_spawn", flags & -16385);
		FakeClientCommand(client, g_sSpawnZombieClass[7]);
		SetCommandFlags("z_spawn", flags);
		i++;
	}
	PrintToChatAll("☠触发特殊事件:女巫狂暴（witch将会随机出现在玩家附近!）");
	return 0;
}

void:SpawnTank()
{
	new client = iGetRandomSurvivor();
	new tank;
	new Float:vPos[3] = 0.0;
	new bool:b_success;
	new j = 8;
	while (0 < j)
	{
		if (L4D_GetRandomPZSpawnPosition(0, j, 8, vPos))
		{
			tank = L4D2_SpawnTank(vPos, NULL_VECTOR);
			if (tank != -1)
			{
				b_success = true;
			}
			if (!b_success)
			{
				tank = CreateSpecial(client, 8);
			}
			if (IsValidPlayer(tank, true, true))
			{
				SetEntityHealth(tank, 8000);
			}
			PrintToChatAll("☠触发特殊事件:Tank Rush!(已经生成一只8000血的tank...)");
			return 0;
		}
		j--;
	}
	if (!b_success)
	{
		tank = CreateSpecial(client, 8);
	}
	if (IsValidPlayer(tank, true, true))
	{
		SetEntityHealth(tank, 8000);
	}
	PrintToChatAll("☠触发特殊事件:Tank Rush!(已经生成一只8000血的tank...)");
	return 0;
}

void:ThreePanic()
{
	UpdatePanicNums = 0;
	TriggerPanicEvent();
	Timer_ThreePanic = CreateTimer(55.0, 279, 0, 1);
	PrintToChatAll("☠触发特殊事件:持续1分钟的尸潮!");
	return 0;
}

void:TriggerPanicEvent()
{
	new flager = iGetRandomSurvivor();
	if (flager == 0)
	{
		return 0;
	}
	new flag = GetCommandFlags("director_force_panic_event");
	SetCommandFlags("director_force_panic_event", flag & -16385);
	FakeClientCommand(flager, "director_force_panic_event");
	return 0;
}

void:FallBoomer()
{
	UpdateRainNums = 0;
	Old_ombie_player_l = GetConVarInt(FindConVar("z_max_player_zombies"));
	Old_zombie_minion_l = GetConVarInt(FindConVar("z_minion_limit"));
	Old_boomer_limit_l = GetConVarInt(FindConVar("z_boomer_limit"));
	new Handle:zombie_player_l = FindConVar("z_max_player_zombies");
	SetConVarBounds(zombie_player_l, 0, true, 32.0);
	SetConVarBounds(FindConVar("z_max_player_zombies"), 0, false, 0.0);
	new Handle:zombie_minion_l = FindConVar("z_minion_limit");
	SetConVarBounds(zombie_minion_l, 0, true, 32.0);
	SetConVarInt(FindConVar("z_boomer_limit"), 32, false, false);
	CreateTimer(0.2, 291, 0, 1);
	PrintToChatAll("☠触发特殊事件:天降boomer!");
	return 0;
}

bool:StrEqual(String:str1[], String:str2[], bool:caseSensitive)
{
	return strcmp(str1[0], str2[0], caseSensitive) == 0;
}

void:PrintToChatAll(String:format[], any:_arg1)
{
	new String:buffer[1024];
	new i = 1;
	while (i <= MaxClients)
	{
		if (IsClientInGame(i))
		{
			SetGlobalTransTarget(i);
			VFormat(buffer, 254, format[0], 2);
			PrintToChat(i, "%s", buffer);
		}
		i++;
	}
	return 0;
}

void:SetGodMode(client, Float:duration)
{
	if (!IsClientInGame(client))
	{
		return 0;
	}
	SetEntProp(client, 1, "m_takedamage", 0, 1, 0);
	if (duration > 0.0)
	{
		CreateTimer(duration, 287, GetClientUserId(client), 0);
	}
	return 0;
}

SelectAndidata(team, client, Float:hitpos[3])
{
	new selected;
	new andidate[66];
	new index;
	if (client > 0)
	{
		index++;
		andidate[index << 2] = client;
	}
	else
	{
		new i = 1;
		while (i <= MaxClients)
		{
			new var1;
			if (IsClientInGame(i) && IsPlayerAlive(i) && team == GetClientTeam(i))
			{
				index++;
				andidate[index << 2] = i;
			}
			i++;
		}
	}
	while (index > 0)
	{
		new c = GetRandomInt(0, index + -1);
		new Float:v[3] = 0.0;
		new Float:pos[3] = 0.0;
		v = GetRandomFloat(-20.0, 20.0) + 0.0;
		v + 4/* ERROR unknown load Binary */ = GetRandomFloat(-20.0, 20.0) + 0.0;
		v + 8/* ERROR unknown load Binary */ = 1114636288;
		GetVectorAngles(v, v);
		GetClientEyePosition(andidate[c << 2], pos);
		GetRayHitPos2(pos, v, hitpos[0], andidate[c << 2]);
		new Float:distance = GetVectorDistance(pos, hitpos[0], false);
		ShowLarserByPos(pos, hitpos[0], 0, 12.5);
		if (distance > 400.0)
		{
			distance -= 100.0;
			if (distance > 2000.0)
			{
				distance = 2000.0;
			}
			MakeVectorFromPoints(pos, hitpos[0], v);
			NormalizeVector(v, v);
			ScaleVector(v, distance);
			AddVectors(pos, v, hitpos[0]);
			selected = andidate[c << 2];
			return selected;
		}
		index--;
		andidate[c << 2] = andidate[index << 2];
	}
	return selected;
}

GetEntSendPropOffs(ent, String:prop[], bool:actual)
{
	new String:cls[256];
	if (!GetEntityNetClass(ent, cls, 64))
	{
		return -1;
	}
	new local = -1;
	new offset = FindSendPropInfo(cls, prop[0], 0, 0, local);
	if (actual)
	{
		return offset;
	}
	return local;
}

GetRayHitPos2(Float:pos[3], Float:angle[3], Float:hitpos[3], ent)
{
	new Handle:trace;
	new hit;
	trace = TR_TraceRayFilterEx(pos[0], angle[0], 33570827, 1, 289, ent);
	if (TR_DidHit(trace))
	{
		TR_GetEndPosition(hitpos[0], trace);
		hit = TR_GetEntityIndex(trace);
	}
	CloseHandle(trace);
	return hit;
}

void:ShowLarserByPos(Float:pos1[3], Float:pos2[3], flag, Float:life)
{
	new color[4];
	if (flag == 0)
	{
		color = 200;
		color + 4/* ERROR unknown load Binary */ = 200;
		color + 8/* ERROR unknown load Binary */ = 200;
		color + 12/* ERROR unknown load Binary */ = 230;
	}
	else
	{
		color = 200;
		color + 4/* ERROR unknown load Binary */ = 0;
		color + 8/* ERROR unknown load Binary */ = 0;
		color + 12/* ERROR unknown load Binary */ = 230;
	}
	new Float:width1 = 0.5;
	new Float:width2 = 0.3;
	width2 = 0.3;
	TE_SetupBeamPoints(pos1[0], pos2[0], g_sprite, 0, 0, 0, life, width1, width2, 1, 0.0, color, 0);
	TE_SendToAll(0.0);
	return 0;
}

iGetRandomSurvivor()
{
	new iSurvivorCount;
	new iSurvivors[66];
	new iSurvivor = 1;
	while (iSurvivor <= MaxClients)
	{
		new var1;
		if (IsClientInGame(iSurvivor) && GetClientTeam(iSurvivor) == 2)
		{
			iSurvivorCount++;
			iSurvivors[iSurvivorCount << 2] = iSurvivor;
		}
		iSurvivor++;
	}
	return iSurvivors[GetRandomInt(0, iSurvivorCount - 1) << 2];
}

bool:HasEntProp(entity, PropType:type, String:prop[])
{
	if (type == 1)
	{
		return FindDataMapInfo(entity, prop[0], 0, 0, 0) != -1;
	}
	if (type != 0)
	{
		return 0;
	}
	new String:cls[256];
	if (!GetEntityNetClass(entity, cls, 64))
	{
		return 0;
	}
	return FindSendPropInfo(cls, prop[0], 0, 0, 0) != -1;
}

bool:LeftStartArea()
{
	new ent = -1;
	new maxents = GetMaxEntities();
	new i = MaxClients + 1;
	while (i <= maxents)
	{
		if (IsValidEntity(i))
		{
			new String:netclass[256];
			GetEntityNetClass(i, netclass, 64);
			if (StrEqual(netclass, "CTerrorPlayerResource", true))
			{
				ent = i;
				if (ent > -1)
				{
					new offset = FindSendPropInfo("CTerrorPlayerResource", "m_hasAnySurvivorLeftSafeArea", 0, 0, 0);
					if (offset > 0)
					{
						if (GetEntData(ent, offset, 4))
						{
							if (GetEntData(ent, offset, 4) == 1)
							{
								return 1;
							}
						}
					}
				}
				return 0;
			}
		}
		i++;
	}
	if (ent > -1)
	{
		new offset = FindSendPropInfo("CTerrorPlayerResource", "m_hasAnySurvivorLeftSafeArea", 0, 0, 0);
		if (offset > 0)
		{
			if (GetEntData(ent, offset, 4))
			{
				if (GetEntData(ent, offset, 4) == 1)
				{
					return 1;
				}
			}
		}
	}
	return 0;
}

bool:IsValidClient(client)
{
	new var1;
	return client > 0 && client <= MaxClients && IsClientInGame(client);
}

bool:IsSurvivor(client)
{
	new var1;
	return client > 0 && client <= MaxClients && IsClientInGame(client) && GetClientTeam(client) == 2;
}

bool:IsInfected(client)
{
	new var1;
	return client > 0 && client <= MaxClients && IsClientInGame(client) && GetClientTeam(client) == 3;
}

bool:IsCommonInfected(iEntity)
{
	new var1;
	if (iEntity > 0 && IsValidEntity(iEntity) && IsValidEdict(iEntity))
	{
		decl String:strClassName[256];
		GetEdictClassname(iEntity, strClassName, 64);
		return StrEqual(strClassName, "infected", true);
	}
	return 0;
}

bool:IsWitch(iEntity)
{
	new var1;
	if (iEntity > 0 && IsValidEntity(iEntity) && IsValidEdict(iEntity))
	{
		decl String:strClassName[256];
		GetEdictClassname(iEntity, strClassName, 64);
		return StrEqual(strClassName, "witch", true);
	}
	return 0;
}

DealDamage(attacker, victim, damage, dmg_type, String:weapon[])
{
	new var1;
	if (IsValidEdict(victim) && damage > 0)
	{
		new String:victimid[256];
		new String:dmg_type_str[128];
		IntToString(dmg_type, dmg_type_str, 32);
		new PointHurt = CreateEntityByName("point_hurt", -1);
		if (PointHurt)
		{
			Format(victimid, 64, "victim%d", victim);
			DispatchKeyValue(victim, "targetname", victimid);
			DispatchKeyValue(PointHurt, "DamageTarget", victimid);
			DispatchKeyValueFloat(PointHurt, "Damage", float(damage));
			DispatchKeyValue(PointHurt, "DamageType", dmg_type_str);
			if (!StrEqual(weapon[0], "", true))
			{
				DispatchKeyValue(PointHurt, "classname", weapon[0]);
			}
			DispatchSpawn(PointHurt);
			if (IsValidPlayer(attacker, true, true))
			{
				AcceptEntityInput(PointHurt, "Hurt", attacker, -1, 0);
			}
			else
			{
				AcceptEntityInput(PointHurt, "Hurt", -1, -1, 0);
			}
			RemoveEdict(PointHurt);
		}
	}
	return 0;
}

CheatCommand(Client, String:command[], String:arguments[])
{
	if (!Client)
	{
		return 0;
	}
	new admindata = GetUserFlagBits(Client);
	SetUserFlagBits(Client, 16384);
	new flags = GetCommandFlags(command[0]);
	SetCommandFlags(command[0], flags & -16385);
	FakeClientCommand(Client, "%s %s", command[0], arguments[0]);
	SetCommandFlags(command[0], flags);
	SetUserFlagBits(Client, admindata);
	return 0;
}

bool:IsValidPlayer(Client, bool:AllowBot, bool:AllowDeath)
{
	new var1;
	if (Client < 1 || Client > MaxClients)
	{
		return 0;
	}
	new var2;
	if (!IsClientConnected(Client) || !IsClientInGame(Client))
	{
		return 0;
	}
	if (!AllowBot)
	{
		if (IsFakeClient(Client))
		{
			return 0;
		}
	}
	if (!AllowDeath)
	{
		if (!IsPlayerAlive(Client))
		{
			return 0;
		}
	}
	return 1;
}

void:vSpawnBreakProp(client, Float:pos[3], Float:offset, String:model[])
{
	new iProp = CreateEntityByName("prop_physics", -1);
	if (bIsValidEntity(iProp))
	{
		DispatchKeyValue(iProp, "disableshadows", "1");
		SetEntityModel(iProp, model[0]);
		pos[0] + 8/* ERROR unknown load Binary */ += offset;
		TeleportEntity(iProp, pos[0], NULL_VECTOR, NULL_VECTOR);
		DispatchSpawn(iProp);
		SetEntPropEnt(iProp, 0, "m_hOwnerEntity", client, 0);
		SetEntPropEnt(iProp, 1, "m_hPhysicsAttacker", client, 0);
		SetEntPropFloat(iProp, 1, "m_flLastPhysicsInfluenceTime", GetGameTime(), 0);
		SetEntProp(iProp, 0, "m_CollisionGroup", 1, 4, 0);
		SetEntityRenderMode(iProp, 1);
		SetEntityRenderColor(iProp, 0, 0, 0, 0);
		AcceptEntityInput(iProp, "Break", client, -1, 0);
	}
	return 0;
}

bool:GetEntityClassname(entity, String:clsname[], maxlength)
{
	return !!GetEntPropString(entity, 1, "m_iClassname", clsname[0], maxlength, 0);
}

bool:bIsValidEntity(entity)
{
	new var1;
	return entity > MaxClients && IsValidEntity(entity);
}

bool:IsCharger(client)
{
	if (!IsInfected(client))
	{
		return 0;
	}
	if (GetEntProp(client, 0, "m_zombieClass", 4, 0) != 6)
	{
		return 0;
	}
	return 1;
}

bool:IsSpitter(client)
{
	if (!IsInfected(client))
	{
		return 0;
	}
	if (GetEntProp(client, 0, "m_zombieClass", 4, 0) != 4)
	{
		return 0;
	}
	return 1;
}

void:SetEntityMoveType(entity, MoveType:mt)
{
	static bool:gotconfig;
	static String:datamap[128];
	if (!gotconfig)
	{
		new GameData:gc = GameData.GameData("core.games");
		new bool:exists = GameData.GetKeyValue(gc, "m_MoveType", datamap, 32);
		CloseHandle(gc);
		gc = 0;
		if (!exists)
		{
			strcopy(datamap, 32, "m_MoveType");
		}
		gotconfig = true;
	}
	SetEntProp(entity, 1, datamap, mt, 4, 0);
	return 0;
}

bool:IsHunter(client)
{
	if (!IsInfected(client))
	{
		return 0;
	}
	if (GetEntProp(client, 0, "m_zombieClass", 4, 0) != 3)
	{
		return 0;
	}
	return 1;
}

bool:IsSmoker(client)
{
	if (!IsInfected(client))
	{
		return 0;
	}
	if (GetEntProp(client, 0, "m_zombieClass", 4, 0) != 1)
	{
		return 0;
	}
	return 1;
}

bool:IsBoomer(client)
{
	if (!IsInfected(client))
	{
		return 0;
	}
	if (GetEntProp(client, 0, "m_zombieClass", 4, 0) != 2)
	{
		return 0;
	}
	return 1;
}

bool:IsTank(client)
{
	if (!IsInfected(client))
	{
		return 0;
	}
	if (GetEntProp(client, 0, "m_zombieClass", 4, 0) != 8)
	{
		return 0;
	}
	return 1;
}

bool:IsJockey(client)
{
	if (!IsInfected(client))
	{
		return 0;
	}
	if (GetEntProp(client, 0, "m_zombieClass", 4, 0) != 5)
	{
		return 0;
	}
	return 1;
}

L4D2_GetSurvivorVictim(client)
{
	new victim = GetEntPropEnt(client, 0, "m_pummelVictim", 0);
	if (victim > 0)
	{
		return victim;
	}
	victim = GetEntPropEnt(client, 0, "m_carryVictim", 0);
	if (victim > 0)
	{
		return victim;
	}
	victim = GetEntPropEnt(client, 0, "m_pounceVictim", 0);
	if (victim > 0)
	{
		return victim;
	}
	victim = GetEntPropEnt(client, 0, "m_tongueVictim", 0);
	if (victim > 0)
	{
		return victim;
	}
	victim = GetEntPropEnt(client, 0, "m_jockeyVictim", 0);
	if (victim > 0)
	{
		return victim;
	}
	return -1;
}

void:SetEntityRenderMode(entity, RenderMode:mode)
{
	static bool:gotconfig;
	static String:prop[128];
	if (!gotconfig)
	{
		new GameData:gc = GameData.GameData("core.games");
		new bool:exists = GameData.GetKeyValue(gc, "m_nRenderMode", prop, 32);
		CloseHandle(gc);
		gc = 0;
		if (!exists)
		{
			strcopy(prop, 32, "m_nRenderMode");
		}
		gotconfig = true;
	}
	SetEntProp(entity, 0, prop, mode, 1, 0);
	return 0;
}

void:SetEntityRenderColor(entity, r, g, b, a)
{
	static bool:gotconfig;
	static String:prop[128];
	if (!gotconfig)
	{
		new GameData:gc = GameData.GameData("core.games");
		new bool:exists = GameData.GetKeyValue(gc, "m_clrRender", prop, 32);
		CloseHandle(gc);
		gc = 0;
		if (!exists)
		{
			strcopy(prop, 32, "m_clrRender");
		}
		gotconfig = true;
	}
	new offset = GetEntSendPropOffs(entity, prop, false);
	if (offset <= 0)
	{
		ThrowError("SetEntityRenderColor not supported by this mod");
	}
	SetEntData(entity, offset, r, 1, true);
	SetEntData(entity, offset + 1, g, 1, true);
	SetEntData(entity, offset + 2, b, 1, true);
	SetEntData(entity, offset + 3, a, 1, true);
	return 0;
}

void:SetEntityHealth(entity, amount)
{
	static bool:gotconfig;
	static String:prop[128];
	if (!gotconfig)
	{
		new GameData:gc = GameData.GameData("core.games");
		new bool:exists = GameData.GetKeyValue(gc, "m_iHealth", prop, 32);
		CloseHandle(gc);
		gc = 0;
		if (!exists)
		{
			strcopy(prop, 32, "m_iHealth");
		}
		gotconfig = true;
	}
	new String:cls[256];
	new PropFieldType:type;
	new offset;
	if (!GetEntityNetClass(entity, cls, 64))
	{
		ThrowError("SetEntityHealth not supported by this mod: Could not get serverclass name");
		return 0;
	}
	offset = FindSendPropInfo(cls, prop, type, 0, 0);
	if (offset <= 0)
	{
		ThrowError("SetEntityHealth not supported by this mod");
		return 0;
	}
	if (type == 2)
	{
		SetEntDataFloat(entity, offset, float(amount), false);
	}
	else
	{
		SetEntProp(entity, 0, prop, amount, 4, 0);
	}
	return 0;
}

void:EmitSoundToClient(client, String:sample[], entity, channel, level, flags, Float:volume, pitch, speakerentity, Float:origin[3], Float:dir[3], bool:updatePos, Float:soundtime)
{
	new clients[1] = client;
	new var1;
	if (entity == -2)
	{
		var1 = client;
	}
	else
	{
		var1 = entity;
	}
	entity = var1;
	EmitSound(clients, 1, sample[0], entity, channel, level, flags, volume, pitch, speakerentity, origin[0], dir[0], updatePos, soundtime);
	return 0;
}

void:EmitSoundToAll(String:sample[], entity, channel, level, flags, Float:volume, pitch, speakerentity, Float:origin[3], Float:dir[3], bool:updatePos, Float:soundtime)
{
	new clients[MaxClients];
	new total;
	new i = 1;
	while (i <= MaxClients)
	{
		if (IsClientInGame(i))
		{
			total++;
			total << 2 + clients[0]/* ERROR unknown load Binary */ = i;
		}
		i++;
	}
	if (total)
	{
		EmitSound(clients[0], total, sample[0], entity, channel, level, flags, volume, pitch, speakerentity, origin[0], dir[0], updatePos, soundtime);
	}
	return 0;
}

void:TE_SendToAll(Float:delay)
{
	new total;
	new clients[MaxClients];
	new i = 1;
	while (i <= MaxClients)
	{
		if (IsClientInGame(i))
		{
			total++;
			total << 2 + clients[0]/* ERROR unknown load Binary */ = i;
		}
		i++;
	}
	TE_Send(clients[0], total, delay);
	return 0;
}

void:TE_SetupBeamPoints(Float:start[3], Float:end[3], ModelIndex, HaloIndex, StartFrame, FrameRate, Float:Life, Float:Width, Float:EndWidth, FadeLength, Float:Amplitude, Color[4], Speed)
{
	TE_Start("BeamPoints");
	TE_WriteVector("m_vecStartPoint", start[0]);
	TE_WriteVector("m_vecEndPoint", end[0]);
	TE_WriteNum("m_nModelIndex", ModelIndex);
	TE_WriteNum("m_nHaloIndex", HaloIndex);
	TE_WriteNum("m_nStartFrame", StartFrame);
	TE_WriteNum("m_nFrameRate", FrameRate);
	TE_WriteFloat("m_fLife", Life);
	TE_WriteFloat("m_fWidth", Width);
	TE_WriteFloat("m_fEndWidth", EndWidth);
	TE_WriteFloat("m_fAmplitude", Amplitude);
	TE_WriteNum("r", Color[0]);
	TE_WriteNum("g", Color[0] + 4/* ERROR unknown load Binary */);
	TE_WriteNum("b", Color[0] + 8/* ERROR unknown load Binary */);
	TE_WriteNum("a", Color[0] + 12/* ERROR unknown load Binary */);
	TE_WriteNum("m_nSpeed", Speed);
	TE_WriteNum("m_nFadeLength", FadeLength);
	return 0;
}

public APLRes:AskPluginLoad2(Handle:myself, bool:late, String:error[], err_max)
{
	new EngineVersion:version = GetEngineVersion();
	if (version != 7)
	{
		strcopy(error[0], err_max, "只能用在求生2.");
		return 2;
	}
	return 0;
}

public Action:BeDizzy_Stop(Handle:timer)
{
	g_BeDizzy = false;
	Timer_BeDizzy = 0;
	PrintToChatAll("头晕目眩效果结束");
	return 0;
}

public CreateSpecial(client, L4d2_ZombieClass:ZombieClass)
{
	new tanks;
	new bool:exsit[66];
	new i = 1;
	while (i <= MaxClients)
	{
		exsit[i << 2] = false;
		new var1;
		if (IsClientInGame(i) && GetClientTeam(i) == 3 && IsPlayerAlive(i))
		{
			if (ZombieClass == GetEntProp(i, 0, "m_zombieClass", 4, 0))
			{
				exsit[i << 2] = true;
			}
		}
		i++;
	}
	if (ZombieClass == 2)
	{
		new flags = GetCommandFlags("z_spawn");
		SetCommandFlags("z_spawn", flags & -16385);
		new var3 = ZombieClass << 2 + 3468;
		FakeClientCommand(client, var3 + var3);
		SetCommandFlags("z_spawn", flags);
	}
	else
	{
		new flags = GetCommandFlags("z_spawn_old");
		SetCommandFlags("z_spawn_old", flags & -16385);
		new var4 = ZombieClass << 2 + 3468;
		FakeClientCommand(client, var4 + var4);
		SetCommandFlags("z_spawn_old", flags);
	}
	new i = 1;
	while (i <= MaxClients)
	{
		new var2;
		if (IsClientInGame(i) && GetClientTeam(i) == 3 && IsPlayerAlive(i))
		{
			if (ZombieClass == GetEntProp(i, 0, "m_zombieClass", 4, 0))
			{
				if (exsit[i << 2] == false)
				{
					tanks = i;
					return tanks;
				}
			}
		}
		i++;
	}
	return tanks;
}

public void:Event_Incapacitate(Event:event, String:name[], bool:dontBroadcast)
{
	new victim = GetClientOfUserId(Event.GetInt(event, "userid", 0));
	new var1;
	if (IsIncapKongju && IsSurvivor(victim))
	{
		g_BeDizzy = true;
		Timer_BeDizzy = CreateTimer(2.0, 233, 0, 0);
		PrintToChatAll("\x01 %N 倒地的时候压在了你的键盘上面ww", victim);
	}
	return 0;
}

public void:Event_JockeyRide(Event:event, String:name[], bool:dontBroadcast)
{
	if (g_RapidJockey)
	{
		new userid = Event.GetInt(event, "userid", 0);
		if (userid)
		{
			new client = GetClientOfUserId(userid);
			new var1;
			if (client && IsClientInGame(client) && GetClientTeam(client) == 3)
			{
				CreateTimer(GetRandomFloat(0.1, 0.5), 299, userid, 0);
			}
		}
	}
	return 0;
}

public Event_PlayerBoomed(Handle:event, String:event_name[], bool:dontBroadcast)
{
	new client = GetClientOfUserId(GetEventInt(event, "userid", 0));
	new bool:exploded = GetEventBool(event, "exploded", false);
	new var1;
	if (IsBoomerBomb && exploded && IsSurvivor(client) && IsPlayerAlive(client))
	{
		DealDamage(0, client, 20, 0, "");
		PrintHintText(client, "小心！你离胖胖太近了！被炸到了20hp！");
	}
	return 0;
}

public void:Event_PlayerHurt(Event:event, String:name[], bool:dontBroadcast)
{
	new victim = GetClientOfUserId(Event.GetInt(event, "userid", 0));
	new attacker = GetClientOfUserId(Event.GetInt(event, "attacker", 0));
	new entity = GetEventInt(event, "attackerentid", 0);
	new var1;
	if (IsAmmoLose && entity > MaxClients && IsCommonInfected(entity) && GetEntProp(entity, 1, "m_iHealth", 4, 0) > 0 && IsSurvivor(victim))
	{
		new rnd = GetRandomInt(1, 100);
		if (rnd <= 20)
		{
			new ent = GetPlayerWeaponSlot(victim, 0);
			new var2;
			if (ent > 0 && IsValidEntity(ent))
			{
				new iOffset = GetEntProp(ent, 0, "m_iPrimaryAmmoType", 0, 0) * 4;
				new iAmmoTable = FindSendPropInfo("CTerrorPlayer", "m_iAmmo", 0, 0, 0);
				new clipammo = GetEntProp(ent, 0, "m_iClip1", 4, 0);
				new allAmmo = GetEntData(victim, iOffset + iAmmoTable, 4);
				if (allAmmo >= 10)
				{
					SetEntData(victim, iOffset + iAmmoTable, allAmmo - 10, 4, true);
				}
				else
				{
					SetEntData(victim, iOffset + iAmmoTable, 0, 4, true);
					new other = 10 - allAmmo;
					if (clipammo >= other)
					{
						SetEntProp(ent, 0, "m_iClip1", clipammo - other, 4, 0);
					}
					else
					{
						SetEntProp(ent, 0, "m_iClip1", 0, 4, 0);
					}
				}
			}
		}
	}
	new var3;
	if (IsSpitter(victim) && IsSurvivor(attacker) && g_StealthSpitter)
	{
		SetEntityRenderMode(victim, 1);
		SetEntityRenderColor(victim, 255, 255, 255, 0);
	}
	new var4;
	if (IsHunterHeal && IsSurvivor(victim) && IsHunter(attacker))
	{
		PrintHintText(victim, "hunter正在窃取你的血给全部特感恢复生命...");
		new i = 1;
		while (i <= MaxClients)
		{
			new var5;
			if (IsClientInGame(i) && IsPlayerAlive(i) && GetClientTeam(i) == 3)
			{
				Heal_Player(i, 50);
			}
			i++;
		}
	}
	new var6;
	if (IsBoomer(victim) && IsSurvivor(attacker) && g_StealthBoomer)
	{
		SetEntityRenderMode(victim, 1);
		SetEntityRenderColor(victim, 255, 255, 255, 0);
	}
	new var7;
	if (IsJockey(attacker) && IsSurvivor(victim) && IsSpitterDod)
	{
		SetEntProp(attacker, 1, "m_takedamage", 0, 1, 0);
		SetGodMode(attacker, 3.0);
	}
	new var8;
	if (IsSpitter(attacker) && IsSurvivor(victim) && IsSpitterControl)
	{
		L4D_StaggerPlayer(victim, attacker, NULL_VECTOR);
	}
	new var9;
	if (IsTank(attacker) && IsSurvivor(victim) && IsTankControl)
	{
		PrintToChatAll("\x01 %N 被坦克攻击之后地表发生了颤抖!", victim);
		new i = 1;
		while (i <= MaxClients)
		{
			new var10;
			if (IsClientInGame(i) && IsPlayerAlive(i) && GetClientTeam(i) == 2)
			{
				L4D_StaggerPlayer(i, attacker, NULL_VECTOR);
				EmitSoundToClient(i, "physics/destruction/smash_cave_woodrockcollapse2.wav", -2, 0, 75, 0, 1.0, 100, -1, NULL_VECTOR, NULL_VECTOR, true, 0.0);
			}
			i++;
		}
	}
	new var11;
	if (IsTank(attacker) && IsSurvivor(victim))
	{
		if (IsAddHeaklthTank)
		{
			Heal_Player(attacker, 250);
		}
	}
	return 0;
}

public Action:Event_player_death(Event:event, String:name[], bool:dontBroadcast)
{
	new client = GetClientOfUserId(GetEventInt(event, "userid", 0));
	new attacker = GetClientOfUserId(Event.GetInt(event, "attacker", 0));
	new var1;
	if (IsValidClient(client) && !IsFakeClient(client))
	{
		if (IsTimerBomb)
		{
			new Float:flPos[3] = 0.0;
			GetEntPropVector(client, 0, "m_vecOrigin", flPos, 0);
			PrintToChatAll("\x01 %N 死亡后身上爆出了粘稠的液体...", client);
			switch (GetRandomInt(0, 1))
			{
				case 0:
				{
					new boomer = L4D2_SpawnSpecial(2, flPos, NULL_VECTOR);
					if (boomer > 0)
					{
						SDKHooks_TakeDamage(boomer, client, client, 10000.0, 0, -1, NULL_VECTOR, NULL_VECTOR);
					}
				}
				case 1:
				{
					new spitter = L4D2_SpawnSpecial(4, flPos, NULL_VECTOR);
					if (spitter > 0)
					{
						SDKHooks_TakeDamage(spitter, client, client, 10000.0, 0, -1, NULL_VECTOR, NULL_VECTOR);
						KickClient(spitter, "");
					}
				}
				default:
				{
				}
			}
		}
	}
	new var2;
	if (IsFriendLyFireDeath && IsSurvivor(client) && IsSurvivor(attacker))
	{
		ForcePlayerSuicide(attacker);
		PrintToChatAll("\x01 %N 杀死了一名玩家之后自己也受到了死亡制裁!", attacker);
	}
	new var3;
	if (IsWitchRbirth && IsSurvivor(client) && IsTank(attacker))
	{
		CheatCommand(attacker, "z_spawn", "witch");
		PrintToChatAll("\x01 %N 被坦克包装成可爱的女巫~", client);
	}
	if (!IsInfected(client))
	{
		return 0;
	}
	if (g_ExplodeInfected)
	{
		if (GetRandomInt(1, 100) <= 22)
		{
			new Float:flPos[3] = 0.0;
			GetEntPropVector(client, 0, "m_vecOrigin", flPos, 0);
			vSpawnBreakProp(client, flPos, 30.0, "models/props_junk/propanecanister001a.mdl");
		}
	}
	if (g_FireInfected)
	{
		if (GetRandomInt(1, 100) <= 25)
		{
			new Float:flPos[3] = 0.0;
			GetEntPropVector(client, 0, "m_vecOrigin", flPos, 0);
			vSpawnBreakProp(client, flPos, 30.0, "models/props_junk/gascan001a.mdl");
		}
	}
	if (IsZEPZ)
	{
		if (GetEntProp(client, 0, "m_zombieClass", 4, 0) == 2)
		{
			new Float:flPos[3] = 0.0;
			GetEntPropVector(client, 0, "m_vecOrigin", flPos, 0);
			new iEntity = CreateEntityByName("prop_dynamic_override", -1);
			DispatchKeyValue(iEntity, "model", "models/infected/limbs/exploded_boomer.mdl");
			DispatchKeyValue(iEntity, "solid", "0");
			DispatchKeyValue(iEntity, "spawnflags", "256");
			DispatchSpawn(iEntity);
			SetEntProp(iEntity, 1, "m_iEFlags", GetEntProp(iEntity, 1, "m_iEFlags", 4, 0) | 33554432 | 512, 4, 0);
			SetEntProp(iEntity, 0, "m_nSolidType", 6, 1, 0);
			TeleportEntity(iEntity, flPos, NULL_VECTOR, NULL_VECTOR);
			new Float:vPos[3] = 0.0;
			new Float:vMins[3] = 0.0;
			new Float:vMaxs[3] = 0.0;
			GetEntPropVector(iEntity, 0, "m_vecOrigin", vPos, 0);
			GetEntPropVector(iEntity, 0, "m_vecMaxs", vMaxs, 0);
			GetEntPropVector(iEntity, 0, "m_vecMins", vMins, 0);
			vMaxs + 8/* ERROR unknown load Binary */ -= 30.0;
			TriggerMultipleDamage(iEntity, vPos, vMaxs, vMins);
		}
	}
	if (IsDrugCircle)
	{
		if (GetRandomInt(1, 100) <= 12)
		{
			new Float:flPos[3] = 0.0;
			GetEntPropVector(client, 0, "m_vecOrigin", flPos, 0);
			new smoker = L4D2_SpawnSpecial(1, flPos, NULL_VECTOR);
			if (smoker > 0)
			{
				SDKHooks_TakeDamage(smoker, client, client, 10000.0, 0, -1, NULL_VECTOR, NULL_VECTOR);
				KickClient(smoker, "");
			}
		}
	}
	return 0;
}

public Float:GetTempHealth(client)
{
	new var1;
	if (IsClientConnected(client) && IsClientInGame(client))
	{
		new Float:fullTemp = GetEntPropFloat(client, 0, "m_healthBuffer", 0);
		new Float:tempStarted = GetEntPropFloat(client, 0, "m_healthBufferTime", 0);
		new Float:decayRate = GetConVarFloat(FindConVar("pain_pills_decay_rate"));
		new Float:tempDuration = 5*0(tempStarted - GetGameTime(), -1);
		new Float:fTempHp = fullTemp - tempDuration * decayRate;
		if (fTempHp < 0.0)
		{
			fTempHp = 0.0;
		}
		return fTempHp;
	}
	return 0;
}

public void:Heal_Player(entity, heal)
{
	new maxhealth = GetEntProp(entity, 1, "m_iMaxHealth", 4, 0);
	new health = GetClientHealth(entity);
	if (heal + health > maxhealth)
	{
		SetEntityHealth(entity, maxhealth);
	}
	else
	{
		SetEntityHealth(entity, heal + health);
	}
	return 0;
}

public void:OnClientDisconnect(Client)
{
	Client << 2 + 3124/* ERROR unknown load Binary */ = 0;
	SDKUnhook(Client, 2, 267);
	return 0;
}

public void:OnClientPutInServer(Client)
{
	Client << 2 + 3124/* ERROR unknown load Binary */ = 0;
	SDKHook(Client, 2, 267);
	return 0;
}

public void:OnGamemode(String:sOutput[], iCaller, iActivator, Float:fDelay)
{
	new iCvar_CurrentMode;
	if (strcmp(sOutput[0], "OnCoop", true) == 0)
	{
		iCvar_CurrentMode = 1;
	}
	else
	{
		if (strcmp(sOutput[0], "OnSurvival", true) == 0)
		{
			iCvar_CurrentMode = 2;
		}
		if (strcmp(sOutput[0], "OnVersus", true) == 0)
		{
			iCvar_CurrentMode = 4;
		}
		if (strcmp(sOutput[0], "OnScavenge", true) == 0)
		{
			iCvar_CurrentMode = 8;
		}
	}
	if (iCvar_CurrentMode > 0)
	{
		iCvar_CurrentMode = 0;
	}
	return 0;
}

public void:OnInfectedSpawnPost(entity)
{
	if (entity > 0)
	{
		if (IsValidEntity(entity))
		{
			return 0;
		}
	}
	return 0;
}

public void:OnMapStart()
{
	g_sprite = PrecacheModel("materials/sprites/laserbeam.vmt", false);
	PrecacheModel("models/infected/common_male_clown.mdl", true);
	PrecacheModel("models/infected/common_male_riot.mdl", true);
	PrecacheModel("models/infected/common_male_mud.mdl", true);
	PrecacheModel("models/infected/limbs/exploded_boomer.mdl.mdl", true);
	PrecacheModel("models/props_junk/gascan001a.mdl", true);
	PrecacheModel("models/props_junk/propanecanister001a.mdl", true);
	PrecacheModel("models/props/cs_militia/silo_01.mdl", true);
	PrecacheSound("physics/destruction/smash_cave_woodrockcollapse2.wav", false);
	PrecacheSound("player/hunter/hit/tackled_1.wav", false);
	new i;
	while (i < 3)
	{
		new var1 = i << 2 + 3684;
		PrecacheSound(var1 + var1, false);
		i++;
	}
	return 0;
}

public Action:OnPlayerRunCmd(client, &buttons, &impulse, Float:vel[3], Float:angles[3], &weapon, &_arg6, &_arg7, &_arg8, &_arg9, _arg10[2])
{
	if (!IsSurvivor(client))
	{
		return 0;
	}
	if (IsShouMangJiaoLuan)
	{
		new ent = GetPlayerWeaponSlot(client, 0);
		new var1;
		if (ent > 0 && IsValidEntity(ent) && GetEntProp(ent, 0, "m_bInReload", 1, 0))
		{
			SetEntPropFloat(client, 0, "m_flLaggedMovementValue", 0.3, 0);
		}
	}
	if (!g_BeDizzy)
	{
		return 0;
	}
	if (buttons & 2)
	{
		buttons = buttons & -3;
		buttons = buttons | 4;
		return 1;
	}
	if (buttons & 4)
	{
		buttons = buttons & -5;
		buttons = buttons | 2;
		return 1;
	}
	if (buttons & 8)
	{
		vel[0] = -200.0;
		return 1;
	}
	if (buttons & 16)
	{
		vel[0] = 200.0;
		return 1;
	}
	if (buttons & 512)
	{
		vel[0] + 4/* ERROR unknown load Binary */ = 1128792064;
		return 1;
	}
	if (buttons & 1024)
	{
		vel[0] + 4/* ERROR unknown load Binary */ = -1018691584;
		return 1;
	}
	return 0;
}

public void:OnPluginStart()
{
	HookEvent("round_start", 265, 1);
	HookEvent("player_death", 245, 1);
	HookEvent("jockey_ride", 239, 1);
	HookEvent("revive_success", 295, 1);
	HookEvent("mission_lost", 3, 1);
	HookEvent("player_hurt", 243, 0);
	HookEvent("player_incapacitated", 237, 1);
	HookEvent("player_shoved", 271, 1);
	HookEvent("player_now_it", 241, 1);
	return 0;
}

public Action:OnRoundStart(Handle:event, String:name[], bool:dontBroadcast)
{
	g_FriendLyFire = false;
	g_BeDizzy = false;
	g_FireInfected = false;
	IsTankControl = false;
	IsShovedDamage = false;
	IsFenShen = false;
	IsIncapKongju = false;
	IsFriendLyFireDeath = false;
	SetConVarInt(FindConVar("survivor_limp_health"), 40, false, false);
	SetConVarInt(FindConVar("sv_disable_glow_survivors"), 0, false, false);
	SetConVarInt(FindConVar("z_claw_hit_pitch_min"), 40, false, false);
	SetConVarInt(FindConVar("z_claw_hit_pitch_max"), -40, false, false);
	SetConVarInt(FindConVar("z_witch_always_kills"), 0, false, false);
	if (Timer_LeftStart != 0)
	{
		CloseHandle(Timer_LeftStart);
		Timer_LeftStart = 0;
	}
	if (Timer_SpecialEvents != 0)
	{
		CloseHandle(Timer_SpecialEvents);
		Timer_SpecialEvents = 0;
	}
	if (Timer_ThreePanic != 0)
	{
		CloseHandle(Timer_ThreePanic);
		Timer_ThreePanic = 0;
	}
	if (Timer_BeDizzy != 0)
	{
		CloseHandle(Timer_BeDizzy);
		Timer_BeDizzy = 0;
	}
	Timer_LeftStart = CreateTimer(0.1, 269, 0, 1);
	return 0;
}

public Action:OnTakeDamage(victim, &attacker, &inflictor, &Float:damage, &damagetype, &weapon, Float:damageForce[3], Float:damagePosition[3], damagecustom)
{
	new Float:damage_multiplier = 1.0;
	new var1;
	if (IsSurvivor(victim) && IsTank(attacker) && IsTankPunch)
	{
		damage_multiplier *= 2.0;
	}
	new var2;
	if (IsSurvivor(victim) && IsCommonInfected(attacker) && IsCommonPunch)
	{
		damage_multiplier *= 3.0;
	}
	new var3;
	if (IsSurvivor(victim) && IsSmoker(attacker) && IsSmokerPunch)
	{
		damage_multiplier *= 2.5;
	}
	new var4;
	if (IsSurvivor(victim) && IsCharger(attacker) && IsChargerDamage)
	{
		if (GetRandomInt(1, 100) <= 50)
		{
			damage_multiplier *= 2.0;
			EmitSoundToClient(attacker, "player/hunter/hit/tackled_1.wav", -2, 0, 75, 0, 1.0, 100, -1, NULL_VECTOR, NULL_VECTOR, true, 0.0);
		}
	}
	new var5;
	if (IsSurvivor(attacker) && IsTank(victim) && IsHardTank)
	{
		if (5<0(damage, 30))
		{
			damage = 1077936128;
		}
		return 1;
	}
	new var6;
	if (IsCommonInfected(attacker) && IsSurvivor(victim) && IsFenShen)
	{
		CheatCommand(victim, "z_spawn", "common");
	}
	new var7;
	if (g_FriendLyFire && IsSurvivor(attacker) && IsSurvivor(victim) && attacker != victim)
	{
		if (!attacker << 2 + 3124/* ERROR unknown load Binary */)
		{
			SDKHooks_TakeDamage(attacker, victim, attacker, 15.0, 33554432, -1, NULL_VECTOR, NULL_VECTOR);
			attacker << 2 + 3124/* ERROR unknown load Binary */ = 1;
			CreateTimer(0.3, 283, attacker, 0);
		}
	}
	new var8;
	if (IsMYZZ && IsInfected(attacker) && IsSurvivor(victim))
	{
		new String:WeaponUsed[256];
		if (IsValidEdict(weapon))
		{
			GetEdictClassname(weapon, WeaponUsed, 64);
		}
		else
		{
			if (IsValidEdict(inflictor))
			{
				GetEdictClassname(inflictor, WeaponUsed, 64);
			}
		}
		if (StrContains(WeaponUsed, "player", true) != -1)
		{
			new zombie_class = GetEntProp(attacker, 0, "m_zombieClass", 4, 0);
			new hasvictim = L4D2_GetSurvivorVictim(attacker);
			new var9;
			if (victim != hasvictim && zombie_class < 7)
			{
				CreateTimer(1.0, 281, GetClientUserId(victim), 2);
				CreateTimer(2.0, 281, GetClientUserId(victim), 2);
				CreateTimer(3.0, 281, GetClientUserId(victim), 2);
				CreateTimer(4.0, 281, GetClientUserId(victim), 2);
				CreateTimer(5.0, 281, GetClientUserId(victim), 2);
			}
		}
	}
	damage = damage * damage_multiplier;
	return 0;
}

public Action:PlayerLeftStart(Handle:Timer)
{
	if (LeftStartArea())
	{
		Timer_LeftStart = 0;
		new Float:rnd_timer = GetRandomFloat(160.0, 230.0);
		Timer_SpecialEvents = CreateTimer(rnd_timer, 277, 0, 0);
		return 4;
	}
	return 0;
}

public void:PlayerShoved(Event:event, String:name[], bool:dontBroadcast)
{
	new attacker = GetClientOfUserId(Event.GetInt(event, "attacker", 0));
	new victim = GetClientOfUserId(Event.GetInt(event, "userid", 0));
	new var1;
	if (IsShovedDamage && IsSurvivor(attacker) && IsPlayerAlive(attacker))
	{
		DealDamage(attacker, attacker, 2, 0, "");
	}
	new var2;
	if (IsHunter(victim) && IsSurvivor(attacker) && g_StealthSmoker)
	{
		L4D_StaggerPlayer(attacker, victim, NULL_VECTOR);
	}
	return 0;
}

public Action:RemoveInfecte(Handle:timer, any:data)
{
	new iMaxEntities = GetMaxEntities();
	new iEntity = MaxClients + 1;
	while (iEntity <= iMaxEntities)
	{
		if (IsWitch(iEntity))
		{
			new health = GetEntProp(iEntity, 1, "m_iHealth", 4, 0);
			if (health > 0)
			{
				DealDamage(iEntity, iEntity, health + 1, -**********, "");
			}
		}
		iEntity++;
	}
	return 0;
}

public bool:SetTempHealth(client, Float:fHp)
{
	new var1;
	if (IsClientConnected(client) && IsClientInGame(client))
	{
		SetEntPropFloat(client, 0, "m_healthBufferTime", GetGameTime(), 0);
		SetEntPropFloat(client, 0, "m_healthBuffer", fHp, 0);
		return 1;
	}
	return 0;
}

public Action:SpecialEvents(Handle:Timer)
{
	Timer_SpecialEvents = 0;
	ExecSpecialEvents();
	new Float:rnd_timer = GetRandomFloat(160.0, 230.0);
	Timer_SpecialEvents = CreateTimer(rnd_timer, 277, 0, 0);
	return 4;
}

public Action:ThreePanic_Stop(Handle:timer)
{
	UpdatePanicNums += 1;
	if (UpdatePanicNums > 3)
	{
		Timer_ThreePanic = 0;
		PrintToChatAll("持续1分钟的尸潮已停止!");
		return 4;
	}
	TriggerPanicEvent();
	return 0;
}

public Action:Time_damage(Handle:timer, any:userid)
{
	new Client = GetClientOfUserId(userid);
	if (IsSurvivor(Client))
	{
		if (IsIncaped(Client))
		{
			return 0;
		}
		new health = GetClientHealth(Client);
		new Float:temphealth = GetTempHealth(Client);
		if (5<0(temphealth, 2))
		{
			if (5==0(temphealth, 1))
			{
				SetTempHealth(Client, 5-0(temphealth, 1));
				if (GetClientHealth(Client) > 1)
				{
					SetEntityHealth(Client, health - 1);
				}
				else
				{
					SetIncapState(Client, 1);
				}
			}
			else
			{
				if (GetClientHealth(Client) > 2)
				{
					SetEntityHealth(Client, health - 1);
				}
				SetIncapState(Client, 1);
			}
		}
		else
		{
			SetTempHealth(Client, 5-0(temphealth, 1));
		}
	}
	return 0;
}

public Action:TimerCheckDamage(Handle:timer, any:attacker)
{
	attacker << 2 + 3124/* ERROR unknown load Binary */ = 0;
	return 0;
}

public Action:Timer_WenYiTank(Handle:timer)
{
	new i = 1;
	while (i <= MaxClients)
	{
		new var1;
		if (IsTank(i) && IsPlayerAlive(i))
		{
			new Float:pos[3] = 0.0;
			GetClientAbsOrigin(i, pos);
			new j = 1;
			while (j <= MaxClients)
			{
				new var2;
				if (IsSurvivor(j) && IsPlayerAlive(j) && !IsFakeClient(j))
				{
					new Float:vpos[3] = 0.0;
					GetClientAbsOrigin(j, vpos);
					new Float:dis = GetVectorDistance(pos, vpos, false);
					if (5<=0(dis, 160))
					{
						DealDamage(i, j, 5, 0, "");
					}
				}
				j++;
			}
		}
		i++;
	}
	return 0;
}

public Action:Timer_mortal(Handle:timer, client)
{
	new var1;
	if (client == 0 || !IsClientInGame(client))
	{
		return 0;
	}
	SetEntProp(client, 1, "m_takedamage", 2, 1, 0);
	return 0;
}

public bool:TraceRayDontHitSelfAndLive(entity, mask, any:data)
{
	if (data == entity)
	{
		return 0;
	}
	new var1;
	if (entity > 0 && entity <= MaxClients)
	{
		if (IsClientInGame(entity))
		{
			return 0;
		}
	}
	return 1;
}

public Action:UpdateRain(Handle:timer)
{
	UpdateRainNums += 1;
	if (UpdateRainNums > 10)
	{
		new Handle:zombie_player_l = FindConVar("z_max_player_zombies");
		SetConVarBounds(zombie_player_l, 0, true, 5*0(1.0, Old_ombie_player_l));
		SetConVarBounds(FindConVar("z_max_player_zombies"), 0, false, 0.0);
		new Handle:zombie_minion_l = FindConVar("z_minion_limit");
		SetConVarBounds(zombie_minion_l, 0, true, 5*0(1.0, Old_zombie_minion_l));
		SetConVarInt(FindConVar("z_boomer_limit"), Old_boomer_limit_l, false, false);
		return 4;
	}
	new Float:hitpos[3] = 0.0;
	new client = iGetRandomSurvivor();
	client = SelectAndidata(2, client, hitpos);
	if (client > 0)
	{
		new bot = CreateFakeClient("Infected Bot");
		if (bot != 0)
		{
			ChangeClientTeam(bot, 3);
			CreateTimer(0.1, 297, bot, 0);
		}
		new boomer = CreateSpecial(client, 2);
		if (boomer > 0)
		{
			SetGodMode(boomer, 5.0);
			TeleportEntity(boomer, hitpos, NULL_VECTOR, NULL_VECTOR);
		}
	}
	return 0;
}

public void:__ext_core_SetNTVOptional()
{
	MarkNativeAsOptional("GetFeatureStatus");
	MarkNativeAsOptional("RequireFeature");
	MarkNativeAsOptional("AddCommandListener");
	MarkNativeAsOptional("RemoveCommandListener");
	MarkNativeAsOptional("BfWriteBool");
	MarkNativeAsOptional("BfWriteByte");
	MarkNativeAsOptional("BfWriteChar");
	MarkNativeAsOptional("BfWriteShort");
	MarkNativeAsOptional("BfWriteWord");
	MarkNativeAsOptional("BfWriteNum");
	MarkNativeAsOptional("BfWriteFloat");
	MarkNativeAsOptional("BfWriteString");
	MarkNativeAsOptional("BfWriteEntity");
	MarkNativeAsOptional("BfWriteAngle");
	MarkNativeAsOptional("BfWriteCoord");
	MarkNativeAsOptional("BfWriteVecCoord");
	MarkNativeAsOptional("BfWriteVecNormal");
	MarkNativeAsOptional("BfWriteAngles");
	MarkNativeAsOptional("BfReadBool");
	MarkNativeAsOptional("BfReadByte");
	MarkNativeAsOptional("BfReadChar");
	MarkNativeAsOptional("BfReadShort");
	MarkNativeAsOptional("BfReadWord");
	MarkNativeAsOptional("BfReadNum");
	MarkNativeAsOptional("BfReadFloat");
	MarkNativeAsOptional("BfReadString");
	MarkNativeAsOptional("BfReadEntity");
	MarkNativeAsOptional("BfReadAngle");
	MarkNativeAsOptional("BfReadCoord");
	MarkNativeAsOptional("BfReadVecCoord");
	MarkNativeAsOptional("BfReadVecNormal");
	MarkNativeAsOptional("BfReadAngles");
	MarkNativeAsOptional("BfGetNumBytesLeft");
	MarkNativeAsOptional("BfWrite.WriteBool");
	MarkNativeAsOptional("BfWrite.WriteByte");
	MarkNativeAsOptional("BfWrite.WriteChar");
	MarkNativeAsOptional("BfWrite.WriteShort");
	MarkNativeAsOptional("BfWrite.WriteWord");
	MarkNativeAsOptional("BfWrite.WriteNum");
	MarkNativeAsOptional("BfWrite.WriteFloat");
	MarkNativeAsOptional("BfWrite.WriteString");
	MarkNativeAsOptional("BfWrite.WriteEntity");
	MarkNativeAsOptional("BfWrite.WriteAngle");
	MarkNativeAsOptional("BfWrite.WriteCoord");
	MarkNativeAsOptional("BfWrite.WriteVecCoord");
	MarkNativeAsOptional("BfWrite.WriteVecNormal");
	MarkNativeAsOptional("BfWrite.WriteAngles");
	MarkNativeAsOptional("BfRead.ReadBool");
	MarkNativeAsOptional("BfRead.ReadByte");
	MarkNativeAsOptional("BfRead.ReadChar");
	MarkNativeAsOptional("BfRead.ReadShort");
	MarkNativeAsOptional("BfRead.ReadWord");
	MarkNativeAsOptional("BfRead.ReadNum");
	MarkNativeAsOptional("BfRead.ReadFloat");
	MarkNativeAsOptional("BfRead.ReadString");
	MarkNativeAsOptional("BfRead.ReadEntity");
	MarkNativeAsOptional("BfRead.ReadAngle");
	MarkNativeAsOptional("BfRead.ReadCoord");
	MarkNativeAsOptional("BfRead.ReadVecCoord");
	MarkNativeAsOptional("BfRead.ReadVecNormal");
	MarkNativeAsOptional("BfRead.ReadAngles");
	MarkNativeAsOptional("BfRead.BytesLeft.get");
	MarkNativeAsOptional("PbReadInt");
	MarkNativeAsOptional("PbReadFloat");
	MarkNativeAsOptional("PbReadBool");
	MarkNativeAsOptional("PbReadString");
	MarkNativeAsOptional("PbReadColor");
	MarkNativeAsOptional("PbReadAngle");
	MarkNativeAsOptional("PbReadVector");
	MarkNativeAsOptional("PbReadVector2D");
	MarkNativeAsOptional("PbGetRepeatedFieldCount");
	MarkNativeAsOptional("PbSetInt");
	MarkNativeAsOptional("PbSetFloat");
	MarkNativeAsOptional("PbSetBool");
	MarkNativeAsOptional("PbSetString");
	MarkNativeAsOptional("PbSetColor");
	MarkNativeAsOptional("PbSetAngle");
	MarkNativeAsOptional("PbSetVector");
	MarkNativeAsOptional("PbSetVector2D");
	MarkNativeAsOptional("PbAddInt");
	MarkNativeAsOptional("PbAddFloat");
	MarkNativeAsOptional("PbAddBool");
	MarkNativeAsOptional("PbAddString");
	MarkNativeAsOptional("PbAddColor");
	MarkNativeAsOptional("PbAddAngle");
	MarkNativeAsOptional("PbAddVector");
	MarkNativeAsOptional("PbAddVector2D");
	MarkNativeAsOptional("PbRemoveRepeatedFieldValue");
	MarkNativeAsOptional("PbReadMessage");
	MarkNativeAsOptional("PbReadRepeatedMessage");
	MarkNativeAsOptional("PbAddMessage");
	MarkNativeAsOptional("Protobuf.ReadInt");
	MarkNativeAsOptional("Protobuf.ReadInt64");
	MarkNativeAsOptional("Protobuf.ReadFloat");
	MarkNativeAsOptional("Protobuf.ReadBool");
	MarkNativeAsOptional("Protobuf.ReadString");
	MarkNativeAsOptional("Protobuf.ReadColor");
	MarkNativeAsOptional("Protobuf.ReadAngle");
	MarkNativeAsOptional("Protobuf.ReadVector");
	MarkNativeAsOptional("Protobuf.ReadVector2D");
	MarkNativeAsOptional("Protobuf.GetRepeatedFieldCount");
	MarkNativeAsOptional("Protobuf.SetInt");
	MarkNativeAsOptional("Protobuf.SetInt64");
	MarkNativeAsOptional("Protobuf.SetFloat");
	MarkNativeAsOptional("Protobuf.SetBool");
	MarkNativeAsOptional("Protobuf.SetString");
	MarkNativeAsOptional("Protobuf.SetColor");
	MarkNativeAsOptional("Protobuf.SetAngle");
	MarkNativeAsOptional("Protobuf.SetVector");
	MarkNativeAsOptional("Protobuf.SetVector2D");
	MarkNativeAsOptional("Protobuf.AddInt");
	MarkNativeAsOptional("Protobuf.AddInt64");
	MarkNativeAsOptional("Protobuf.AddFloat");
	MarkNativeAsOptional("Protobuf.AddBool");
	MarkNativeAsOptional("Protobuf.AddString");
	MarkNativeAsOptional("Protobuf.AddColor");
	MarkNativeAsOptional("Protobuf.AddAngle");
	MarkNativeAsOptional("Protobuf.AddVector");
	MarkNativeAsOptional("Protobuf.AddVector2D");
	MarkNativeAsOptional("Protobuf.RemoveRepeatedFieldValue");
	MarkNativeAsOptional("Protobuf.ReadMessage");
	MarkNativeAsOptional("Protobuf.ReadRepeatedMessage");
	MarkNativeAsOptional("Protobuf.AddMessage");
	VerifyCoreVersion();
	return 0;
}

public void:eReviveSuccess(Event:event, String:name[], bool:dontBroadcast)
{
	new Reviver = GetClientOfUserId(Event.GetInt(event, "userid", 0));
	new var1;
	if (IsReviveDamage && IsSurvivor(Reviver) && IsPlayerAlive(Reviver))
	{
		DealDamage(Reviver, Reviver, 5, 0, "");
	}
	return 0;
}

public Action:kickbot(Handle:timer, any:Client)
{
	new var1;
	if (Client > 0 && IsClientInGame(Client) && IsFakeClient(Client))
	{
		KickClient(Client, "");
	}
	return 0;
}

public Action:tmrJump(Handle:timer, any:userid)
{
	DoJump(userid);
	return 0;
}

