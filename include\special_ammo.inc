#if defined _spacial_ammo_included_
  #endinput
#endif
#define _spacial_ammo_included_

/**
 * Give a special ammo to client.
 *
 * @param iClient		Client index given.
 * @param iAmmoType		1 - Incendiary, 2 - Bursting, 3 - Armor piercing.
 *
 * @error				Wrong number of arguments.
 * @noreturn
 */
native void AddSpecialAmmo(int iClient, int iAmmoType);

public SharedPlugin __pl_special_ammo =
{
	name = "special_ammo",
	file = "l4d_specialammo.smx",
	#if defined REQUIRE_PLUGIN
	required = 1,
	#else
	required = 0,
	#endif
};

#if !defined REQUIRE_PLUGIN
public void __pl_special_ammo_SetNTVOptional()
{
	MarkNativeAsOptional("AddSpecialAmmo");
}
#endif