#pragma semicolon 1
#pragma newdecls required

#include <sourcemod>
#include <sdktools>
#include <sdkhooks>

#define PLUGIN_VERSION "*******"
#define PLUGIN_NAME "L4D Special Ammo"


Handle AddUpgrade = INVALID_HANDLE;
Handle RemoveUpgrade = INVALID_HANDLE;

bool HasFieryAmmo[MAXPLAYERS+1];
bool HasHighdamageAmmo[MAXPLAYERS+1];
bool HasDumDumAmmo[MAXPLAYERS+1];

int SpecialAmmoUsed[MAXPLAYERS+1];
int killcount[MAXPLAYERS+1];

ConVar SpecialAmmoAmount;
ConVar KillCountLimitSetting;
ConVar DumDumForce;
ConVar CanLightTank;
ConVar AutoRecoilReducer;
ConVar g_CvarSurvUpgrades;
ConVar g_CvarEnabled;

UserMsg sayTextMsgId;

#define USE_EFFECTS 0

public Plugin myinfo = {
	name = PLUGIN_NAME,
	author = "AtomicStryker (Fork by Dragokas)",
	description = "Dish out major damage with special ammo types",
	version = PLUGIN_VERSION,
	url = "https://forums.alliedmods.net/showthread.php?t=98354"
}

/*
	Fork by Dragokas

	******* (25-Mar-2020)
	 - Added ability to disable particle effects
	
	******* (18-Dec-2019)
	 - Disabled firing from other weapon (like grenade) counting as speciall ammo.

	*******
	 - Additionally increased 33% of damage deal to infected with armor piercing ammo.

	*******
	 - Added translation into Russian
	 - Added API
	
	*******
	 - Removed coop mode restriction
	 - Converted to a new syntax and methodmaps
	 - Improved upgrades reset code
	 - Improved tank identify code
	 - Bum-Bum ammo renamed to "armor piercing ammo"
	 - Hollowpoints ammo renamed to "bursting ammo".
	
	TODO:
	- Add blood particles to "bursting ammo" fire effect.
	  from (DiscoBBQ, "L4D Gore").
	 
*/

public APLRes AskPluginLoad2(Handle myself, bool late, char[] error, int err_max)
{
	EngineVersion test = GetEngineVersion();
	if (test != Engine_Left4Dead && test != Engine_Left4Dead2)
	{
		strcopy(error, err_max, "Plugin only supports Left 4 Dead 1 and Left 4 Dead 2.");
		return APLRes_SilentFailure;
	}

	CreateNative("AddSpecialAmmo", NATIVE_AddSpecialAmmo);
	RegPluginLibrary("special_ammo");
	return APLRes_Success;
}

public void OnPluginStart()
{
	LoadTranslations("special_ammo.phrases");
	LoadTranslations("common.phrases"); // Needed for SDK Calls

	g_CvarSurvUpgrades = FindConVar("survivor_upgrades"); // in case the admin hasn't set this.
	
	HookEvent("infected_hurt", AnInfectedGotHurt);
	HookEvent("player_hurt", APlayerGotHurt);
	HookEvent("weapon_fire", WeaponFired);
	
	#if ( USE_EFFECTS )
	HookEvent("bullet_impact",BulletImpact);
	#endif
	
	HookEvent("infected_death", KillCountUpgrade);
	HookEvent("round_end", RoundHasEnded);
	HookEvent("map_transition", RoundHasEnded);
	HookEvent("mission_lost", RoundHasEnded);
	HookEvent("player_death", 			Event_PlayerDeath);
	HookEvent("player_bot_replace", 	Event_PlayerBotReplace, EventHookMode_Pre);
	HookEvent("player_disconnect", 		Event_PlayerDisconnect, EventHookMode_Pre);

	sayTextMsgId = GetUserMessageId("SayText");
	HookUserMessage(sayTextMsgId, SayCommandExecuted, true);
	
	CreateConVar("l4d_specialammo_version", PLUGIN_VERSION, " The version of L4D Special Ammo running ", FCVAR_DONTRECORD);
	g_CvarEnabled = CreateConVar("l4d_specialammo_enabled", "1", " Enable the plugin (0 - No, 1 - Yes) ", FCVAR_NOTIFY);
	SpecialAmmoAmount	= CreateConVar("l4d_specialammo_amount", "1000", " How much special ammo a player gets. (default 50) ", FCVAR_NOTIFY);
	CanLightTank = CreateConVar("l4d_specialammo_canlighttank", "0", " Does incendiary ammo set the Tank aflame? (default 0) ", FCVAR_NOTIFY);
	AutoRecoilReducer = CreateConVar("l4d_specialammo_recoilreduction", "1", " Does special ammo have less recoil? (default 1) ", FCVAR_NOTIFY);
	KillCountLimitSetting = CreateConVar("l4d_specialammo_killcountsetting", "30", " How much Infected a Player has to shoot to win special ammo. (default 120) ", FCVAR_NOTIFY);
	DumDumForce = CreateConVar("l4d_specialammo_dumdumforce", "75.0", " How powerful the DumDum Kickback is. (default 75.0) ", FCVAR_NOTIFY);
	
	RegAdminCmd("sm_givespecialammo", GiveSpecialAmmo, ADMFLAG_KICK, " sm_givespecialammo <1, 2 or 3> ");
	
	AutoExecConfig(true, "l4d_specialammo"); // an autoexec! ooooh shiny
	
	StartPrepSDKCall(SDKCall_Player);
	if (!PrepSDKCall_SetSignature(SDKLibrary_Server, "\xA1****\x83***\x57\x8B\xF9\x0F*****\x8B***\x56\x51\xE8****\x8B\xF0\x83\xC4\x04", 34))
	{
		PrepSDKCall_SetSignature(SDKLibrary_Server, "@_ZN13CTerrorPlayer10AddUpgradeE19SurvivorUpgradeType", 0);
	}
	PrepSDKCall_AddParameter(SDKType_PlainOldData, SDKPass_ByValue);
	AddUpgrade = EndPrepSDKCall();
	
	StartPrepSDKCall(SDKCall_Player);
	if (!PrepSDKCall_SetSignature(SDKLibrary_Server, "\x51\x53\x55\x8B***\x8B\xD9\x56\x8B\xCD\x83\xE1\x1F\xBE\x01\x00\x00\x00\x57\xD3\xE6\x8B\xFD\xC1\xFF\x05\x89***", 32))
	{
		PrepSDKCall_SetSignature(SDKLibrary_Server, "@_ZN13CTerrorPlayer13RemoveUpgradeE19SurvivorUpgradeType", 0);
	}
	PrepSDKCall_AddParameter(SDKType_PlainOldData, SDKPass_ByValue);
	RemoveUpgrade = EndPrepSDKCall();
}

public int NATIVE_AddSpecialAmmo(Handle plugin, int numParams)
{
	if(numParams < 2)
		ThrowNativeError(SP_ERROR_PARAM, "Invalid numParams");
	
	int iClient = GetNativeCell(1);
	int iAmmoType = GetNativeCell(2);
	
	vAddSpecialAmmo(iClient, iAmmoType);
	
	return 0;
}

public void OnMapStart()
{
	if(IsValidConVarChar(g_CvarSurvUpgrades))
	g_CvarSurvUpgrades.SetInt(1);
	
	#if ( USE_EFFECTS )
	/*
	static int i;
	static char sName[32];
	for (i = 1; i <= 8; i++) {
		Format(sName, sizeof(sName), "decals/blood%i", i);	// thanks to Lux
		if(!IsDecalPrecached(sName))
			PrecacheDecal(sName, true);
	}
	*/
	
	PrecacheEffect("ParticleEffect");
	
	//PrecacheGeneric("particles/blood_fx.pcf", true);
	//PrecacheGeneric("particles/boomer_fx.pcf", true);
	PrecacheGeneric("particles/fire_01.pcf", true);
	
	/*
	PrecacheParticleEffect("blood_impact_survivor_01");
	PrecacheParticleEffect("blood_impact_headshot_01c");
	PrecacheParticleEffect("blood_impact_infected_01_shotgun");
	PrecacheParticleEffect("blood_impact_tank_02");
	PrecacheParticleEffect("blood_impact_infected_01");
	PrecacheParticleEffect("boomer_explode_D");
	PrecacheParticleEffect("blood_impact_arterial_spray_cheap");
	PrecacheParticleEffect("blood_impact_tank_01_cheap");
	*/
	PrecacheParticleEffect("molotov_groundfire");
	#endif
}

public Action WeaponFired(Event event, const char[] ename, bool dontBroadcast)
{
	// get client and used weapon
	int client=GetClientOfUserId(event.GetInt("userid"));
	static char weapon[64];
	event.GetString("weapon", weapon, 64);
	
	if(client && (HasFieryAmmo[client]==true || HasHighdamageAmmo[client]==true || HasDumDumAmmo[client]==true)) // if client hasnt special ammo, we dont care
	{
		int iActiveWeapon = GetEntPropEnt(client, Prop_Data, "m_hActiveWeapon");
		int iWeapon1 = GetPlayerWeaponSlot(client, 0);
		int iWeapon2 = GetPlayerWeaponSlot(client, 1);
		
		if (iActiveWeapon != -1 && (iActiveWeapon == iWeapon1 || iActiveWeapon == iWeapon2))
		{
			if (StrContains(weapon, "shotgun", false)==-1)
			{
				SpecialAmmoUsed[client]++; // if not a shotgun, one round per shot.
			}
			else {
				SpecialAmmoUsed[client] += 5; // Five times the special rounds usage for shotguns.
			}
			
			int SpecialAmmoLeft = SpecialAmmoAmount.IntValue - SpecialAmmoUsed[client];
			if((SpecialAmmoLeft % 10) == 0 && SpecialAmmoLeft != 0) // Display a center HUD message every round decimal value of leftover ammo (30, 20, 10...)
				PrintCenterText(client, "%t: %d", "Elapsed", SpecialAmmoLeft); // "Special ammo rounds left: %d", SpecialAmmoLeft);
			
			if(SpecialAmmoUsed[client]>=SpecialAmmoAmount.IntValue) CreateTimer(0.3, OutOfAmmo, GetClientUserId(client), TIMER_FLAG_NO_MAPCHANGE); //to remove the toys
		}
	}
	return Plugin_Continue;
}

stock bool IsTank(int client)
{
	if( client > 0 && client <= MaxClients && IsClientInGame(client) && GetClientTeam(client) == 3 )
	{
		int class = GetEntProp(client, Prop_Send, "m_zombieClass");
		if( class == 5 )
			return true;
	}
	return false;
}

public Action APlayerGotHurt(Event event, const char[] name, bool dontBroadcast)
{
	int attacker = event.GetInt("attacker");
	if(attacker == 0) return Plugin_Continue; // if hit by a zombie or anything, we dont care
	
	int client = GetClientOfUserId(attacker);
	if (!HasFieryAmmo[client] && !HasDumDumAmmo[client]==true) return Plugin_Continue; //this function only handles special ammo
	if (GetClientTeam(client) != 2) return Plugin_Continue; //if for some reason a Zombie ends up with incendiary ammo LOL
	
	int InfClient = GetClientOfUserId(event.GetInt("userid"));
	if (GetClientTeam(InfClient) != 3) return Plugin_Continue; //no FF effects (or should we ;P )
	
	if (HasFieryAmmo[client])
	{
		bool bTank = IsTank(InfClient);
	
		if(bTank && CanLightTank.IntValue==0) return Plugin_Continue; //only burn Playertanks if the convar is 1
		
		int damagetype = event.GetInt("type");
		if(damagetype != 64 && damagetype != 128 && damagetype != 268435464)
		{
			IgniteEntity(InfClient, 120.0, false);
			if(bTank) 
			{
				float mSpeed = GetEntPropFloat(InfClient, Prop_Data, "m_flLaggedMovementValue");
				if ( mSpeed < 1.3 )
				{
					SetEntPropFloat(InfClient, Prop_Data, "m_flLaggedMovementValue", 1.3); // for a mad speed increase
				}
			}
		}
	}
	
	if (HasDumDumAmmo[client] && !IsUnderWater(InfClient))
	{
		float FiringAngles[3];
		float PushforceAngles[3];
		float force = DumDumForce.FloatValue;
		
		GetClientEyeAngles(client, FiringAngles);
		
		PushforceAngles[0] = Cosine(DegToRad(FiringAngles[1])) * force;
		PushforceAngles[1] = Sine(DegToRad(FiringAngles[1])) * force;
		PushforceAngles[2] = Sine(DegToRad(FiringAngles[0])) * force;
		
		float current[3];
		GetEntPropVector(InfClient, Prop_Data, "m_vecVelocity", current);
		
		float resulting[3];
		resulting[0] = current[0] + PushforceAngles[0];
		resulting[1] = current[1] + PushforceAngles[1];
		resulting[2] = current[2] + PushforceAngles[2];
		
		TeleportEntity(InfClient, NULL_VECTOR, NULL_VECTOR, resulting);
	}
	
	return Plugin_Continue;
}

public Action AnInfectedGotHurt(Event event, const char[] name, bool dontBroadcast)
{
	int client = GetClientOfUserId(event.GetInt("attacker"));
	if (!HasFieryAmmo[client] && !HasDumDumAmmo[client]==true) return Plugin_Continue; //this function only handles special ammo
	if (GetClientTeam(client) != 2) return Plugin_Continue; //if for some reason a Zombie ends up with incendiary ammo LOL
	
	int infectedentity = event.GetInt("entityid");
	
	if (HasFieryAmmo[client])
	{
		//char class_string[64];
		//GetEntityNetClass(infectedentity, class_string, 64); // witch has no client, so we cant use getclientmodel
		//if(strcmp(class_string, "Witch")==0) return Plugin_Continue; // no witch burning
		
		int damagetype = event.GetInt("type");
		if(damagetype != 64 && damagetype != 128 && damagetype != 268435464)
		{
			IgniteEntity(infectedentity, 120.0, false);
		}
	}
	
	if (HasDumDumAmmo[client])
	{
		float FiringAngles[3];
		float PushforceAngles[3];
		float force = DumDumForce.FloatValue;
		
		GetClientEyeAngles(client, FiringAngles);
		
		PushforceAngles[0] = Cosine(DegToRad(FiringAngles[1])) + force;
		PushforceAngles[1] = Sine(DegToRad(FiringAngles[1])) + force;
		PushforceAngles[2] = Sine(DegToRad(FiringAngles[0])) + force;
		
		float current[3];
		GetEntPropVector(infectedentity, Prop_Data, "m_vecVelocity", current);
		
		float resulting[3];
		resulting[0] = current[0] + PushforceAngles[0];
		resulting[1] = current[1] + PushforceAngles[1];
		resulting[2] = current[2] + PushforceAngles[2];
		
		TeleportEntity(infectedentity, NULL_VECTOR, NULL_VECTOR, resulting);
	}
	
	return Plugin_Continue;
}

public void OnClientPutInServer(int client)
{
	if (IsFakeClient(client))
		SDKHook(client, SDKHook_OnTakeDamage, OnTakeDamage);
}

public Action OnTakeDamage(int victim, int &attacker, int &inflictor, float &damage, int &damagetype, int &weapon, float damageForce[3], float damagePosition[3])
{
	if (attacker > 0 && attacker <= MaxClients && HasDumDumAmmo[attacker])
	{
		damage *= 1.2;
		return Plugin_Changed;
	}
	return Plugin_Continue;
}

public Action OutOfAmmo(Handle hTimer, int UserId)
{
	int client = GetClientOfUserId(UserId);
	
	if (client && IsClientInGame(client)) {
		if(HasFieryAmmo[client])
		{
			CPrintToChat(client, "%t", "Out_Of_Incendiary"); // "\x05You've run out of incendiary ammo.");
			HasFieryAmmo[client]=false;
			SDKCall(RemoveUpgrade, client, 19); // remove recoil dampener
		}
		if(HasHighdamageAmmo[client])
		{
			CPrintToChat(client, "%t", "Out_Of_Bursting"); // "\x05You've run out of bursting ammo.");
			SDKCall(RemoveUpgrade, client, 21);
			HasHighdamageAmmo[client]=false;
			SDKCall(RemoveUpgrade, client, 19); // remove recoil dampener
		}
		if(HasDumDumAmmo[client])
		{
			CPrintToChat(client, "%t", "Out_Of_Piercing"); // "\x05You've run out of armor piercing ammo.");
			HasDumDumAmmo[client]=false;
			SDKCall(RemoveUpgrade, client, 19); // remove recoil dampener
		}
	}
	SpecialAmmoUsed[client]=0;
	return Plugin_Continue;
}

public Action KillCountUpgrade(Event event, char[] ename, bool dontBroadcast)
{
	if (g_CvarEnabled.IntValue == 0)
		return Plugin_Continue;

	int client = GetClientOfUserId(event.GetInt("attacker"));
	bool minigun = event.GetBool("minigun");
	bool blast = event.GetBool("blast");
	
	if (client)
	{
		if (!minigun && !blast) killcount[client] += 1;
		
		if ((killcount[client] % 20) == 0) PrintCenterText(client, "%t: %d", "Killed", killcount[client]); // "Infected killed: %d", killcount[client]);
		
		if ((killcount[client] % KillCountLimitSetting.IntValue) == 0 && killcount[client] > 1)
		{
			if(IsClientInGame(client)==true && GetClientTeam(client)==2)
			{
				int luck = GetRandomInt(1,3); // wee randomness!!
				vAddSpecialAmmo(client, luck);
				static char name[64];
				GetClientName(client, name, 64);
				CPrintToChatAll("%t", "Won", name, GetAmmoName(luck), killcount[client]); // "\x04%s\x01 won %s for killing %d Infected!",name, ammotype, killcount[client]);
			}
		}
	}
	
	return Plugin_Continue;
}

public void BulletImpact(Event event, const char[] name, bool dontBroadcast)
{
	int client = GetClientOfUserId(event.GetInt("userid"));
	
	float Origin[3];	
	Origin[0] = event.GetFloat("x");
	Origin[1] = event.GetFloat("y");
	Origin[2] = event.GetFloat("z");
	
	if(HasHighdamageAmmo[client] || HasDumDumAmmo[client])
	{
		float Direction[3];
		Direction[0] = GetRandomFloat(-1.0, 1.0);
		Direction[1] = GetRandomFloat(-1.0, 1.0);
		Direction[2] = GetRandomFloat(-1.0, 1.0);
		
		TE_SetupSparks(Origin,Direction,1,3);
		TE_SendToAll();
	}
	
	if(HasFieryAmmo[client])
	{
		TE_SetupMuzzleFlash(Origin,Origin,1.5,1);
		TE_SendToAll();
		CreateParticleEffect(Origin, "molotov_groundfire", 1.0);
	}
}

public void OnMapEnd()
{
	ResetUpgrades();
}

public Action RoundHasEnded(Event event, const char[] name, bool dontBroadcast)
{
	ResetUpgrades();
	return Plugin_Continue;
}

void ResetUpgrades()
{
	// One Function to control them all, one function to find them,
	// one function to find them all and in the dark null reset them
	// in the land of Sourcepawn where the memoryleaks lie
	
	for(int i=1; i<=MaxClients; i++)
	{
		if (IsClientInGame(i) && GetClientTeam(i) == 2)
			ResetUpgrade(i);
	}
}

void ResetUpgrade(int client)
{
	HasFieryAmmo[client]=false;
	HasHighdamageAmmo[client]=false;
	HasDumDumAmmo[client]=false;
	killcount[client]=0;
	if(IsClientInGame(client))
	{
		SDKCall(RemoveUpgrade, client, 21);
		SDKCall(RemoveUpgrade, client, 19);
	}
}

public void Event_PlayerDeath(Event event, const char[] name, bool dontBroadcast)
{
	int client = GetClientOfUserId(event.GetInt("userid"));
	if(client != 0 && IsClientInGame(client) && GetClientTeam(client) == 2 && !IsFakeClient(client)) ResetUpgrade(client);
}

public void Event_PlayerBotReplace(Event event, const char[] name, bool dontBroadcast)
{
	int client = GetClientOfUserId(event.GetInt("player"));
	
	if (client && IsClientInGame(client) && GetClientTeam(client) == 2) {
		ResetUpgrade(client);
	}
}

public void Event_PlayerDisconnect(Event event, const char[] name, bool dontBroadcast)
{
	int client = GetClientOfUserId(event.GetInt("userid"));
	
	if (client && IsClientInGame(client) && GetClientTeam(client) == 2)
		ResetUpgrade(client);
}

public Action GiveSpecialAmmo(int client, int args)
{
	if (args < 1)
	{
		ReplyToCommand(client, "[SM] %t: sm_givespecialammo <1, 2 or 3>", "Usage"); // "[SM] Usage: sm_givespecialammo <1, 2 or 3>");
		return Plugin_Handled;
	}
	
	static char setting[10];
	GetCmdArg(1, setting, sizeof(setting));
	
	vAddSpecialAmmo(client, StringToInt(setting));
	
	static char name[64];
	GetClientName(client, name, 64);
	CPrintToChatAll("%t", "Cheat", name, GetAmmoName(StringToInt(setting))); // "\x04%s\x01 cheated himself some %s",name, ammotype);
	return Plugin_Handled;
}

char[] GetAmmoName(int iAmmoType)
{
	static char ammotype[64];
	
	switch(iAmmoType)
	{
		case 1: ammotype = "Incendiary";
		case 2: ammotype = "Bursting";
		case 3: ammotype = "Armor piercing";
	}
	return ammotype;
}

void vAddSpecialAmmo(int client, int iAmmoType)
{
	switch (iAmmoType)
	{
	case 1:
		{
			HasHighdamageAmmo[client] = false;
			HasFieryAmmo[client] = true;
			HasDumDumAmmo[client] = false;
			SpecialAmmoUsed[client]=0;
			if(AutoRecoilReducer.IntValue==1) SDKCall(AddUpgrade, client, 19); //add the Recoil dampener if demanded
		}
		
	case 2:
		{
			HasHighdamageAmmo[client] = true;
			SDKCall(AddUpgrade, client, 21); //add Hollowpoints
			HasFieryAmmo[client] = false;
			HasDumDumAmmo[client] = false;
			SpecialAmmoUsed[client]=0;
			if(AutoRecoilReducer.IntValue==1) SDKCall(AddUpgrade, client, 19); //add the Recoil dampener if demanded
		}
		
	case 3:
		{
			HasHighdamageAmmo[client] = false;
			HasFieryAmmo[client] = false;
			HasDumDumAmmo[client] = true;				
			SpecialAmmoUsed[client]=0;
			if(AutoRecoilReducer.IntValue==1) SDKCall(AddUpgrade, client, 19); //add the Recoil dampener if demanded
		}
	}
}

public Action SayCommandExecuted(UserMsg msg_id, Handle bf, const int[] players, int playersNum, bool reliable, bool init)
{
	static char containedtext[1024];
	BfReadByte(bf);
	BfReadByte(bf);
	BfReadString(bf, containedtext, 1024);
	
	if(StrContains(containedtext, "combat_sling")!= -1) return Plugin_Handled;

	if(StrContains(containedtext, "_expire")!= -1) return Plugin_Handled;

	if(StrContains(containedtext, "#L4D_Upgrade_")!=-1)
	{
		if(StrContains(containedtext, "description")!=-1)	return Plugin_Handled;
	}

	return Plugin_Continue;
}

public void CreateParticleEffect(float pos[3], char[] particlename, float time)
{
	int particle = CreateEntityByName("info_particle_system");
	if (IsValidEdict(particle))
	{
		TeleportEntity(particle, pos, NULL_VECTOR, NULL_VECTOR);
		
		DispatchKeyValue(particle, "effect_name", particlename);
		ActivateEntity(particle);
		AcceptEntityInput(particle, "Start");
		
		SetEntityKillTimer(particle, time);
	}
}

stock void PrecacheEffect(const char[] sEffectName) // thanks to Dr. Api
{
    static int table = INVALID_STRING_TABLE;
    
    if (table == INVALID_STRING_TABLE)
    {
        table = FindStringTable("EffectDispatch");
    }
    bool save = LockStringTables(false);
    AddToStringTable(table, sEffectName);
    LockStringTables(save);
}

stock void PrecacheParticleEffect(const char[] sEffectName) // thanks to Dr. Api
{
    static int table = INVALID_STRING_TABLE;
    
    if (table == INVALID_STRING_TABLE)
    {
        table = FindStringTable("ParticleEffectNames");
    }
    bool save = LockStringTables(false);
    AddToStringTable(table, sEffectName);
    LockStringTables(save);
}

stock void WriteBlood(int Ent, char[] ParticleName)
{
	static int Particle;
	static char tName[64];

	Particle = CreateEntityByName("info_particle_system");
	
	if(IsValidEdict(Particle))
	{
		static float Position[3], Angles[3];

		Angles[0] = GetRandomFloat(0.0, 360.0);
		Angles[1] = GetRandomFloat(-15.0, 15.0);
		Angles[2] = GetRandomFloat(-15.0, 15.0);

		GetEntPropVector(Ent, Prop_Send, "m_vecOrigin", Position);

		//Lower:
		// Position[2] += GetRandomFloat(5.0, 45.0);
		Position[2] += GetRandomFloat(5.0, 25.0);

		//Randomize:
		// Position[0] += GetRandomFloat(-15.0, 15.0);
		// Position[1] += GetRandomFloat(-15.0, 15.0);

		Position[0] += GetRandomFloat(-10.0, 10.0);
		Position[1] += GetRandomFloat(-10.0, 10.0);

		//Target Name:
		Format(tName, sizeof(tName), "Entity%d", Ent);
		DispatchKeyValue(Ent, "targetname", tName);
		GetEntPropString(Ent, Prop_Data, "m_iName", tName, sizeof(tName));

		//Properties:
		DispatchKeyValue(Particle, "targetname", "L4DParticle");
		DispatchKeyValue(Particle, "parentname", tName);
		DispatchKeyValue(Particle, "effect_name", ParticleName);

		DispatchKeyValueVector(Particle, "angles", Angles);
		DispatchKeyValueVector(Particle, "origin", Position);

		//Spawn:
		DispatchSpawn(Particle);

		//TeleportEntity(Particle, Position, Angles, NULL_VECTOR);

		//Parent:
		SetVariantString(tName);
		AcceptEntityInput(Particle, "SetParent", Ent);
		SetVariantString(tName);
		AcceptEntityInput(Particle, "SetParentAttachment", Ent);

		ActivateEntity(Particle);
		AcceptEntityInput(Particle, "Start");

		SetEntityKillTimer(Particle, 1.5);
	}
}

stock void SetEntityKillTimer(int entity, float fTimeout) 
{ 
	static char sCmd[32];
	Format(sCmd, sizeof(sCmd), "OnUser1 !self:Kill::%f:1", fTimeout);
	SetVariantString(sCmd);
	AcceptEntityInput(entity, "AddOutput");
	AcceptEntityInput(entity, "FireUser1");
}

stock void ReplaceColor(char[] message, int maxLen)
{
    ReplaceString(message, maxLen, "{white}", "\x01", false);
    ReplaceString(message, maxLen, "{cyan}", "\x03", false);
    ReplaceString(message, maxLen, "{orange}", "\x04", false);
    ReplaceString(message, maxLen, "{green}", "\x05", false);
}

stock void CPrintToChatAll(const char[] format, any ...)
{
    static char buffer[192];
    for( int i = 1; i <= MaxClients; i++ )
    {
        if( IsClientInGame(i) && !IsFakeClient(i) )
        {
            SetGlobalTransTarget(i);
            VFormat(buffer, sizeof(buffer), format, 2);
            ReplaceColor(buffer, sizeof(buffer));
            PrintToChat(i, "\x01%s", buffer);
        }
    }
}

stock void CPrintToChat(int iClient, const char[] format, any ...)
{
    static char buffer[192];
    SetGlobalTransTarget(iClient);
    VFormat(buffer, sizeof(buffer), format, 3);
    ReplaceColor(buffer, sizeof(buffer));
    PrintToChat(iClient, "\x01%s", buffer);
}

bool IsUnderWater(int client)
{
	return GetEntProp(client, Prop_Send, "m_nWaterLevel") == 3;
} 