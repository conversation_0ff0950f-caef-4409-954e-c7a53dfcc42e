1.140 (03-Dec-2023)
	- Added forwards "L4D_OnIncapacitated", "L4D_OnIncapacitated_Post" and "L4D_OnIncapacitated_PostHandled" to trigger when a Survivor is incapacitated. Requested by "<PERSON><PERSON>otter".
	- Fixed forwards "L4D2_OnPounceOrLeapStumble*" in L4D1 from throwing errors. Thanks to "HarryPotter" for reporting.
	- Increased the Spitter Acid bug fix volume. Thanks to "Marttt" and "KadabraZz" for reporting.

	- Updated: Plugin and test plugin.
	- Updated: "/scripting/gamedata/left4dhooks.l4d1.txt" GaneData file.
	- Updated: "/scripting/gamedata/left4dhooks.l4d2.txt" GaneData file.
	- Updated: "/scripting/include/left4dhooks.inc" Include file.
	- Updated: "/scripting/l4dd/l4dd_forwards.sp" project file.
	- Updated: "/scripting/l4dd/l4dd_natives.sp" project file.
	- Updated: "/scripting/l4dd/l4dd_setup.sp" project file.

1.139 (25-Oct-2023)
	- Added new attribute "L4D2IWA_WeaponType" for use with the "L4D2_GetIntWeaponAttribute" native. Thanks to "Forgetest" for providing.
	- Added new enum "WeaponType" for use with the "L4D2IWA_WeaponType" attribute. Thanks to "Forgetest" for providing.

	- Added forward "L4D_OnMotionControlledXY" which is called when staggering or other motion controlled animations. Requested by "Forgetest".
	- Added forward "L4D_OnDeathDroppedWeapons" to list weapon indexes dropped on player death. Requested by "Forgetest".
	- Added forwards "L4D2_GrenadeLauncher_Detonate", "L4D2_GrenadeLauncher_Detonate_Post" and "L4D2_GrenadeLauncher_Detonate_PostHandled" when a Grenade Launcher projectile detonates.

	- Added native "L4D_PrecacheParticle" to precache particle effects.
	- Added native "L4D_RemoveEntityDelay" to remove an entity after a specified amount of time.
	- Added native "L4D_WriteMemoryString" to write a string to a memory address.

	- Changed native "L4D_PipeBombPrj" to optionally create the PipeBomb fuse/light particles. Requested by "Eyal282".
	- Fixed native "L4D2_IsRealismMode" from returning values other than 0 and 1. Thanks to "Eyal282" for reporting.
	- Fixed forward "L4D2_OnFindScavengeItem*" to stop passing invalid entities. Thanks to "kochiurun119" for reporting.

1.138 (25-Sep-2023)
	- Fixed the "L4D2_CGasCan_EventKilled*" forwards not triggering when a gascan had not been picked up. Thanks to "Mystik Spiral" for reporting.

1.137 (23-Sep-2023)
	- Added forwards "L4D2_Infected_HitByVomitJar", "L4D2_Infected_HitByVomitJar_Post" and "L4D2_Infected_HitByVomitJar_PostHandled" to trigger when common infected are hit with a VomitJar. Requested by "Eyal282".
	- Incorporated the Spitter Acid no sound fix for when clients disconnected. Thanks to "Marttt" for reporting.
	- Now also flashes red like the game should when taking damage from this bugged entity. Thanks to "Marttt" for reporting.
	- Updated to the latest version of the "lux_library" include file. Thanks to "Lux" for reporting.

1.136 (05-Sep-2023)
	- Fixed the last update logging an error in L4D1. Thanks to "HarryPotter" for reporting.
	- Fixed forward "L4D_TankClaw_DoSwing_Post" having the wrong name and not activating. Thanks to "Forgetest" for reporting.

1.135 (18-Aug-2023)
	- Added a ton of natives for "PlayerAnimState" in L4D2 only. See the "methodmap PlayerAnimState" section of the "left4dhooks.inc" include file. Thanks to "Forgetest" for the code and "A1m`" for various stuff.
	- Added native "L4D_HasMapStarted" to check if game addresses have been loaded in OnMapStart. For use with some natives that require being called only after OnMapStart.
	- Added an example to the test plugin for use with "L4D_HasMapStarted" and natives that require waiting after OnMapStart. See the first OnMapStart section that is commented out.
	- Fixed forward "L4D2_OnPounceOrLeapStumble" detour to potentially fix rare random crashes. Thanks to "A1m`" and "Forgetest" for reporting.
	- Fixed stock "L4D2_SetEntityGlow" to use the newdecls. Thanks to "Dragokas" for reporting.
	- Fixed the test plugin examples for "L4D1_FirstAidKit_StartHealing" and "L4D2_BackpackItem_StartAction". Thanks to "Mystik Spiral" for reporting.

1.134 (10-Jul-2023)
	- Added "POINTER_SURVIVALMODE" enum for the "L4D_GetPointer" native to return the SurvivalMode pointer.
	- Added "L4D2CT_SurvivalSetupTimer" to the "L4D2CountdownTimer" enum for use with the "L4D2_CTimer*" natives.
	- Added stock "L4D_SetPlayerTempHealthFloat" to the "left4dhooks_stocks.inc" include file to set a players temporary health above 200 HP. Thanks to "Eyal282" for the code.
	- Added forwards "L4D_TankRock_BounceTouch", "L4D_TankRock_BounceTouch_Post" and "L4D_TankRock_BounceTouch_PostHandled" when a tank rock collides with something. Thanks to "Forgetest" for the code.
	- Added natives "L4D2_GetSurvivalStartTime" and "L4D2_SetSurvivalStartTime" to get and set the Survival start time. Requested by "Forgetest".
	- Added native "L4D2_SpawnAllScavengeItems" to spawn all Scavenge items. Thanks to "Eyal282" for some of the code and changes.
	- Added native "L4D2_Rematch" to restart a Versus game. Thanks to "Eyal282" for some of the code and changes.
	- Added native "L4D_EndVersusModeRound" to end the Versus round. Thanks to "Spirit_12" for some of the code and changes.
	- Fixed native "L4D2_Charger_StartCarryingVictim" removing teleporting and placement position bug. Thanks to "HarryPotter" for reporting.
	- Fixed L4D2 GameData offset for "L4D2_GetFirstSpawnClass" and "L4D2_SetFirstSpawnClass" natives. Thanks to "Forgetest" for reporting the new offset.
	- Errors about using natives before OnMapStart will now show the plugin causing it.

1.133 (19-Jun-2023)
	- Added details to some natives explaining they can only be used 1 frame after "OnMapStart" due to pointers being unavailable before. Thanks to "HarryPotter" for reporting.
	- Added an error log when certain natives are used before before "OnMapStart" instead of an ambiguous error about invalid offsets.
	- Added "POINTER_MISSIONINFO" enum for the "L4D_GetPointer" native to return the MissionInfo pointer.
	- Added native "L4D_FindUseEntity" to find a valid Use entity. This was added to find a valid target within the "L4D1_FirstAidKit_StartHealing" and "L4D2_BackpackItem_StartAction" pre hook forwards.
	- Added natives "L4D_ForceHunterVictim", "L4D_ForceSmokerVictim" and "L4D2_ForceJockeyVictim" to pin specific Special Infected on specific Survivors.
	- Added natives "L4D_Hunter_ReleaseVictim", "L4D_Smoker_ReleaseVictim" and "L4D2_Charger_EndCarry" to release pinned victims.
	- Changed forwards "L4D2_BackpackItem_StartAction", "L4D2_BackpackItem_StartAction_Post" and "L4D2_BackpackItem_StartAction_PostHandled" to include a weapon type param.
	- Changed native "L4D2_UseAdrenaline" adding an optional param to fire the related event. Also fixed max health to use the true value instead of hard coding to 100. Thanks to "Eyal282" for reporting.
	- Changed native "L4D_IsMissionFinalMap" adding an optional param to support custom maps with multiple finales to return true if the "env_outtro_stats" entity is detected but not technically the last map.
	- Changed native "L4D2_NavAreaBuildPath" to block being called before OnMapStart, to fix any related crashes while TheNavArea is not initialized. Thanks to "Mika Misori" for reporting.
	- Changed native "L4D_IsInLastCheckpoint" code, to prevent teleporting Survivors on some 3rd party maps. Thanks to "tRololo312312" for reporting.
	- Explanation: calling "TerrorNavMesh::GetLastCheckpoint" would otherwise teleport players to that finale position, for some reason.
	- Fixed native "L4D2Direct_GetSIClassDeathTimer" returning the timer for the wrong Special Infected and errors when checking Chargers. Thanks to "PencilMario" for reporting.

	- Updated "left4dhooks_lux_library" include file, fixing the "Terror_GetAdrenalineTime" stock to check for adrenaline being active. Thanks to "Lux" for changes and "Eyal282" for reporting.

1.132 (25-May-2023)
	- Added forwards "L4D1_FirstAidKit_StartHealing", "L4D1_FirstAidKit_StartHealing_Post" and "L4D1_FirstAidKit_StartHealing_PostHandled" to control first aid kit duration.
	- Added forwards "L4D2_BackpackItem_StartAction", "L4D2_BackpackItem_StartAction_Post" and "L4D2_BackpackItem_StartAction_PostHandled" to control first aid kit duration, defibrillator and other use durations.
	- Forwards requested by "Eyal282". Thanks to "Psyk0tik" for the detour functions and signatures.

	- Fixed various offsets from the from the 2.2.2.7 L4D2 update. Thanks to "sorallll" and "PaaNChaN" for reporting and "SirPlease" for updating.

1.131 (25-May-2023)
	- Fixed signatures broken from the 2.2.2.7 L4D2 update.
	- Thanks to "robex" and "XiLuo" for reporting changes.
	- Thanks to "A1m`" for updating various offsets.

	- Added stock "L4D_IsTankProp" in the "left4dhooks_silver.inc" file, to check if a prop can be hit by tanks. Thanks to "Eyal282" for adding.
	- Added forward "L4D2_OnChargerImpact" to trigger when a Charger impacts after charging. Requested by "Eyal282".
	- Added native "L4D_NavArea_IsConnected" to check if 2 areas are connected. Requested by "Eyal282".
	- Added native "L4D_GetNavAreaCenter" to get the center of a NavArea. Requested by "3ipKa".
	- Added native "L4D_SetPlayerSpawnTime" to set the spawn timer of a SI. Requested by "A1m`".

	- Fixed native "L4D_OnGameModeChange" in L4D2 returning 0 for realism. Thanks to "HarryPotter" for reporting.
	- Fixed forward "L4D2_CGasCan_EventKilled" not setting the correct modified values. Thanks to " 4NTEP xD " for reporting.

	- Fixed stocks "L4D2_GetWeaponUpgradeAmmoCount" and "L4D2_GetWeaponUpgrades" in the "left4dhooks_stocks" file, to return values other than 0 and 1. Thanks to "BHaType" for reporting.
	- Plugins using either of these stocks should be recompiled against the updated file.

	- Changes to the test plugin removing unnecessary lines printed to console when forwards trigger.

1.130 (01-May-2023)
	- Changed native "L4D2_NavAreaTravelDistance" to support L4D1. Thanks to "Eärendil" for reporting, and "Psyk0tik" for the signature.
	- Changed forward "L4D2_CGasCan_EventKilled" to an Action instead of Void forward to allow blocking detonation. Requested by "Mystik Spiral".
	- Added forwards "L4D2_CGasCan_EventKilled_Post" and "L4D2_CGasCan_EventKilled_PostHandled" to pair with the changed forward.
	- Added a check to validate the "CTerrorGameRules::GetMissionInfo" address. Thanks to "yezi" for reporting.

1.129 (31-Mar-2023)
	- Added native "L4D_WarpToValidPositionIfStuck" to teleport a stuck player. Requested by "ioioio".
	- Added forwards "L4D_OnTakeOverBot", "L4D_OnTakeOverBot_Post" and "L4D_OnTakeOverBot_PostHandled" that triggers when a client takes over a bot.
	- Updated "left4dhooks_lux_library" include file to version "0.5.7".

1.128 (06-Feb-2023)
	- Added forwards "L4D_OnDoAnimationEvent", "L4D_OnDoAnimationEvent_Post" and "L4D_OnDoAnimationEvent_PostHandled" to trigger on certain animation events. Requested by "Forgetest".
	- Added native "L4D_GetAllNavAreas" to return an ArrayList of all "TheNavAreas" addresses. Requested by "Marttt". Thanks to "HarryPotter" and "Forgetest" for help.
	- Added natives "L4D_GetNavAreaPos" and "L4D_GetNavAreaSize" to return the origin and size of a NavArea.
	- Added natives "L4D_GetNavAreaID" and "L4D_GetNavAreaByID" to return the ID from a NavArea address, and an address from a NavArea ID.
	- Added natives "L4D2_GetFirstSpawnClass" and "L4D2_SetFirstSpawnClass" to get and set the first Special Infected class that will spawn. L4D2 only. Thanks to "Forgetest" providing.
	- Added more stocks to the "left4dhooks_silver.inc" include file. Thanks to "knifeeeee" for providing.
	- Added enum "POINTER_THENAVAREAS" for the "L4D_GetPointer" native to return the "TheNavAreas" pointer.
	- Added enum list "PLAYERANIMEVENT_*" used by the "L4D2Direct_DoAnimationEvent" native and "L4D_OnDoAnimationEvent*" forwards. Thanks to "A1m`" for providing.
	- Changed native "L4D2Direct_DoAnimationEvent" to expose the variant param. Requested by "Forgetest".
	- Fixed natives "L4D_IsInFirstCheckpoint" and "L4D_IsPositionInFirstCheckpoint" sometimes incorrectly reporting false positives. Thanks to "Voevoda" for reporting.
	- Fixed forward "L4D2_OnEntityShoved_Post*" param "bIsHighPounce" to be more reliable, although it may still not report accurately. Thanks to "Forgetest" for changes.
	- Fixed forwards "L4D_OnSpawnSpecial_*", "L4D2_OnSpawnWitchBride_*" and "L4D_OnSpawnTank_Post*" to allow returning -1 as client index. Thanks to "Forgetest" for reporting.
	- Prevented forwards "L4D_OnTryOfferingTankBot*" from triggering when the client index was 0. Thanks to "Forgetest" for reporting.

1.127 (07-Jan-2023)
	- Added more Charger stocks to the "left4dhooks_silver.inc" include file. Thanks to "Forgetest" for providing.
	- Added some more "L4D2UseAction" enum values to the "left4dhooks_stocks.inc" include file. Thanks to "Marttt" for reporting.
	- Changed natives "L4D_IsPositionIn*" to use a wider search area to the nearest NavArea position, from 300.0 to 1000.0. Suggested by "ProjectSky".
	- Added missing post handled forwards to the include file. Thanks to "Forgetest" for reporting.
		- "L4D_OnStartMeleeSwing_PostHandled", "L4D2_OnChangeFinaleStage_PostHandled", "L4D2_CGasCan_ShouldStartAction_PostHandled",
		- "L4D2_OnHitByVomitJar_PostHandled", "L4D2_CGasCan_ActionComplete_PostHandled", "L4D2_OnJockeyRide_PostHandled" and "L4D2_OnStartCarryingVictim_PostHandled".
	- Added forward "L4D2_OnSlammedSurvivor_PostHandled" when the pre hook is being blocked. Requested by "Forgetest".
	- Changed forward "L4D2_OnSlammedSurvivor" to allow blocking the forward.
	- Changed forward "L4D_OnFatalFalling" to return client index 0 when the client is not available.
	- Fixed forward "L4D_OnFatalFalling" throwing errors when client index is 0. Thanks to "Beatles" for reporting.
	- Fixed forwards "L4D_TankRock_OnRelease" and "L4D_TankRock_OnRelease_Post" crashing on L4D1 Linux not giving a vecAng. Thanks to "Forgetest" for fixing.
	- Deprecated native: "L4D2_AreTeamsFlipped" in favor of using: GameRules_GetProp("m_bAreTeamsFlipped"); - Suggested by "A1m`".

1.126 (21-Dec-2022)
	- Added weapon attribute "L4D2IWA_DefaultSize" for the "L4D2_GetIntWeaponAttribute" and "L4D2_SetIntWeaponAttribute" natives to get a weapons default clip size. Thanks to "iaNanaNana" for adding.
	- Added natives "L4D_IsPositionInFirstCheckpoint" and "L4D_IsPositionInLastCheckpoint" to check if a given vector is within a checkpoint area. Thanks to "Forgetest" for the code.
	- Added native "L4D_GetEntityWorldSpaceCenter" to get an entities worldspace center. Requested by "A1m`".
	- Added forwards "L4D_OnCancelStagger", "L4D_OnCancelStagger_Post" and "L4D_OnCancelStagger_PostHandled" to notify when a players stagger is being cancelled. Requested by "Forgetest".
	- Fixed native "L4D_IsInLastCheckpoint" teleporting players in the finale. Thanks to "HarryPotter" for reporting.
	- L4D1: Fixed forward "L4D_OnReleaseSurvivorPositions" crashing Windows servers by fixing the GameData signature.

	- Changed natives: "L4D2Direct_GetNextShoveTime" and "L4D2Direct_SetNextShoveTime" method. Please report any issues.
	- Deprecated natives: "L4D2Direct_GetShovePenalty" and "L4D2Direct_SetShovePenalty" in favor of using the "m_iShovePenalty" Prop_Send GetEntProp/SetEntProp natives.

1.125 (07-Dec-2022)
	- Fixed update 1.124 breaking "L4D2_OnEndVersusModeRound". Thanks to "Forgetest" for reporting.
	- Fixed update 1.124 errors on Windows L4D1. Thanks to "HarryPotter" for reporting.
	- Fixed native "L4D_SetBecomeGhostAt" not working on Windows L4D1.

1.124 (07-Dec-2022)
	- Fixed natives "L4D2_SetIntMeleeAttribute" and "L4D2_GetIntMeleeAttribute" using Int32 instead of Int16. Thanks to "NoroHime" for reporting.
	- Fixed natives "L4D_IsInFirstCheckpoint" and "L4D_IsInLastCheckpoint" to use SDKCalls for accuracy instead of events. Thanks to "BHaType" for reporting.
	- Changed native "L4D_IsAnySurvivorInStartArea" to also use the "L4D_IsInFirstCheckpoint" method, since it often incorrectly reported false.

1.123 (03-Dec-2022)
	- Added native "L4D_SetBecomeGhostAt" to set when a dead Special Infected player will transition into ghost state. Requested by "sorallll".
	- L4D2: Fixed native "L4D_GetPlayerSpawnTime" on Windows reporting the wrong value.

1.122 (15-Nov-2022)
	- Added native "Left4DHooks_Version" to get the Left4DHooks plugin version.
	- Fixed native "L4D2_SpitterPrj" not playing any acid damage sounds if the attacker index was 0.
	- Fixed forwards "L4D_OnGetCrouchTopSpeed", "L4D_OnGetWalkTopSpeed" and "L4D_OnGetRunTopSpeed" from throwing client not in game errors. Thanks to "Eärendil" for reporting.

1.121 (06-Nov-2022)
	- Added native "L4D_AreAllSurvivorsInFinaleArea". Requested by "Eärendil".
	- Fixed native "L4D2_SpitterPrj" to not emit duplicate sounds from regular Spitter projectiles and playback at about the same volume as default.
	- L4D2: Added forwards "L4D2_OnSlammedSurvivor" and "L4D2_OnSlammedSurvivor_Post" when a Survivor is carried by a Charger and slammed into a wall. Thanks to "Forgetest" for adding.
	- L4D2: Added stocks "L4D2_GetQueuedPummelStartTime", "L4D2_IsInQueuedPummel", "L4D2_GetQueuedPummelAttacker" and "L4D2_GetQueuedPummelVictim" to the "left4dhooks_silver.inc" include file.
	- L4D1: Fixed trying to load the "IsVisibleToPlayer" signature. Thanks to "HarryPotter" for reporting.
	- Fixed stocks "IsUsingMinigun" and "StopUsingMinigun" throwing errors from the "left4dhooks_silver.inc" include file. Plugins must recompile to fix this error. Thanks to "HarryPotter" for reporting.

1.120 (20-Oct-2022)
	- Update for L4D2.

	- Added native "L4D2_CommandABot" to command a bots actions.
	- Added native "L4D2_GetScriptScope" to get an entities script ID. Thanks to "Forgetest" for help.
	- Added native "L4D2_GetVScriptEntity" to get a VScript "logic_script" entity index.
	- Added native "L4D2_IsVisibleToPlayer" to get a VScript "logic_script" entity index. Requested by "morzlee", thanks to "fdxx" and "Psyk0tik" for the code and gamedata.
	- Fixed native "ITimer_GetTimestamp" using the wrong stock and returning the wrong values. Thanks to "Forgetest" for reporting.
	- L4D2: Fixed forward "L4D2_OnGetScriptValueVector" not returning a vector value and not setting the value when changed.
	- Fixed forwards "L4D2_OnGetScriptValue*" not working after map change. Thanks to "Forgetest" for reporting.

1.119 (15-Oct-2022)
	- L4D2: Added a new pointer "POINTER_SCRIPTVM" to the "L4D_GetPointer" native.
	- L4D2: Added native "L4D2_GetDirectorScriptScope" to get the directors script scope.
	- L4D2: Added forwards "L4D2_OnGetScriptValueInt", "L4D2_OnGetScriptValueFloat", "L4D2_OnGetScriptValueVector" and "L4D2_OnGetScriptValueVoid" to use a new system of detecting script values. Thanks to "Forgetest" for coding.
	- L4D2: Added forward "L4D_OnForceSurvivorPositions_Pre" - pre relative to "L4D_OnForceSurvivorPositions" forward. Requested by "gabuch2".
	- L4D2: Changed native "L4D2_UseAdrenaline" to fire the event even when not healing. Thanks to "HarryPotter" for reporting.
	- L4D1 Linux: fixed forward "L4D_OnReleaseSurvivorPositions" using the wrong signature. Thanks to "HarryPotter" for reporting.
	- Added stocks "IsUsingMinigun" and "StopUsingMinigun" in the "left4dhooks_silver.inc" include file. Requested by "knifeeeee".
	- Added native "L4D_CleanupPlayerState" to clean up a players state. Requested by "knifeeeee".

1.118 (03-Oct-2022)
	- Fixed forward "L4D_OnReleaseSurvivorPositions" not being detected when used. Thanks to "gabuch2" for reporting.

1.117 (30-Sep-2022)
	- Added forward "L4D_OnReleaseSurvivorPositions" to trigger after round start when Survivors are released from starting positions, usually first map and finale escape start, not all maps. Requested by "gabuch2".
	- Added forwards "L4D_OnSpeakResponseConcept_Pre" and "L4D_OnSpeakResponseConcept_Post" to trigger when a talker response is triggered. Requested by "gabuch2".
	- Added some details for the "*_Detonate*" forwards in the "left4dhooks.inc" include file

1.116 (21-Sep-2022)
	- L4D1: Fixed GameData missing the "L4D_Molotov_Detonate" and "L4D_PipeBomb_Detonate" forwards data.
	- L4D2: Fixed native "L4D2_SpitterPrj" crashing the server when playing damage sounds. Thanks to "Marttt" for fixing.

1.115 (20-Sep-2022)
	- Added forward "L4D_OnForceSurvivorPositions" Triggers after round start when Survivors are forced into starting positions, usually first map and finale escape start, not all maps. Requested by "gabuch2".
	- Added forwards "L4D_Molotov_Detonate", "L4D_PipeBomb_Detonate" and "L4D2_VomitJar_Detonate" with their relative "_Post" and "_PostHandled" forwards to trigger when a grenade projectile is about to explode.
	- L4D2: Fixed native "L4D2_SpitterPrj" not causing damage sounds when the projectile is not connected with a player from the Special Infected team. Thanks to "Marttt" for reporting.
	- L4D2: Fixed native "L4D2_UseAdrenaline" to use the "adrenaline_health_buffer" cvar value instead of "pain_pills_health_value" for healing and prevent over-healing. Thanks to "HarryPotter" for fixing.
	- Fixed stock "L4D_StopReviveAction" from "left4dhooks_silver.inc" include file from throwing errors. Thanks to "Red Flame" for reporting.

1.114 (01-Sep-2022)
	- Added forwards "L4D_OnPlayerCough", "L4D_OnPlayerCough_Post" and "L4D_OnPlayerCough_PostHandled" when a player is coughing from a Smoker Cloud.
	- Added native "L4D_GoAwayFromKeyboard" to set a client as idle. Requested by "knifeeeee".
	- Added natives "L4D_GetNavArea_AttributeFlags" and "L4D_SetNavArea_AttributeFlags" to get and set a nav areas attribute flags.
	- Added natives "L4D_GetNavArea_SpawnAttributes" and "L4D_SetNavArea_SpawnAttributes" to get and set a nav areas spawn attribute flags.
	- Initial adding of native "L4D2_GetScriptValueString" to return the directors string key values. This crashes when the key has not been set.
	- Fixed forward "L4D_OnGetScriptValueString" throwing errors.

	- Added missing forwards "L4D2_CInsectSwarm_CanHarm_PostHandled" and "L4D2_OnSpawnWitchBride_PostHandled" to the include file.
	- Changes to some forwards information in the include file.
	- Changes to prevent errors throwing from some forwards in the test plugin.

1.113 (16-Aug-2022)
	- Fixed the natives "L4D_GetCheckpointFirst" and "L4D_GetCheckpointLast" not always finding the saferoom door. Thanks to "azureblue" for reporting.

1.112 (15-Aug-2022)
	- Added native "L4D2_GetScriptValueFloat" to return the directors float key values. See the left4dhooks.inc include file for details.
	- Changed forwards "L4D_OnGetScriptValueInt" to read and modify the post hook values.
	- Probably fixed forward "L4D_OnGetScriptValueString" not modifying the return value when changed.
	- Initial adding of forward "L4D_OnGetScriptValueVector" to return the directors vector key values. This is unused and commented out in code due to not being tested.
	- Thanks to "Forgetest" for help coding and testing.
	- Wildcarded the "SurvivorBot::UseHealingItems" signature to prevent conflicts with other plugins.

1.111 (30-Jul-2022)
	- Fixed native "L4D_GetNearestNavArea" additional params not being backwards compatible with plugins compiled against older versions of left4dhooks. Thanks to "Mr. Man" for reporting.

1.110 (15-Jul-2022)
	- Added L4D2 native "L4D2_NavAreaBuildPath" to replace the "L4D2_VScriptWrapper_NavAreaBuildPath" native VScript wrapper. Requested by "morzlee". Thanks to "Forgetest" for helping.
	- Changed native "L4D_GetNearestNavArea" to accept some more parameters. Suggested by "Forgetest".

	- Various minor code changes to adjust for splitting the project (cleaner comments and better error handling when compiling the split project files). Thanks to "Psyk0tik" for some help.

1.109 (05-Jul-2022)
	- Plugin now requires SourceMod 1.11. Utilizing some fixes from the new SourceMod and changed code to use new methodmaps.
	- Core changes to the source code splitting into multiple files for maintainability. Thanks to "Psyk0tik" for some suggestions.

	- Changed forward "L4D2_OnEntityShoved" client detection method. Thanks to "A1m`" for a solution.

	- Fixed forwards: "L4D_OnTryOfferingTankBot", "L4D_OnSpawnSpecial", "L4D_OnSpawnTank", "L4D_OnSpawnWitch", "L4D2_OnSpawnWitchBride", "L4D_PipeBombProjectile_Pre" and "L4D2_OnFindScavengeItem"
		to return -1 instead of 0 when blocking the functions. Thanks to "Forgetest" for reporting.

	- New project files located in "scripting/l4dd/"

1.108 (20-Jun-2022)
	- Added support for the "Updater" plugin by "GoD-Tony" to auto-update Left4DHooks. Can change the "ALLOW_UPDATER" define value for various options.
	- L4D2: Added native "L4D2_Jockey_EndRide" to stop a Jockey riding a Survivor. Requested by "Eyal282".

1.107 (08-Jun-2022)
	- Fixed memory leak when players disconnect. Thanks to "hefiwhfcds2" for reporting.
	- Fixed forwards "L4D_OnShovedBySurvivor*" from throwing "null pointer" errors. Thanks to "HarryPotter" for reporting.
	- Fixed native "L4D_TakeOverZombieBot" not always setting the correct zombie type in L4D2. Thanks to "a2121858" for fixing.
	- Fixed native "L4D2Direct_SetTankTickets" crashing servers. Requires SourceMod 1.11. Thanks to "a2121858" for reporting.
	- Potentially fixed other server crashes on map change when writing to memory. Requires SourceMod 1.11.

1.106 (02-Jun-2022)
	- Added stock "L4D_HasReachedSmoker" to return if a Survivor has reached the Smoker. Requested by "Nuki".
	- Plugin now warns if Left4DHooks is already running, to avoid duplicate plugins.
	- Reverted accidental debug logging from the last update. Thanks to "Hawkins" for reporting.

1.105 (01-Jun-2022)
	- L4D1: Fixed native "L4D2_GetTankCount" not reporting the correct value. Thanks to "Alexmy" for reporting.

1.104 (28-May-2022)
	- Fixed the new natives from the last update throwing errors in L4D1.

1.103 (28-May-2022)
	- L4D2: Added new natives:
		"L4D2_Charger_ThrowImpactedSurvivor" - To throw a Survivor like when they're flung by a Charger impacting nearby.
		"L4D2_Charger_StartCarryingVictim" - To carry a Survivor by a Charger.
		"L4D2_Charger_PummelVictim" - To pummel a specific Survivor by a Charger.
		"L4D2_Charger_EndPummel" - To end pummelling a Survivor by a Charger.

	- Various changes to the "left4dhooks.inc" include file. Thanks to "Vinillia" for fixing.
		- Added some missing params
		- Added some const qualifiers
		- Renamed some brief params
		- Changed some return types to any to avoid extra casting

	- When recompiling plugins changes maybe required to align with these fixes.

1.102 (16-May-2022)
	- Added various post hook forwards that will trigger even when the relative pre hook has been blocked with return Plugin_Handled. This is since the changes in Left4DHooks version 1.94.

	- Added new forwards:
		"L4D_OnSpawnSpecial_PostHandled", "L4D_OnSpawnTank_PostHandled", "L4D_OnSpawnWitch_PostHandled", "L4D_OnMobRushStart_PostHandled", "L4D_OnSpawnITMob_PostHandled", "L4D_OnSpawnMob_PostHandled",
		"L4D_OnEnterGhostState_PostHandled", "L4D_OnFirstSurvivorLeftSafeArea_PostHandled", "L4D_OnGetMissionVSBossSpawning_PostHandled", "L4D_TankClaw_OnPlayerHit_PostHandled", "L4D_OnTryOfferingTankBot_PostHandled",
		"L4D_OnCThrowActivate_PostHandled", "L4D2_OnEndVersusModeRound_PostHandled", "L4D_OnLedgeGrabbed_PostHandled", "L4D2_OnStagger_PostHandled", "L4D_OnShovedBySurvivor_PostHandled", "L4D2_OnEntityShoved_PostHandled",
		"L4D2_OnPounceOrLeapStumble_PostHandled", "L4D_OnKnockedDown_PostHandled", "L4D2_OnPummelVictim_PostHandled", "L4D2_OnThrowImpactedSurvivor_PostHandled", "L4D2_OnPlayerFling_PostHandled",
		"L4D_OnMaterializeFromGhost_PostHandled", "L4D_OnVomitedUpon_PostHandled", "L4D_PipeBombProjectile_PostHandled", "L4D_OnPouncedOnSurvivor_PostHandled", "L4D_OnGrabWithTongue_PostHandled".

1.101 (10-May-2022)
	- Added natives "L4D_GetCheckpointFirst" and "L4D_GetCheckpointLast" to get the first and last saferoom door entities.

1.100 (28-Apr-2022)
	- Fixed forward "L4D2_OnStagger_Post" throwing errors. Thanks to "Eyal282" for reporting.

1.99 (27-Apr-2022)
	- Fixed forward "L4D2_OnStagger_Post" not triggering. Thanks to "Eyal282" for reporting.

1.98 (27-Apr-2022)
	- Added new forward "L4D_OnSwingStart" to trigger when a Survivor shoves.
	- Added post hook forward "L4D_OnShovedBySurvivor_Post". Requested by "Eyal282".
	- Added post hook forwards: "L4D_TankRock_OnRelease_Post, "L4D_OnCThrowActivate_Post", "L4D_OnLedgeGrabbed_Post", "L4D2_OnEntityShoved_Post",
		"L4D_OnPouncedOnSurvivor_Post", "L4D_OnStartMeleeSwing_Post" and "L4D2_OnChangeFinaleStage_Post"

	- Fixed native "L4D2Direct_SetNextShoveTime" not working when setting the shove time earlier than the current value. Thanks to "Eyal282" for reporting and helping fix.

1.97 (15-Apr-2022)
	- Fixed removing animation detours in the same frame as the detour callback, crashing the server. Thanks to "Red Flame" and "fdxx" for reporting and helping.
	- Fixed not erasing all animation hooks on client disconnect. Thanks to "sorallll" for reporting and fixing.
	- Fixed not hooking pre hooks when only post forwards were used. Thanks to "Beatles" for reporting.

1.96 (11-Apr-2022)
	- Fixed error on client disconnect. Thanks to "sorallll" for reporting.

1.95 (10-Apr-2022)
	- Added stock "GetRandomClient" in the "left4dhooks_silver.inc" include file.
	- Added forward "L4D_OnWitchSetHarasser" to fire when a Witch has been startled. Requested by "ProjectSky".
	- Added post hook forward "L4D_OnTryOfferingTankBot_Post" to compliment it's related forward "L4D_OnTryOfferingTankBot".

	- Fixed animation hooks not removing the detour when no longer required.
	- Fixed animation hooks not cleaning up when a client disconnects.
	- Fixed animation hooks triggering on clients other than those specified. Thanks to "JoinedSenses", "nosoop" and "Impact" for helping.
	- Fixed forward "L4D2_OnPummelVictim" bugging out the victim when blocking the pummel.
	- Fixed some description errors in the "left4dhooks.inc" include file. Thanks to "Eyal282" for reporting.
	- Fixed a compile warning in 1.11 from the "left4dhooks_silver.inc" include file.

1.94 (29-Mar-2022)
	- Added natives "L4D_GetReserveAmmo" and "L4D_SetReserveAmmo" to get and set a players weapons reserve ammo.

	- Changed forward "L4D2_CGasCan_ShouldStartAction" swapping the nozzle and gascan params - to keep consistency with "L4D2_CGasCan_ActionComplete". Thanks to "Eyal282" for reporting.

	- Fixed forward "L4D2_OnPlayerFling" not firing. Thanks to "Forgetest" for reporting.

	- The follow forwards will no longer fire their post hook counterparts when blocked in the pre-hook (this allows using the post hooks to guarantee the forwards detoured function is invoked):
		"L4D2_OnHitByVomitJar", "L4D_OnVomitedUpon", "L4D2_CInsectSwarm_CanHarm", "L4D2_OnStartCarryingVictim", "L4D2_OnStartCarryingVictim", "L4D2_OnJockeyRide", "L4D_OnGrabWithTongue",
		"L4D2_CGasCan_ActionComplete", "L4D2_CGasCan_ShouldStartAction", "L4D_PipeBombProjectile_Pre", "L4D_OnMaterializeFromGhostPre", "L4D2_OnPlayerFling", "L4D2_OnThrowImpactedSurvivor",
		"L4D2_OnPummelVictim", "L4D_OnKnockedDown", "L4D2_OnPounceOrLeapStumble", "L4D2_OnEndVersusModeRound", "L4D_OnTryOfferingTankBot", "L4D_TankClaw_OnPlayerHit_Pre", "L4D_OnGetMissionVSBossSpawning",
		"L4D_OnEnterGhostState", "L4D_OnSpawnMob", "L4D_OnSpawnITMob", "L4D_OnMobRushStart", "L4D_OnFirstSurvivorLeftSafeArea", "L4D_OnSpawnWitch", "L4D2_OnSpawnWitchBride", "L4D_OnSpawnTank" and "L4D_OnSpawnSpecial".

1.93 (25-Mar-2022)
	- Fixed forward "L4D_OnVomitedUpon_Post" throwing errors.

1.92 (24-Mar-2022)
	- Fixed forward "L4D_OnSpawnSpecial_Post" firing when the client index is -1. Seems the code wasn't copied to the release.

1.91 (24-Mar-2022)
	- Added post hook forwards:
		"L4D2_CGasCan_ShouldStartAction_Post", "L4D2_CGasCan_ActionComplete_Post", "L4D2_CInsectSwarm_CanHarm_Post",
		"L4D_OnMobRushStart_Post", "L4D_OnSpawnITMob_Post", "L4D_OnSpawnMob_Post", "L4D2_OnStagger_Post", "L4D2_OnPounceOrLeapStumble_Post",
		"L4D_OnSetCampaignScores_Post", "L4D_OnRecalculateVersusScore_Post", "L4D_OnHasConfigurableDifficulty_Post",
		"L4D_OnVomitedUpon_Post", "L4D2_OnHitByVomitJar_Post", "L4D2_OnJockeyRide_Post" and "L4D2_OnStartCarryingVictim_Post".

	- Changed forward "L4D2_CGasCan_ShouldStartAction" params to include the nozzle entity.

	- Added some missing "MarkNativeAsOptional" lines to the include file.
	- Cleaned and tidied up some code.

	- Fixed forward "L4D_OnSpawnSpecial" firing when the client index is -1. Thanks to "Marttt" for reporting.
	- Fixed forward "L4D2_CGasCan_ShouldStartAction" crashing. Thanks to "Eyal282" for reporting.
	- Fixed native "L4D_GetCurrentChapter" sometimes reporting the wrong chapter number.

1.90 (20-Mar-2022)
	- Added forwards "L4D_OnKnockedDown" and "L4D_OnKnockedDown_Post" to trigger when a Survivor is being thrown by a Tank rock or Hunter lung.
	- Added forwards "L4D2_OnThrowImpactedSurvivor" and "L4D2_OnThrowImpactedSurvivor_Post" to trigger when a Survivor is impacted by a Charger.
	- Added forwards "L4D2_OnPummelVictim" and "L4D2_OnPummelVictim_Post" to trigger when a Survivor is about to be pummelled by a Charger.
	- Added native "L4D_EstimateFallingDamage" to check a players estimated falling damage. Requested by "Eyal282".
	- Added stocks "L4D_GetPinnedSurvivor" and "L4D2_IsMultiCharged" in the "left4dhooks_silver.inc" include file. Requested by "Eyal282".
	- Changed stock "L4D_IsPlayerStaggering" in the "left4dhooks_silver.inc" include file to add better thanks. Thanks to "HarryPotter" for modifying.
	- Changed forward "L4D_OnMaterializeFromGhost" from "Action" type to "void". Thanks to "Eyal282" for reporting.

1.89 (02-Mar-2022)
	- Fixed various @ targeting from only selecting 1 player. Thanks to "Eyal282" for reporting.
	- Changed stock "L4D_ForcePanicEvent" in the "left4dhooks_silver.inc" include file to fix breaking and in L4D2 trigger under more circumstances. Thanks to "Eyal282" for reporting.

1.88 (01-Mar-2022)
	- Added forward "L4D2_CGasCan_ShouldStartAction" (L4D2 only) to trigger when someone is about to pour a gascan. Requested by "Eyal282".
	- Added forward "L4D2_OnPlayerFling_Post" as a post hook to supplement the "L4D2_OnPlayerFling" forward. Requested by "Eyal282".

	- Added natives "L4D_ForceVersusStart", "L4D_ForceSurvivalStart" and "L4D2_ForceScavengeStart" (L4D2 only). Requested by "ProjectSky".
	- Thanks to "Lux" for some advice.

	- Added stock "L4D_GetClientTeam" in the "left4dhooks_stocks.inc" include file. Requested by Eyal282".
	- Added stock "L4D_IsPlayerStaggering" in the "left4dhooks_silver.inc" include file. Thanks to "HarryPotter" for writing.
	- Changed stock "L4D_ForcePanicEvent" in the "left4dhooks_silver.inc" include file to strip cheat flags when executing the command. Thanks to "Eyal282" for reporting.

	- Fixed stock "L4D_GetPinnedInfected" in the "left4dhooks_silver.inc" include file not returning a Charger carrying someone. Thanks to "Eyal282" for reporting.

	- Added enums "L4D1ZombieClassname" and "L4D2ZombieClassname" to the "left4dhooks_stocks.inc" include file to retrieve a classname from the relative "L4D1ZombieClassType" and "L4D2ZombieClassType" enums.

	- Added target filters "@blackwhite" and "@bw" to target people who are on their third strike (black and white - about to die). Requested by "eyal282".
	- Added target filters "@survivorbots" and "@sb" to target Survivor Bots. Requested by "LordVGames".
	- Added target filters "@infectedbots" and "@ib" to target Infected Bots. Requested by "LordVGames".

	- Changed "L4D2Direct_SetShovePenalty" and "L4D2Direct_SetNextShoveTime" to use SDKCalls instead of writing to memory.

	- Changes to potentially fix intermittent crashing on map change when using "CTimer_Set*", "ITimer_Set*", natives.
	- Thanks to "Forgetest" and "vikingo12" for reporting and possible solutions.
	- Requires re-compiling with SourceMod 1.11 to take affect.

	- Plugins using these stocks should recompile their plugin if the relative stock mentioned above has been updated.

1.87 (06-Feb-2022)
	- Added native "L4D_LobbyIsReserved" to return if players connected from the lobby and reserved the server.
	- Added natives "L4D_GetLobbyReservation" and "L4D_SetLobbyReservation" to get and set the lobby reservation ID.
	- Setting lobby reservation may not work when L4DToolZ is installed.

	- Added new weapon attribute. Requested by "vikingo12".
		- L4D2FloatWeaponAttributes: "L4D2FWA_ReloadDuration".

	- L4D2: now dynamically generates the "CTerrorGameRules::IsRealism" signature to future proof against updates breaking the signature.

1.86 (02-Feb-2022)
	- Added forward "L4D_OnServerHibernationUpdate" to report when server hibernation status changes. Requested by ProjectSky".
	- Added new weapon attribute. Requested by "A1m`".
		- L4D2FloatWeaponAttributes: "L4D2FWA_GainRange".

	- Fixed broken signatures in L4D1 and L4D2 due to game updates.

1.85 (10-Jan-2022)
	- Fixed "L4D2IWA_Bucket", "L4D2IWA_Tier", "L4D2FWA_VerticalPunch" and "L4D2FWA_HorizontalPunch" not reading offsets. Thanks to "DarklSide" for reporting.
	- Fixed native "AnimHookDisable" removing all hooks when multiple identical hook callbacks are added from a single plugin. Thanks to "Eärendil" for reporting.

1.84 (08-Jan-2022)
	- Fixed forward "L4D_OnTryOfferingTankBot" detour returning the wrong value when using "Plugin_Changed". Thanks to "Nuki" for reporting.
	- Fixed "Local_GetRandomClient" not processing alive or bots values correctly. Thanks to "Mrs cheng" for reporting.
	- Now using a random seed when using functions that randomly select players. Requested by "Mrs cheng".
	- More error messages report the OS, game and plugin version.

1.83 (25-Dec-2021)
	- Made error messages report the OS, game and plugin version.
	- Wildcarded the "CDirector::RestartScenarioFromVote" signature for L4D1. Thanks to "Beatles" or reporting.

1.82 (14-Dec-2021)
	- Added new weapon attributes. Thanks to "iaNanaNana" for requesting and giving offsets.
		- L4D2IntWeaponAttributes: "L4D2IWA_Bucket" (both games) and "L4D2IWA_Tier" (L4D2 only).
		- L4D2FloatWeaponAttributes: "L4D2FWA_VerticalPunch" and "L4D2FWA_HorizontalPunch".

1.81 (30-Nov-2021)
	- Fixed forward "L4D_OnVomitedUpon" sometimes throwing errors about null pointer. Thanks to "Krufftys Killers" for reporting.
	- Changed function "Local_GetRandomClient" in "left4dhooks_silver.inc" to be stock. Thanks to "A1m`" for reporting.

1.80 (24-Nov-2021)
	- Fixed forward "L4D_OnSpawnSpecial_Post" param error. Thanks to "fdxx" for reporting.

1.79 (23-Nov-2021)
	- Changed forward "L4D_OnSpawnSpecial_Post" prototype to remove the reference variable.

1.78 (23-Nov-2021)
	- Added forwards "L4D_OnSpawnSpecial_Post", "L4D_OnSpawnTank_Post", "L4D_OnSpawnWitch_Post" and "L4D2_OnSpawnWitchBride_Post". Requested by "A1m`".
	- Added stocks "GetAnyRandomClient", "GetRandomSurvivor" and "GetRandomInfected" to the "left4dhooks_silver.inc" include file.

1.77 (21-Nov-2021)
	- Fixed not detouring some functions when only 1 detour was used instead of both pre and post. Thanks to "fdxx" for reporting.

1.76 (20-Nov-2021)
	- Fixed native "L4D2_IsReachable" using a bad signature. Thanks to "tRololo312312" for reporting.
	- Wildcarded the "Music::Play" signatures to be compatible with plugins detouring. Thanks to "user2000", "Edison1318" and "Shadowysn" for reporting.
	- Wildcarded the "ZombieManager::SpawnTank" signatures to be compatible with plugins detouring. Thanks to "Tank Rush" for reporting.

	- L4D1 and L4D2 GameData files updated.

1.75 (18-Nov-2021)
	- Fixed forward "L4D2_OnHitByVomitJar" throwing errors about null pointer.
	- Forwards "L4D_OnVomitedUpon" and "L4D2_OnHitByVomitJar" attacker can now be reported as 0.

	- "left4dhooks_silver.inc" include:
		- Deprecated stock "L4D_IsPlayerIncapped".

	- "left4dhooks_stocks.inc" include:
		- Renamed the stock "L4D_HasAnySurvivorLeftSafeArea" to "L4D_HasAnySurvivorLeftSafeAreaStock" (the native "L4D_HasAnySurvivorLeftSafeArea" still exists).
		- Fixed compile errors on SourceMod version 1.11. Thanks to "user2000" for reporting.
		- Replaced indentation spaces with tabs.

	- Note: "#include <left4dhooks>" will load all <left4dhooks_*> includes. Other "#include <left4dhooks_*>" will attempt to load each other if included on their own.
	- Only required to use "#include <left4dhooks>" for all features and stocks availability.

	- Plugin and Include files updated.

1.74 (16-Nov-2021)
	- Fixed releasing 1.73 with the wrong "left4dhooks_stocks.inc" include file version. Thanks to "moschinovac" for reporting.

1.73 (15-Nov-2021)
	- Added tons of new stocks! About 150!

	- Added some new enums to the "left4dhooks_silver.inc" include file: "L4D_TEAM_*", "L4D_ZOMBIE_CLASS_*", "L4D2_ZOMBIE_CLASS_*" and "L4D_WEAPON_SLOT_*".
	- Added new include file "left4dhooks_lux_library.inc" - various new stocks from "lux_library.inc". Thanks to "Lux" for the original file and allowing use.
	- Added new include file "left4dhooks_silver.inc" - various new stocks. This will be updated over time to add new simple stock functions.
	- Added new include file "left4dhooks_stocks.inc" - various new stocks from "l4d_stocks.inc". Thanks to "Mr. Zero" for the original files.
	- This is also combined with the "l4d_weapon_stocks.inc" include file by "Mr. Zero".
	- Only required to copy to the "scripting/include" directory when compiling, "left4dhooks.inc" will include these automatically.

	- New GameData file "lux_library.txt" used for the "left4dhooks_lux_library.inc" include file. Thanks to "Lux" for providing these.

	- Added: New include files and GameData file.

1.72 (10-Nov-2021)
	- Added native "L4D_GetPointer" to return various pointer addresses.
	- Added native "L4D_GetClientFromAddress" to return a client index from a memory address.
	- Added native "L4D_GetEntityFromAddress" to return an entity index from a memory address.
	- Added native "L4D_ReadMemoryString" to read a string from a memory address.
	- Added native "L4D_GetServerOS" to return the current server OS.

1.71 (07-Nov-2021)
	- Fixed native "L4D2_GetSurvivorSetMod" not being restricted to L4D2. Thanks to "HarryPotter" for reporting.
	- Plugin now loads about 1 second faster and no longer creates about 1 second delay on map changes.
	- Changes to the "sm_l4dd_detours" and "sm_l4dhooks_detours" commands to prevent errors when using the DEBUG or DETOUR defines.

1.70 (07-Nov-2021)
	- Added native "L4D_TankRockPrj" to create a Tank Rock projectile.
	- Added native "L4D_DetonateProjectile" to detonate grenade projectiles.
	- Added natives to L4D2: "L4D2_GetSurvivorSetMap" and "L4D2_GetSurvivorSetMod" to return the maps and modified Survivor set.
	- Changed forwards "L4D_OnGetSurvivorSet" and "L4D_OnFastGetSurvivorSet" to post hooks to retrieve the correct value. Thanks to "Gabe Iggy" for reporting.
	- Fixed detours "OnShovedBySurvivor_Clone" and "OnStaggered_Clone" being broken on L4D1 linux. Thanks to "HarryPotter" for reporting.

	- GameData files, include file and plugins updated.

1.69a (04-Nov-2021)
	- Added missing forwards "L4D_OnPouncedOnSurvivor" and "L4D2_OnStartCarryingVictim" to the include file. Thanks to "ProjectSky" for reporting.

1.69 (03-Nov-2021)
	- Added forward "L4D_OnPouncedOnSurvivor" to notify when a Survivor is being pounced on by a Hunter.
	- Added forward "L4D2_OnStartCarryingVictim" to L4D2 to notify when a Survivor is being grabbed by a Charger.
	- Fixed some natives disabling the plugin if their signatures broke. Only their functionality will break.

	- GameData files, include file and plugins updated.

1.68 (02-Nov-2021)
	- Added forward "L4D_OnGrabWithTongue" to L4D2 to notify when someone is about to be grabbed by a Smoker Tongue. Requested by "Alexmy".
	- Added forward "L4D2_OnJockeyRide" to notify when someone is about to be ridden by a Jockey. Requested by "Alexmy".
	- Cleaned and consolidated the code: standardized gamedata names, function names and variable names.
	- Compatibility support for SourceMod 1.11. Fixed various warnings.

	- GameData files, include file and plugins updated.

1.67 (25-Oct-2021)
	- Fixed the create projectile natives from failing still when passing 0 entity index. Thanks to "BHaType" for reporting.
	- Fixed L4D1 Linux forward "TryOfferingTankBot" sometimes throwing errors. Thanks to "HarryPotter" for reporting.
	- Fixed L4D1 setting "L4D2FWA_PenetrationNumLayers" - float values will be rounded to ceiling. Thanks to "epzminion" for finding and "Psyk0tik" for reporting.
	- Fixed target filters "@isb" and "@isp" being flipped.

1.66 (21-Oct-2021)
	- Fixed L4D1 Linux not finding the "g_pWeaponInfoDatabase" signature. Thanks to "Ja-Forces" for reporting.
	- L4D1 GameData updated.

1.65 (20-Oct-2021)
	- Changed forward "L4D2_CGasCan_EventKilled" params to show the inflictor and attacker.
	- Thanks to "ProjectSky" for reminding me.

1.64 (20-Oct-2021)
	- Added 1 new forward to L4D1 and L4D2:
		- "L4D_CBreakableProp_Break" - When a physics prop is broken.

	- Added 3 new forwards to L4D2:
		- "L4D2_CGasCan_EventKilled" - When a GasCan is destroyed.
		- "L4D2_CGasCan_ActionComplete" - When a Survivor has finished pouring gas.
		- "L4D2_CInsectSwarm_CanHarm" - When Spitter Acid is checking if a player or entity can be damaged.

	- Added 1 new native to L4D1 and L4D2:
		- "L4D_GetWeaponID" - to get the Weapon ID by classname

	- Added and unlocked all the weapon attribute modification natives to L4D1:
	- Thanks to "Psyk0tik" for the suggestion and information about offsets.
		- "L4D2_IsValidWeapon"
		- "L4D2_GetFloatWeaponAttribute" and "L4D2_SetFloatWeaponAttribute"
		- "L4D2_GetIntWeaponAttribute" and "L4D2_SetIntWeaponAttribute"
		- "L4D2IntWeaponAttributes" enums - ("L4D2IWA_Bullets", "L4D2IWA_Damage", "L4D2IWA_ClipSize")
		- "L4D2FloatWeaponAttributes" enums - ("L4D2FWA_MaxPlayerSpeed", "L4D2FWA_SpreadPerShot", "L4D2FWA_MaxSpread", "L4D2FWA_Range", etc)

	- Added new target filters (requested by Tonblader):
		"@deads" - Dead Survivors (all, bots)
		"@deadsi" - Dead Special Infected (all, bots)
		"@deadsp" - Dead Survivors players (no bots)
		"@deadsip" - Dead Special Infected players (no bots)
		"@deadsb" - Dead Survivors bots (no players)
		"@deadsib" - Dead Special Infected bots (no players)
		"@sp" - Survivors players (no bots)
		"@sip" - Special Infected players (no bots)
		"@isb" - Incapped Survivor Only Bots
		"@isp" - Incapped Survivor Only Players

	- Changed target filter names (requested by Tonblader):
		"@incappedsurvivorbot" to "@rincappedsurvivorbot"
		"@isb" to "@risb"
		"@survivorbot" to "@rsurvivorbot"
		"@sb" to "@rsb"
		"@infectedbot" to "@rinfectedbot"
		"@ib" to "@rib"
		"@tankbot" to "@rtankbot"
		"@tb" to "@rtb"

	- Added "FINALE_*" enums to the include file for use with the "L4D2_ChangeFinaleStage" and "L4D2_GetCurrentFinaleStage" natives and "L4D2_OnChangeFinaleStage" forward.
	- Thanks to "Dragokas" for suggesting.

	- GameData files, include file and plugins updated.

1.63 (15-Oct-2021)
	- Changed all projectile natives to allow passing 0 (world) instead of a client index. Thanks to "BHaType" for reporting.
	- Changed forward "L4D_OnGameModeChange" from "Action" type to "void". Thanks to "Psyk0tik" for reporting.
	- Fixed commands "sm_l4dd_detours" and "sm_l4dhooks_detours" not showing all forwards when they have pre and post hooks.

	- Added 11 new forwards to L4D1 and L4D2. Thanks to "Psyk0tik" for the suggestions, signatures and detour functions.
		- "L4D_TankClaw_DoSwing_Pre" - When a tank is swinging to punch.
		- "L4D_TankClaw_DoSwing_Post" - When a tank is swinging to punch.
		- "L4D_TankClaw_GroundPound_Pre" - When an tank punches the ground.
		- "L4D_TankClaw_GroundPound_Post" - When an tank punches the ground.
		- "L4D_TankClaw_OnPlayerHit_Pre" - When a tank swings and punches a player.
		- "L4D_TankClaw_OnPlayerHit_Post" - When a tank swings and punches a player.
		- "L4D_TankRock_OnDetonate" - When a tank rock hits something.
		- "L4D_TankRock_OnRelease" - When a tank rock is thrown.
		- "L4D_PlayerExtinguish" - When a player is about to be extinguished.
		- "L4D_PipeBombProjectile_Pre" - When a PipeBomb projectile is being created.
		- "L4D_PipeBombProjectile_Post" - After a PipeBomb projectile is created.

	- Added 1 new forward to L4D2. Thanks to "Lux" for the suggestion, signature and detour functions.
		- "L4D2_MeleeGetDamageForVictim" - When calculating melee damage to inflict on something.

	- GameData files, include file and plugins updated.

1.62 (08-Oct-2021)
	- L4D1 Linux: Update thanks to "Forgetest" for writing.
	- L4D1 Linux: Fixed issues with the forwards "L4D_OnShovedBySurvivor" and "L4D2_OnStagger". Thanks "HarryPotter" for reporting.

	- L4D1 GameData file and plugin updated.

1.61 (05-Oct-2021)
	- Added natives "L4D_GetTempHealth" and "L4D_SetTempHealth" to handle Survivors temporary health buffer.
	- Added natives "L4D_PlayMusic" to play a specified music string to a client. Thanks to "DeathChaos25" and "Shadowysn" for "Dynamic Soundtrack Sets" plugin.
	- Added natives "L4D_StopMusic" to stop playing a specified music string to a client. Thanks to "DeathChaos25" and "Shadowysn" for "Dynamic Soundtrack Sets" plugin.
	- Moved the animation ACT_* enums from "include/left4dhooks.inc" to "include/left4dhooks_anim.inc". Suggested by "Accelerator". No plugin changes required.

	- Thanks to "Psyk0tik" for requesting the following forwards and natives and their signatures found here: https://github.com/Psykotikism/L4D1-2_Signatures

	- Added natives:
		- "L4D2_HasConfigurableDifficultySetting" - Returns if there is a configurable difficulty setting.
		- "L4D2_IsGenericCooperativeMode" - Returns if the current game mode is Coop/Realism mode.
		- "L4D_IsCoopMode" - Returns if the current game mode is Coop mode.
		- "L4D2_IsRealismMode" - Returns if the current game mode is Realism mode.
		- "L4D2_IsScavengeMode" - Returns if the current game mode is Scavenge mode.
		- "L4D_IsSurvivalMode" - Returns if the current game mode is Survival mode.
		- "L4D_IsVersusMode" - Returns if the current game mode is Versus mode.

	- Added forwards:
		- "L4D_OnFalling" - Called when a player is falling.
		- "L4D_OnFatalFalling" - Called when a player is falling in a fatal zone.
		- "L4D2_OnPlayerFling" - Called when a player is flung to the ground.
		- "L4D_OnEnterStasis" - Called when a Tank enters stasis mode in Versus mode.
		- "L4D_OnLeaveStasis" - Called when a Tank leaves stasis mode in Versus mode.

	- GameData files, include file and plugins updated.

1.60 (29-Sep-2021)
	- Added native "L4D2_GrenadeLauncherPrj" to create an activated Grenade Launcher projectile which detonates on impact. L4D2 only.
	- Fixed L4D1 Linux "CMolotovProjectile::Create" signature. Thanks to "Ja-Forces" for reporting.

1.59 (29-Sep-2021)
	- HotFix: Fix Linux not loading the last 2 natives.

1.58 (29-Sep-2021)
	- Added native "L4D_MolotovPrj" to create an activated Molotov projectile which detonates on impact.
	- Added native "L4D2_VomitJarPrj" to create an activated VomitJar projectile which detonates on impact. L4D2 only.
	- Added "STATE_*" enums to the include file for use with the "L4D_State_Transition" native. Thanks to "BHaType" for providing.
	- Fixed some incorrect information in the include file. Thanks to "jackz" for reporting.

	- GameData files, include file and plugins updated.

1.57 (18-Sep-2021)
	- Changed the method for getting the current GameMode. Should have no more issues. Thanks to "ddd123" for reporting.
	- L4D2: Wildcarded the "CTerrorPlayer::Fling" signature for compatibility with being detoured. Thanks to "ddd123" for reporting.

	- L4D2 GameData file and plugin updated.

1.56 (15-Sep-2021)
	- Fixed spawning an entity directly OnMapStart (can cause crashes), delayed by a frame to fix errors. Thanks to "fdxx" for reporting.

1.55 (12-Sep-2021)
	- Fixed native "L4D2Direct_TryOfferingTankBot" not working for L4D1 Linux due to the last update. Thanks to "Forgetest" for reporting.

1.54 (12-Sep-2021)
	- Big thanks to "Forgetest" and "HarryPotter" for helping fix and test this release.

	- Added forward "L4D_OnGameModeChange" to notify plugins when the mode has changed to Coop, Versus, Survival and Scavenge (L4D2).
	- Added native "L4D_GetGameModeType" to return if the current game mode is Coop, Versus, Survival or Scavenge (L4D2).

	- Update for L4D1:

	- Fixed on Linux forward "L4D_OnSpawnWitch" from not triggering for some Witch spawns. Thanks to "Forgetest" for fixing.
	- Fixed on Linux forward "L4D_OnTryOfferingTankBot" from not triggering on the first tank. Thanks to "Forgetest" for fixing.
	- Unlocked native "L4D2Direct_GetMobSpawnTimer" for usage in L4D1. Thanks to "HarryPotter" for reporting functionality.
	- Unlocked native "L4D2Direct_GetTankCount" for usage in L4D1. Missed this from the last update.

	- L4D1 GameData file, include file and plugins updated.

1.53 (09-Sep-2021)
	- Update for L4D1:

	- Added forward "L4D_OnRecalculateVersusScore" from "raziEiL"'s port of "L4D Direct".
	- Added natives "L4DDirect_GetSurvivorHealthBonus", "L4DDirect_SetSurvivorHealthBonus" and "L4DDirect_RecomputeTeamScores" from "raziEiL"'s port of "L4D Direct".
	- Changed native "L4D2_GetTankCount" to use the directors variable instead of counting entities. Thanks to "Forgetest" for the offsets.
	- Unblocked native "L4D_GetTeamScore" for usage in L4D1. Accepts logical_team params 1-6.
	- Fixed forward "L4D_OnFirstSurvivorLeftSafeArea" not blocking correctly. Thanks to "Forgetest" for the solution.
	- Various fixes and additions thanks to "HarryPotter" for requesting and testing.

	- L4D1 GameData file, include file and plugins updated.

1.52 (31-Aug-2021)
	- Added L4D1 and L4D2 specific "ACT_*" animation activity constants to the include file for usage in animation pre-hooks. See the include file for details.
	- Wildcarded "RestartScenarioFromVote" detour to be compatible with the "[L4D2] Restart Without Changelevel" plugin by "iaNanaNana".
	- Various minor changes to the code legibility.

1.51 (10-Aug-2021)
	- Added natives "L4D_GetCurrentChapter" and "L4D_GetMaxChapters" to get the current and max chapters count. Thanks to "Psyk0tik" for help.
	- L4D1: Added natives "L4D_GetVersusMaxCompletionScore" and "L4D_SetVersusMaxCompletionScore" to get/set Versus max score. Thanks to "BHaType" for offsets.
	- L4D1: Fixed broken "CThrowActivate" signature due to the ******* update. Thank to "matrixmark" for reporting.

	- GameData files, include file and plugins updated.

1.50 (22-Jul-2021)
	- Fixed "Native was not found" errors in L4D1. Thanks to "xerox8521" for reporting.
	- Test plugin: Fixed "L4D_OnMaterializeFromGhostPre" and "L4D_OnMaterializeFromGhost" throwing "String formatted incorrectly" errors.

1.49 (13-Jul-2021)
	- L4D2: Fixed the "SpawnTimer" offset being wrong. Thanks to "Forgetest" for reporting.

1.48 (13-Jul-2021)
	- Fixed "Param is not a pointer" in the "L4D_OnVomitedUpon" forward. Thanks to "ddd123" for reporting.
	- L4D2: Changed the way "ForceNextStage" address is read on Windows, hopefully future proof.

1.47 (10-Jul-2021)
	- Fixed "Trying to get value for null pointer" in the "L4D_OnVomitedUpon" forward. Thanks to "Shadowart" for reporting.

1.46 (09-Jul-2021)
	- L4D2: Added native "L4D2_ExecVScriptCode" to exec VScript code instead of having to create an entity to fire code.
	- L4D2: Fixed GameData file from the "*******" game update.

1.45 (04-Jul-2021)
	- Fixed bad description for "L4D_SetHumanSpec" and "L4D_TakeOverBot" in the Include file.
	- L4D1: Fixed forward "L4D_OnVomitedUpon" crashing. GameData file updated. Thanks to "Psyk0tik" for reporting.

1.44 (01-Jul-2021)
	- Fixed forward "L4D_OnMaterializeFromGhost" not firing. Thanks to "ProjectSky" for reporting.
	- Fixed changelog description. Thanks to "Spirit_12" for reporting.

1.43 (01-Jul-2021)
	- L4D1 & L4D2 update:
	- Added forward "L4D_OnMaterializeFromGhostPre" and "L4D_OnMaterializeFromGhost" when a client spawns out of ghost mode. Thanks to "ProjectSky" and "sorallll" and for suggesting.

	- Added native "L4D_RespawnPlayer" to respawn a dead player.
	- Added native "L4D_SetHumanSpec" to takeover a bot.
	- Added native "L4D_TakeOverBot" to takeover a bot.
	- Added native "L4D_CanBecomeGhost" to determine when someone is about to enter ghost mode.
	- Added native "L4D2_AreWanderersAllowed" to determine if Witches can wander.
	- Added native "L4D_IsFinaleEscapeInProgress" to determine when the rescue vehicle is leaving until.
	- Added native "L4D_GetLastKnownArea" to retrieve a clients last known nav area.

	- Added missing "ACT_ITEM2_VM_LOWERED_TO_IDLE" to the "data/left4dhooks.l4d2.cfg" config.

1.42 (23-Jun-2021)
	- L4D1 & L4D2 update:
	- Added forward "L4D_OnVomitedUpon" when client is covered in vomit.
	- Added forward "L4D_OnEnterGhostStatePre" with the ability to block entering ghost state.
	- Changed 2 signatures to be compatible with detouring: "CTerrorPlayer::OnStaggered" and "CTerrorPlayer::OnVomitedUpon".

	- L4D2 update only:

	- Added forward "L4D2_OnHitByVomitJar" when a Bilejar explodes on clients.
	- Added native "L4D2_NavAreaTravelDistance" to return the nav flow distance between two areas.
	- Added native "L4D2_UseAdrenaline" to give a player the Adrenaline effect and health benefits.

	- Added various natives as wrappers executing VScript code:
		- These are slower than native SDKCalls, please report popular ones to convert to fast SDKCalls.
		"L4D2_VScriptWrapper_GetMapNumber"
		"L4D2_VScriptWrapper_HasEverBeenInjured"
		"L4D2_VScriptWrapper_GetAliveDuration"
		"L4D2_VScriptWrapper_IsDead"
		"L4D2_VScriptWrapper_IsDying"
		"L4D2_VScriptWrapper_UseAdrenaline"
		"L4D2_VScriptWrapper_ReviveByDefib"
		"L4D2_VScriptWrapper_ReviveFromIncap"
		"L4D2_VScriptWrapper_GetSenseFlags"
		"L4D2_VScriptWrapper_NavAreaBuildPath"
		"L4D2_VScriptWrapper_NavAreaTravelDistance" // Added as a demonstration and test, SDKCall is available, use "L4D2_NavAreaTravelDistance" instead.

	- Thanks to "Eärendil" for showing me how to call some VScript functions.

1.41 (18-Jun-2021)
	- L4D2: Fixed "InvulnerabilityTimer" offset. Thanks to "Nuki" for helping.

1.40 (16-Jun-2021)
	- L4D2: Fixed various offsets breaking from "*******" game update. Thanks to "Nuki" for reporting and helping.

1.39 (16-Jun-2021)
	- Changed command "sm_l4dd_detours" results displayed to be read easier.
	- L4D2: Fixed signatures breaking from "*******" game update. Thanks to "Psyk0tik" for fixing.
	- L4D2: Fixed "VanillaModeOffset" in Linux breaking from "*******" game update. Thanks to "Accelerator74" for fixing.

1.38 (28-Apr-2021)
	- Changed native "L4D2_IsReachable" to allow using team 2 and team 4.

1.37 (20-Apr-2021)
	- Removed "RoundRespawn" being used, was for private testing, maybe a future native. Thanks to "Ja-Forces" for reporting.

1.36 (20-Apr-2021)
	- Added optional forward "AP_OnPluginUpdate" from "Autoreload Plugins" by "Dragokas", to rescan required detours when loaded plugins change.
	- Fixed native "L4D2Direct_GetFlowDistance" sometimes returning -9999.0 when invalid, now returns 0.0;
	- Fixed native "L4D_FindRandomSpot" from crashing Linux servers. Thanks to "Gold Fish" for reporting and fixing and "Marttt" for testing.
	- Restricted native "L4D2_IsReachable" client index to Survivor bots only. Attempts to find a valid bot otherwise it will throw an error. Thanks to "Forgetest" for reporting.
	- Signatures compatibility with plugins detouring them. L4D1: "OnLedgeGrabbed", "OnRevived" and L4D2: "OnLedgeGrabbed". Thanks to "Dragokas" for providing.

1.35 (10-Apr-2021)
	- Fixed native "L4D_GetTeamScore" error message when using the wrong team values. Thanks to "BHaType" for reporting.
	- Restricted native "L4D2_IsReachable" client index to bots only. Attempts to find a valid bot otherwise it will throw an error. Thanks to "Forgetest" for reporting.

1.34 (23-Mar-2021)
	- Added native "L4D_HasPlayerControlledZombies" to return if players can control infected. Thanks to "Spirit_12" for requesting.
	- Thanks to "Psyk0tik" for the L4D1 signature.
	- Fixed Linux detection accidentally being broken from version 1.17 update.

1.33a (04-Mar-2021)
	- L4D1 GameData updated. Changes fixing "L4D2_OnEntityShoved" were missing from the previous update.

1.33 (02-Mar-2021)
	- Changed forward "L4D2_OnEntityShoved" to trigger for all entities being shoved not just clients.
	- Fixed forward "L4D2_OnEntityShoved" not working in L4D1. GameData file updated for L4D1.
	- Fixed native "L4D_IsFirstMapInScenario" crashing in L4D1 from changes in version 1.30 update.

1.32 (23-Feb-2021)
	- Changed native "L4D_GetRandomPZSpawnPosition" to accept client index of 0. Thanks to "Accelerator74" for reporting.
	- Fixed target filters misspelling "incapped". Thanks to "Forgetest" for reporting.

1.31 (23-Feb-2021)
	- Added Target Filters to target randomly. Requested by "Tonblader":
		"@incappedsurvivors", "@is"
		"@randomincappedsurvivor", "@ris"
		"@randomsurvivor", "@rs"
		"@randominfected", "@ri"
		"@randomtank", "@rt"
		"@incappedsurvivorbot", "@isb"
		"@survivorbot", "@sb"
		"@infectedbot", "@ib"
		"@tankbot", "@tb"

	- Fixed "L4D_GetPlayerSpawnTime" from returning the wrong value again. Thanks to "Forgetest" for reporting.

1.30 (15-Feb-2021)
	- Fixed natives "L4D2_SetIntMeleeAttribute" and "L4D2_SetFloatMeleeAttribute" functions. Thanks to "bw4re" for reporting.
	- Fixed native "L4D_GetPlayerSpawnTime" giving the wrong time. Thanks to "Forgetest" for reporting.

	- Fixes by "Dragokas"
	- Fixed native "L4D_IsFirstMapInScenario" call with SDKCall_Raw returned error in SM 1.11, L4D1. Thanks to "Crasher" for reporting, and "Rostu" for help.
	- Fixed "ForceNextStage" signature (WIN).

1.29 (10-Oct-2020)
	- Fixed "L4D_StaggerPlayer" not working with NULL_VECTOR. Thanks to "Zippeli" for reporting.

1.28 (09-Oct-2020)
	- Added command "sm_l4dd_unreserve" to remove lobby reservation. Added for testing purposes but is functional.
	- Fixed L4D1 GameData failing to find "g_pServer" address. Thanks to "Ja-Forces" for reporting.
	- Fixed forward "L4D_OnFirstSurvivorLeftSafeArea" throwing errors about null pointer.

1.27 (05-Oct-2020)
	- Fixed not loading in L4D1 due to recent changes. Thanks to "TiTz" for reporting.

1.26 (01-Oct-2020)
	- L4D2: Fixed the new target filters not working correctly, now matches by modelname for Survivors instead of character netprop.

1.25 (01-Oct-2020)
	- Added survivor specific target filters: @nick, @rochelle, @coach, @ellis, @bill, @zoey, @francis, @louis
	- Added special infected target filters: @smokers, @boomers, @hunters, @spitters, @jockeys, @chargers
	- Changed native "L4D2_GetMeleeWeaponIndex" to return -1 instead of throwing an error, due to melee being unavailable.
	- Fixed melee weapon IDs being incorrect depending on which are enabled. Thanks to "iaNanaNana" for reporting.
	- Updated the "data/left4dhooks.l4d2.cfg" config with latest "ACT_*" animation numbers.

1.24 (27-Sep-2020)
	- Reverted change: native "L4D_GetTeamScore" now accepts values 1 and 2 again.
	- Changed natives:
		"L4D2Direct_GetVSTankFlowPercent", "L4D2Direct_SetVSTankFlowPercent", "L4D2Direct_GetVSTankToSpawnThisRound",
		"L4D2Direct_SetVSTankToSpawnThisRound", "L4D2Direct_GetVSWitchFlowPercent", "L4D2Direct_SetVSWitchFlowPercent",
		"L4D2Direct_GetVSWitchToSpawnThisRound" and "L4D2Direct_SetVSWitchToSpawnThisRound".
	- Corrected natives "roundNumber" to consider "m_bAreTeamsFlipped" and "m_bInSecondHalfOfRound".
	- Thanks to "devilesk" for native value clarification.

1.23 (27-Sep-2020)
	- Update by "ProdigySim" to fix Addons Eclipse. Thank you!

1.22 (24-Sep-2020)
	- Compatibility update for L4D2's "The Last Stand" update.
	- Big thanks to "ProdigySim" for help updating various gamedata offsets and signatures.
	- Added support for the 2 new melee weapons.
	- Changed native "L4D_GetTeamScore" to accept values 0 and 1 which seems is the standard.
	- Late loading with debug enabled now shows the g_pGameRules pointer.
	- Moved hard coded Addon Eclipse offsets to gamedata.

1.21 (01-Sep-2020)
	- Removed teleporting the old and new tank players when using "L4D_ReplaceTank" native.

1.20 (28-Aug-2020)
	- Changed forward "L4D_OnEnterGhostState" hook from pre to post hook. Thanks to "Forgetest" for reporting.
	- Fixed forward "L4D_OnShovedBySurvivor" client and target order being wrong. Thanks to "Forgetest" for reporting.

1.19 (27-Aug-2020)
	- Fixed native "L4D2Direct_TryOfferingTankBot" from crashing the server. Thanks to "disawar1" for reporting.

1.18 (20-Aug-2020)
	- Thanks to "Forgetest" for reporting the following issues and testing multiple fixes.
	- Fixed natives using "L4D2CT_VersusStartTimer" from reading the incorrect address.
	- Fixed native "L4D2_IsValidWeapon" returning false when the classname is missing "weapon_".
	- Fixed address "g_pGameRules" being incorrect after certain map or mode changes, this broke the following natives:
		"L4D2_GetVersusCompletionPlayer", "L4D_GetVersusMaxCompletionScore" and "L4D_SetVersusMaxCompletionScore".

	- Note: native "L4D2_IsValidWeapon" and various "*WeaponAttribute" natives still returns invalid for CSS weapons.
		This only happens on the servers first load until map is changed and the CSS weapons are precached using whichever method
		your server uses to enable CSS weapons. Plugins using or modifying CSS weapons might need to be updated with this in mind.

1.17 (20-Jul-2020)
	- Added native (L4D2 only): "L4D2_IsReachable" to check if a position is accessible to a Survivor Bot.
	- Fixed include native "L4D2_AreTeamsFlipped" returning an int instead of bool. Thanks to "BloodyBlade" for reporting.
	- Fixed native "L4D_GetHighestFlowSurvivor" throwing errors in 1.11. Thanks to "yuzumi" for reporting.
	- Removed some useless "view_as" code. Might remove more in the future.

1.16a (16-Jun-2020)
	- Fixed using the wrong offset for "m_PendingMobCount". Thanks to "fbef0102" for reporting.

1.16 (05-Jun-2020)
	- Added native "L4D_LobbyUnreserve" finally, to support "Remove Lobby Reservation (When Full)" plugin.
	- Huge thanks to "GAMMACASE" and "Deathreus" for helping figure why the native was crashing.

1.15 (15-May-2020)
	- Added a valid entity check for "L4D2_OnFindScavengeItem" due to so many plugins passing bad entities and throwing errors.
	- Fixed "L4D_Dissolve" native description in the include file. Thanks to "Psyk0tik" for reporting.

1.14 (10-May-2020)
	- Added native (L4D2 only): "L4D2Direct_GetScriptedEventManager" to return the scripted event manager pointer.
	- This native replicates "L4D2_GetCDirectorScriptedEventManager" used by other plugins.

	- Added 19 missing natives from L4D2Direct (L4D1 and L4D2):
	- "CTimer_Reset", "CTimer_Start", "CTimer_Invalidate", "CTimer_HasStarted", "CTimer_IsElapsed", "CTimer_GetElapsedTime", "CTimer_GetRemainingTime",
	- "CTimer_GetCountdownDuration", "ITimer_Reset", "ITimer_Start", "ITimer_Invalidate", "ITimer_HasStarted", "ITimer_GetElapsedTime",
	- "CTimer_GetDuration", "CTimer_SetDuration", "CTimer_GetTimestamp", "CTimer_SetTimestamp", "ITimer_GetTimestamp", "ITimer_SetTimestamp"

	- Fixed "L4D_OnTryOfferingTankBot" not returning a valid client index.
	- Thanks to "Mis" for requesting changes and reporting bugs.

1.13 (05-May-2020)
	- Added better error log message when gamedata file is missing.
	- Fixed "L4D2_OnEntityShoved" not detecting the last client. Thanks to "Addie" for reporting.
	- Made all natives optional from the include file. Thanks to "Psyk0tik" for requesting.
	- Optional natives can be set by plugins with "#undef REQUIRE_PLUGIN" before "#include <left4dhooks>" and "#define REQUIRE_PLUGIN" after.

1.12 (09-Apr-2020)
	- Added commands "sm_l4dd_detours" and "sm_l4dd_reload" as wrappers to "sm_l4dhooks_detours" and "sm_l4dhooks_reload".
	- Fixed command "sm_l4dhooks_detours" displaying the wrong forward, now also displays the plugin name using that forward.

1.11 (18-Mar-2020)
	- Added command "sm_l4dhooks_detours" to display which forwards are enabled.
	- Added missing natives: "L4D2Direct_GetPendingMobCount" and "L4D2Direct_SetPendingMobCount" from "raziEiL"'s port.
	- Fixed native "L4D2_GetVScriptOutput" using the provided buffer to execute code. Buffer now can be small to get small return values.
	- Optimized native "L4D2_GetVScriptOutput" to reuse the same entity for multiple calls in the same frame.
	- Maximum native "L4D2_GetVScriptOutput" code allowed seems to be 1006 characters.

1.10 (14-Mar-2020)
	- Added natives (L4D1 & L4D2): "L4D_IsAnySurvivorInStartArea", "L4D_IsInFirstCheckpoint" and "L4D_IsInLastCheckpoint".
	- Added native (L4D2 only): "L4D2_GetCurrentFinaleStage".
	- Thanks to "Nuki" for requesting.

	- Fixed missing "L4D2IWA_ClipSize" offset. Now this works: L4D2_SetIntWeaponAttribute("weapon_rifle", L4D2IWA_ClipSize, 100);
	- See include for details.

1.9 (10-Mar-2020)
	- Added native (L4D2 only): "L4D2_GetVScriptOutput" to execute VScript code and get return data.
	- This is modified from an example script I published on 29-Jun-2019: https://forums.alliedmods.net/showthread.php?t=317145

	- Added new natives (L4D2 only): "L4D2_ForceNextStage", "L4D2_IsTankInPlay" and "L4D2_GetFurthestSurvivorFlow"
	- Added new natives (L4D1 & L4D2): "L4D_HasAnySurvivorLeftSafeArea" and "L4D_IsAnySurvivorInCheckpoint".
	- See the "NATIVES - Silvers" section inside the include file for details.
	- Thanks to "Nuki" for requesting.

1.8 (08-Mar-2020)
	- Added AutoExecConfig to generate a cvars config saved to "cfgs/sourcemod/left4dhooks.cfg".
	- Loads signatures in "OnPluginStart" except "g_pGameRules" which can only load in "OnMapStart".
	- Thanks to "Accelerator74" for requesting and testing.
	- Fixed some wrong return types in the include file.

1.7 (04-Mar-2020)
	- Added natives "L4D_GetNearestNavArea" and "L4D_FindRandomSpot" to get a random spawn position.
	- Fixed native "L4D2Direct_GetFlowDistance" sometimes causing server crashes.
	- Fixed natives "L4D_IsFirstMapInScenario" and "L4D_IsMissionFinalMap" sometimes returning incorrect values. Thanks to "Accelerator74".

1.6 (02-Mar-2020)
	- Fixed the animation hook throwing an "Exception reported: Client is not connected" error.

	Thanks to "Accelerator74" for reporting:
	- Fixed Addons Disabler "l4d2_addons_eclipse" not working without any plugins using the forward to detour.
	- Fixed "L4D2Direct_GetVSWitchFlowPercent" and "L4D2Direct_SetVSWitchFlowPercent" natives.

1.5 (29-Feb-2020)
	- Added Director Variables to be rechecked:
	- Some of these only work in the Finale, some may only work outside of the Finale. L4D2 is weird.
		"SmokerLimit", "BoomerLimit", "HunterLimit", "SpitterLimit", "JockeyLimit", "ChargerLimit", "TankLimit",
		"DominatorLimit", "WitchLimit" and "CommonLimit".

		Challenge Mode variables, if required:
		"cm_MaxSpecials", "cm_BaseSpecialLimit", "cm_SmokerLimit", "cm_BoomerLimit", "cm_HunterLimit", "cm_SpitterLimit",
		"cm_JockeyLimit", "cm_ChargerLimit", "cm_TankLimit", "cm_DominatorLimit", "cm_WitchLimit" and "cm_CommonLimit".

	- Added Target Filters (thanks to "hoanganh810972" for reporting as missing):
		Survivors: "@s", "@surv", "@survivors"
		Specials:  "@i", "@infe", "@infected"
		Tanks:     "@t", "@tank", "@tanks"

	- Fixed native "L4D_CreateRescuableSurvivors" from not working. Now spawns all dead survivors into rescuable rooms.
	- Removed "L4D_OnGetRandomPZSpawnPosition" forward due to spawning specials at 0,0,0 when modifying any value.

1.4 (28-Feb-2020)
	- AnimHooks no longer affect the same client index if the previous user disconnected and someone else connected.
	- Clarified AnimHooks details in the include file. All AnimHooks are removed on map change.
	- Fixed L4D1: "Invalid Handle" errors caused by Director Variables fix. Thanks to "TiTz" for reporting.

1.3 (27-Feb-2020)
	- Added forward "L4D_OnGetRandomPZSpawnPosition" to display when the game selects a position.
	- Added native "L4D_GetRandomPZSpawnPosition" to select a random position.
	- Thanks to "Accelerator74" for requesting.

	- Added forward "L4D2_OnSelectTankAttackPre" to handle "ACT_*" activity numbers.
	- Changed "L4D2_OnSelectTankAttack" to use "m_nSequence" numbers instead, just like the extension did.
	- Changed the "AnimHook" functions to use "ACT_*" activity numbers (pre-hook) and "m_nSequence" animation number (post-hook).
	- Existing plugins using "L4D2_OnSelectTankAttack" no longer need to change anything.
	- Existing plugins with the new "AnimHook" hook can now use normal model "m_nSequence" sequence numbers in the post hook.
	- Thanks to "Accelerator74" for reporting the fix.

1.2 (27-Feb-2020)
	- Wildcarded the following signatures to be compatible with 3rd party plugin detours:
	- L4D2: "ChooseVictim", "GetSurvivorSet" and "ChangeFinaleStage".
	- Thanks to "xZk" for reporting.

1.1 (27-Feb-2020)
	- Added 26 new natives to L4D2 and 15 to L4D1 from "l4d2addresses.txt". Thanks to "Nuki" for suggesting.
	- See the "NATIVES - l4d2addresses.txt" section inside the include file for details.

	- Added 7 new natives to L4D2 and 5 to L4D1 from my plugins.
	- See the "NATIVES - Silvers" section inside the include file for details.

	- Fixed "L4D2_OnEndVersusModeRound" forwards triggering more than once per round. Thanks to "spumer" for reporting.
	- Fixed creating forwards and natives in the wrong place. Thanks to "Accelerator74" for reporting.
	- Fixed some signatures failing when other plugins detour them. Thanks to "hoanganh810972" for reporting.
	- Fixed cvar "l4d2_addons_eclipse" - values 0 and 1 now disable/enable addons unless otherwise handled.
	- Fixed not forwarding some Director Variables that were initialized too early. Thanks to "hoanganh81097" for reporting.
	- Thanks to "Spirit_12" for ideas with the Director Variables fix.
	- Removed unused actions from some forwards.

1.0 (24-Feb-2020)
	- Initial release.